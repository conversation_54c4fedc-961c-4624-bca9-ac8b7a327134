# main.py
import logging
import asyncio
from telegram import Update
from telegram.constants import ParseMode
from telegram.ext import (
    <PERSON><PERSON><PERSON>er,
    CommandHandler,
    MessageHandler,
    filters,
    ConversationHandler,
    CallbackQueryHandler,
    Defaults,
    ContextTypes,
)
import nest_asyncio

# Import configuration
from config.settings import BOT_TOKEN, LOG_LEVEL, LOG_FORMAT
from config.constants import *

# Import error logging
from utils.error_logger import log_error, log_callback_error, log_message_error, log_callback_interaction

# Import database
from database import init_database

# Import handlers
from handlers.start import StartHandler
from handlers.search.mobile import MobileSearchHandler
from handlers.search.aadhar import AadharSearchHandler
from handlers.search.alternate import AlternateSearchHandler
from handlers.search.vehicle import VehicleSearchHandler
from handlers.admin.panel import AdminPanelHandler

# Import services
from services.auth import AuthService
from ui.messages import MessageTemplates

nest_asyncio.apply()

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL.upper()),
    format=LOG_FORMAT
)
logger = logging.getLogger(__name__)

class TelegramBot:
    """Main bot class"""
    
    def __init__(self):
        self.auth_service = AuthService()
        self.message_templates = MessageTemplates()
        
        # Initialize handlers
        self.start_handler = StartHandler()
        self.mobile_handler = MobileSearchHandler()
        self.aadhar_handler = AadharSearchHandler()
        self.alternate_handler = AlternateSearchHandler()
        self.vehicle_handler = VehicleSearchHandler()
        self.admin_handler = AdminPanelHandler()
    
    async def fallback_callback_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle orphaned callback queries with conversation state recovery"""
        query = update.callback_query
        data = query.data

        # Log the unhandled callback
        log_callback_interaction(update, "FALLBACK_CALLBACK", success=False,
                                details=f"Unhandled callback: {data}")

        # Initialize conversation state recovery
        user_id = update.effective_user.id
        context.user_data["user_id"] = user_id
        context.user_data["conversation_recovered"] = True

        logger.info(f"Recovering conversation state for user {user_id} with callback {data}")

        # Instead of showing restart message, route the callback properly
        try:
            # Route the callback to the appropriate handler
            if data.startswith("admin_"):
                log_callback_interaction(update, "FALLBACK_TO_ADMIN", success=True, details=f"Routing {data} to admin handler")
                context.user_data["conversation_state"] = "ADMIN_PANEL"
                return await self.admin_handler.admin_callback_handler(update, context)
            else:
                log_callback_interaction(update, "FALLBACK_TO_MENU", success=True, details=f"Routing {data} to menu handler")
                context.user_data["conversation_state"] = "MENU"
                return await self.start_handler.menu_callback_handler(update, context)

        except Exception as routing_error:
            log_callback_error(update, context, routing_error, "fallback_routing")

            # If routing fails, show main menu
            try:
                await query.answer("Redirecting to main menu...")
                context.user_data["conversation_state"] = "MENU"
                await self.start_handler.show_main_menu(update, context)
                log_callback_interaction(update, "FALLBACK_TO_MAIN_MENU", success=True)
            except Exception as main_menu_error:
                log_callback_error(update, context, main_menu_error, "fallback_to_main_menu")
                await query.answer("Please use /start to restart the bot")

    async def debug_message_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Universal message handler for state recovery after bot restart"""
        user_id = update.effective_user.id
        message_text = update.message.text

        logger.info(f"UNIVERSAL: Processing message '{message_text}' from user {user_id}")
        logger.info(f"UNIVERSAL: Context user_data: {context.user_data}")

        # Try to route to appropriate search handler based on context
        current_search = context.user_data.get("current_search_field")
        conversation_state = context.user_data.get("conversation_state")

        logger.info(f"UNIVERSAL: Current search field: {current_search}, Conversation state: {conversation_state}")

        # CRITICAL FIX: Only route to search handlers if we're actually in search state
        # Don't route admin inputs to search handlers based on old search fields
        admin_state = context.user_data.get("admin_state")

        # If user is in admin state, don't route to search handlers
        if admin_state:
            logger.info(f"UNIVERSAL: User in admin state {admin_state}, routing to admin handler")
            return await self.admin_handler.handle_admin_input(update, context)

        # Route to appropriate handler based on current conversation state only
        if conversation_state == "MOBILE_SEARCH":
            logger.info("UNIVERSAL: Routing to mobile handler")
            context.user_data["conversation_state"] = "MOBILE_SEARCH"
            context.user_data["current_search_field"] = "mobile"
            return await self.mobile_handler.handle_mobile_input(update, context)

        elif conversation_state == "AADHAR_SEARCH":
            logger.info("UNIVERSAL: Routing to aadhar handler")
            context.user_data["conversation_state"] = "AADHAR_SEARCH"
            context.user_data["current_search_field"] = "aadhar"
            return await self.aadhar_handler.handle_aadhar_input(update, context)

        elif conversation_state == "ALT_SEARCH":
            logger.info("UNIVERSAL: Routing to alternate handler")
            context.user_data["conversation_state"] = "ALT_SEARCH"
            context.user_data["current_search_field"] = "alt"
            return await self.alternate_handler.handle_alternate_input(update, context)

        elif conversation_state == "VEHICLE_SEARCH":
            logger.info("UNIVERSAL: Routing to vehicle handler")
            context.user_data["conversation_state"] = "VEHICLE_SEARCH"
            context.user_data["current_search_field"] = "vehicle"
            return await self.vehicle_handler.handle_vehicle_input(update, context)

        else:
            # No known search state - check if user is authorized before showing menu
            logger.info(f"UNIVERSAL: No search state found, checking authorization for user {user_id}")

            # Check if user is authorized
            user = await self.start_handler.get_user_from_update(update)
            if not user or not self.start_handler.auth_service.is_user_authorized(user):
                # CRITICAL FIX: For unauthorized users, just tell them to use /start
                logger.info(f"UNIVERSAL: User {user_id} not authorized, directing to /start")
                await update.message.reply_text(
                    "Please use /start to begin.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                # Authorized user - send back to main menu
                logger.info(f"UNIVERSAL: Authorized user {user_id}, sending to main menu")
                await update.message.reply_text(
                    "Please use the menu buttons to navigate.",
                    reply_markup=self.start_handler.keyboard_builder.main_menu_keyboard()
                )

    def create_conversation_handler(self):
        """Create the main conversation handler"""
        return ConversationHandler(
            entry_points=[
                CommandHandler("start", self.start_handler.start_command),
                CallbackQueryHandler(self.start_handler.menu_callback_handler, pattern="^start_bot$")
            ],
            states={
                MENU: [
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^admin_"),
                    CallbackQueryHandler(self.start_handler.menu_callback_handler),
                ],
                SEARCH_MENU: [
                    CallbackQueryHandler(self.start_handler.menu_callback_handler),
                ],
                MOBILE_SEARCH: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.mobile_handler.handle_mobile_input),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^admin_"),
                    CallbackQueryHandler(self.start_handler.menu_callback_handler),
                ],
                AADHAR_SEARCH: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.aadhar_handler.handle_aadhar_input),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^admin_"),
                    CallbackQueryHandler(self.start_handler.menu_callback_handler),
                ],
                ALT_SEARCH: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.alternate_handler.handle_alternate_input),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^admin_"),
                    CallbackQueryHandler(self.start_handler.menu_callback_handler),
                ],
                VEHICLE_SEARCH: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.vehicle_handler.handle_vehicle_input),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^admin_"),
                    CallbackQueryHandler(self.start_handler.menu_callback_handler),
                ],
                ADMIN_PANEL: [
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^admin_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^msg_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^notif_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^rate_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^access_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^broadcast_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^lea_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^set_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^toggle_"),
                    CallbackQueryHandler(self.start_handler.menu_callback_handler)
                ],
                ADMIN_ADD_USER: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.admin_handler.handle_admin_input),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^admin_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^plan_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^bulk_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^toggle_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^set_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^msg_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^notif_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^rate_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^access_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^broadcast_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^lea_add_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^lea_wizard_"),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^lea_"),
                    CallbackQueryHandler(self.start_handler.menu_callback_handler),
                ],
                ADMIN_REMOVE_VEHICLE: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, self.admin_handler.handle_admin_input),
                    CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^admin_"),
                ],
                VIEW_LOGS: [CallbackQueryHandler(self.start_handler.menu_callback_handler)],
                VIEW_USER_LOGS: [CallbackQueryHandler(self.start_handler.menu_callback_handler)],
            },
            fallbacks=[
                CommandHandler("cancel", self.start_handler.cancel_command),
                CommandHandler("start", self.start_handler.start_command)  # Allow restart anytime
            ],
            allow_reentry=True,
            per_message=False
        )
    
    def add_command_handlers(self, app):
        """Add command handlers"""
        # Admin command handlers
        app.add_handler(CommandHandler("adduser", self.admin_handler.adduser_command))
        app.add_handler(CommandHandler("addcredits", self.admin_handler.addcredits_command))
        app.add_handler(CommandHandler("setcredits", self.admin_handler.setcredits_command))
        app.add_handler(CommandHandler("suspenduser", self.admin_handler.suspenduser_command))
        app.add_handler(CommandHandler("unsuspenduser", self.admin_handler.unsuspenduser_command))
        app.add_handler(CommandHandler("removeuser", self.admin_handler.removeuser_command))
        app.add_handler(CommandHandler("setprice", self.admin_handler.setprice_command))
        app.add_handler(CommandHandler("hiderecord", self.admin_handler.hiderecord_command))
        app.add_handler(CommandHandler("unhiderecord", self.admin_handler.unhiderecord_command))
        app.add_handler(CommandHandler("listhidden", self.admin_handler.listhidden_command))
        app.add_handler(CommandHandler("help", self.start_handler.help_command))
    
    async def run(self):
        """Main function to start the bot"""
        try:
            # Initialize database
            init_database()
            logger.info("Database initialized successfully")

            # Create application
            app = ApplicationBuilder().token(BOT_TOKEN).defaults(
                Defaults(parse_mode="Markdown")
            ).build()

            # Add conversation handler
            conv_handler = self.create_conversation_handler()
            app.add_handler(conv_handler)

            # Add LEA plan selection callback handler (must be before fallback)
            app.add_handler(CallbackQueryHandler(self.admin_handler.admin_callback_handler, pattern="^plan_"))

            # Add fallback handler for orphaned callbacks (must be after conv_handler)
            app.add_handler(CallbackQueryHandler(self.fallback_callback_handler))

            # Add debug message handler for troubleshooting (temporary)
            app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.debug_message_handler))

            # Add command handlers
            self.add_command_handlers(app)

            logger.info("Bot started and running...")
            await app.run_polling()

        except Exception as e:
            logger.error(f"An error occurred: {e}")
            raise

async def main():
    """Entry point"""
    bot = TelegramBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except RuntimeError as e:
        if "running event loop" in str(e):
            import nest_asyncio
            nest_asyncio.apply()
            asyncio.get_event_loop().run_until_complete(main())
        else:
            raise
