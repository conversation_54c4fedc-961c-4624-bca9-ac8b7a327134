# utils/error_logger.py
"""
Comprehensive error logging system for the bot
"""

import logging
import traceback
import json
from datetime import datetime
from pathlib import Path
import os

class BotErrorLogger:
    """Enhanced error logger for bot operations"""
    
    def __init__(self, log_file="bot_errors.log"):
        self.log_file = log_file
        self.setup_logger()
    
    def setup_logger(self):
        """Setup the error logger"""
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Setup error logger
        self.error_logger = logging.getLogger('bot_errors')
        self.error_logger.setLevel(logging.ERROR)
        
        # Remove existing handlers to avoid duplicates
        for handler in self.error_logger.handlers[:]:
            self.error_logger.removeHandler(handler)
        
        # File handler for errors
        error_handler = logging.FileHandler(f"logs/{self.log_file}", encoding='utf-8')
        error_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        error_handler.setFormatter(error_formatter)
        self.error_logger.addHandler(error_handler)
        
        # Also setup detailed interaction logger
        self.interaction_logger = logging.getLogger('bot_interactions')
        self.interaction_logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        for handler in self.interaction_logger.handlers[:]:
            self.interaction_logger.removeHandler(handler)
        
        # File handler for interactions
        interaction_handler = logging.FileHandler("logs/bot_interactions.log", encoding='utf-8')
        interaction_formatter = logging.Formatter(
            '%(asctime)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        interaction_handler.setFormatter(interaction_formatter)
        self.interaction_logger.addHandler(interaction_handler)
    
    def log_error(self, error, context=None, user_id=None, callback_data=None, message_text=None):
        """Log detailed error information"""
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "user_id": user_id,
            "callback_data": callback_data,
            "message_text": message_text,
            "context": context,
            "traceback": traceback.format_exc()
        }
        
        # Format error message
        error_msg = f"""
{'='*80}
ERROR DETAILS:
{'='*80}
Time: {error_info['timestamp']}
Error Type: {error_info['error_type']}
Error Message: {error_info['error_message']}
User ID: {error_info['user_id']}
Callback Data: {error_info['callback_data']}
Message Text: {error_info['message_text']}
Context: {error_info['context']}

FULL TRACEBACK:
{error_info['traceback']}
{'='*80}
"""
        
        self.error_logger.error(error_msg)

        # Also log to console for immediate visibility
        print(f"🚨 ERROR LOGGED: {error_info['error_type']} - {error_info['error_message']}")
        if user_id:
            print(f"   👤 User: {user_id}")
        if callback_data:
            print(f"   🔘 Button: {callback_data}")
        print(f"   📁 Full details saved to logs/bot_errors.log")
    
    def log_callback_error(self, update, context, error, handler_name=None):
        """Log callback query errors with full context"""
        callback_data = None
        user_id = None
        
        if update.callback_query:
            callback_data = update.callback_query.data
            user_id = update.callback_query.from_user.id
        
        context_info = f"Handler: {handler_name}" if handler_name else "Unknown handler"
        
        self.log_error(
            error=error,
            context=context_info,
            user_id=user_id,
            callback_data=callback_data
        )
    
    def log_message_error(self, update, context, error, handler_name=None):
        """Log message handling errors"""
        message_text = None
        user_id = None
        
        if update.message:
            message_text = update.message.text
            user_id = update.message.from_user.id
        
        context_info = f"Handler: {handler_name}" if handler_name else "Unknown handler"
        
        self.log_error(
            error=error,
            context=context_info,
            user_id=user_id,
            message_text=message_text
        )
    
    def log_interaction(self, user_id, action, details=None, success=True):
        """Log user interactions"""
        status = "SUCCESS" if success else "FAILED"
        details_str = f" | Details: {details}" if details else ""
        
        interaction_msg = f"User {user_id} | Action: {action} | Status: {status}{details_str}"
        self.interaction_logger.info(interaction_msg)
    
    def log_callback_interaction(self, update, action, success=True, details=None):
        """Log callback interactions"""
        if update.callback_query:
            user_id = update.callback_query.from_user.id
            callback_data = update.callback_query.data
            details_str = f"Callback: {callback_data}"
            if details:
                details_str += f" | {details}"
            
            self.log_interaction(user_id, action, details_str, success)
    
    def log_search_interaction(self, user_id, search_type, query, results_count, success=True, error=None):
        """Log search interactions"""
        details = f"Type: {search_type} | Query: {query} | Results: {results_count}"
        if error:
            details += f" | Error: {str(error)}"
        
        self.log_interaction(user_id, "SEARCH", details, success)
    
    def get_recent_errors(self, lines=50):
        """Get recent errors from log file"""
        try:
            log_path = Path(f"logs/{self.log_file}")
            if log_path.exists():
                with open(log_path, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    return ''.join(all_lines[-lines:])
            return "No error log file found"
        except Exception as e:
            return f"Error reading log file: {e}"
    
    def get_recent_interactions(self, lines=100):
        """Get recent interactions from log file"""
        try:
            log_path = Path("logs/bot_interactions.log")
            if log_path.exists():
                with open(log_path, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    return ''.join(all_lines[-lines:])
            return "No interaction log file found"
        except Exception as e:
            return f"Error reading interaction log file: {e}"
    
    def clear_logs(self):
        """Clear all log files"""
        try:
            for log_file in ["logs/bot_errors.log", "logs/bot_interactions.log"]:
                if os.path.exists(log_file):
                    open(log_file, 'w').close()
            print("✅ Log files cleared")
        except Exception as e:
            print(f"❌ Error clearing logs: {e}")

# Global error logger instance
error_logger = BotErrorLogger()

def log_error(error, context=None, user_id=None, callback_data=None, message_text=None):
    """Global function to log errors"""
    error_logger.log_error(error, context, user_id, callback_data, message_text)

def log_callback_error(update, context, error, handler_name=None):
    """Global function to log callback errors"""
    error_logger.log_callback_error(update, context, error, handler_name)

def log_message_error(update, context, error, handler_name=None):
    """Global function to log message errors"""
    error_logger.log_message_error(update, context, error, handler_name)

def log_interaction(user_id, action, details=None, success=True):
    """Global function to log interactions"""
    error_logger.log_interaction(user_id, action, details, success)

def log_callback_interaction(update, action, success=True, details=None):
    """Global function to log callback interactions"""
    error_logger.log_callback_interaction(update, action, success, details)

def log_search_interaction(user_id, search_type, query, results_count, success=True, error=None):
    """Global function to log search interactions"""
    error_logger.log_search_interaction(user_id, search_type, query, results_count, success, error)
