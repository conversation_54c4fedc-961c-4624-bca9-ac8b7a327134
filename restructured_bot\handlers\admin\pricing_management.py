# handlers/admin/pricing_management.py
import logging
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.base import BaseHandler
from config.constants import ADMIN_PANEL, ADMIN_ADD_USER
from database.models import SettingsModel

logger = logging.getLogger(__name__)

class PricingManagementHandler(BaseHandler):
    """Handler for smart pricing system management"""
    
    def __init__(self):
        super().__init__()
        self.settings_model = SettingsModel()
    
    async def show_pricing_panel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show smart pricing management panel"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        # Get current pricing settings
        global_pricing = self.settings_model.get_setting("global_pricing_enabled", True)
        global_cost = self.settings_model.get_setting("search_credit_cost", 1)
        
        # Individual costs
        mobile_cost = self.settings_model.get_setting("mobile_search_cost", 1)
        aadhar_cost = self.settings_model.get_setting("aadhar_search_cost", 2)
        alt_cost = self.settings_model.get_setting("alternate_search_cost", 1)
        vehicle_cost = self.settings_model.get_setting("vehicle_search_cost", 3)
        
        # LEA plan costs
        lea_30d = self.settings_model.get_setting("lea_30d_cost", 500)
        lea_6m = self.settings_model.get_setting("lea_6m_cost", 2500)
        lea_1y = self.settings_model.get_setting("lea_1y_cost", 5000)
        
        text = (
            f"💰 **Smart Pricing System**\n\n"
            f"🔄 **Global Pricing:** {'✅ ON' if global_pricing else '❌ OFF'}\n"
        )
        
        if global_pricing:
            text += (
                f"├─ Single price for all searches: {global_cost} credits\n"
                f"└─ Applies to all search types\n\n"
            )
        else:
            text += (
                f"├─ Individual feature pricing:\n"
                f"├─ 📱 Mobile Search: {mobile_cost} credits\n"
                f"├─ 🆔 ID Search: {aadhar_cost} credits\n"
                f"├─ 📞 Alt Search: {alt_cost} credits\n"
                f"└─ 🚗 Vehicle Search: {vehicle_cost} credits\n\n"
            )
        
        text += (
            f"🏛️ **LEA Plan Costs:**\n"
            f"├─ 30-Day Plan: ₹{lea_30d:,}\n"
            f"├─ 6-Month Plan: ₹{lea_6m:,}\n"
            f"└─ 1-Year Plan: ₹{lea_1y:,}\n\n"
            f"👁️ **User Display:**\n"
            f"├─ LEA Officers: No pricing shown\n"
            f"└─ Normal Users: {self.settings_model.get_pricing_display('normal_user')}"
        )
        
        keyboard = self.keyboard_builder.admin_pricing_keyboard()
        
        try:
            await query.edit_message_text(
                text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            if "Message is not modified" in str(e):
                await query.answer("Pricing panel refreshed")
            else:
                raise e
        
        self.log_admin_action(user["user_id"], "view_pricing_panel")
        return ADMIN_PANEL
    
    async def toggle_global_pricing(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Toggle global pricing mode"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        current_value = self.settings_model.get_setting("global_pricing_enabled", True)
        new_value = not current_value
        self.settings_model.set_setting("global_pricing_enabled", new_value)
        
        mode = "Global Pricing" if new_value else "Individual Pricing"
        await query.answer(f"Switched to {mode}")
        
        self.log_admin_action(user["user_id"], "toggle_pricing_mode", mode)
        
        # Refresh the pricing panel
        return await self.show_pricing_panel(update, context)
    
    async def set_pricing_costs(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show pricing cost setting options"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        global_pricing = self.settings_model.get_setting("global_pricing_enabled", True)
        
        if global_pricing:
            # Show global cost setting
            current_cost = self.settings_model.get_setting("search_credit_cost", 1)
            
            text = (
                f"💰 **Set Global Search Cost**\n\n"
                f"Current cost: {current_cost} credits per search\n\n"
                f"This cost applies to all search types.\n"
                f"Send new cost (number only):"
            )
            
            context.user_data["admin_state"] = "set_global_cost"
            context.user_data["cost_setting_key"] = "search_credit_cost"
        else:
            # Show individual cost setting menu
            mobile_cost = self.settings_model.get_setting("mobile_search_cost", 1)
            aadhar_cost = self.settings_model.get_setting("aadhar_search_cost", 2)
            alt_cost = self.settings_model.get_setting("alternate_search_cost", 1)
            vehicle_cost = self.settings_model.get_setting("vehicle_search_cost", 3)
            
            text = (
                f"💰 **Set Individual Search Costs**\n\n"
                f"Current costs:\n"
                f"📱 Mobile: {mobile_cost} credits\n"
                f"🆔 ID: {aadhar_cost} credits\n"
                f"📞 Alt: {alt_cost} credits\n"
                f"🚗 Vehicle: {vehicle_cost} credits\n\n"
                f"Choose which cost to modify:"
            )
            
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("📱 Mobile", callback_data="set_mobile_cost"),
                    InlineKeyboardButton("🆔 ID", callback_data="set_aadhar_cost")
                ],
                [
                    InlineKeyboardButton("📞 Alt", callback_data="set_alt_cost"),
                    InlineKeyboardButton("🚗 Vehicle", callback_data="set_vehicle_cost")
                ],
                [
                    InlineKeyboardButton("⬅️ Back", callback_data="admin_pricing"),
                    InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
                ]
            ])
            
            await query.edit_message_text(
                text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            
            return ADMIN_PANEL
        
        keyboard = self.keyboard_builder.admin_back_keyboard("admin_pricing")
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_ADD_USER
    
    async def set_individual_cost_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for setting individual search cost"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        cost_type = query.data.replace("set_", "").replace("_cost", "")
        
        cost_map = {
            "mobile": ("📱 Mobile Search", "mobile_search_cost"),
            "aadhar": ("🆔 ID Search", "aadhar_search_cost"),
            "alt": ("📞 Alternative Search", "alternate_search_cost"),
            "vehicle": ("🚗 Vehicle Search", "vehicle_search_cost")
        }
        
        if cost_type not in cost_map:
            return ADMIN_PANEL
        
        feature_name, setting_key = cost_map[cost_type]
        current_cost = self.settings_model.get_setting(setting_key, 1)
        
        context.user_data["admin_state"] = f"set_{cost_type}_cost"
        context.user_data["cost_setting_key"] = setting_key
        
        text = (
            f"💰 **Set {feature_name} Cost**\n\n"
            f"Current cost: {current_cost} credits\n\n"
            f"Send new cost (number only):"
        )
        
        keyboard = self.keyboard_builder.admin_back_keyboard("admin_set_costs")
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_ADD_USER
    
    async def set_lea_costs(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show LEA plan cost setting"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        # Get current LEA costs
        lea_30d = self.settings_model.get_setting("lea_30d_cost", 500)
        lea_6m = self.settings_model.get_setting("lea_6m_cost", 2500)
        lea_1y = self.settings_model.get_setting("lea_1y_cost", 5000)
        
        text = (
            f"🏛️ **LEA Plan Costs**\n\n"
            f"Current costs:\n"
            f"📅 30-Day Plan: ₹{lea_30d:,}\n"
            f"📅 6-Month Plan: ₹{lea_6m:,}\n"
            f"📅 1-Year Plan: ₹{lea_1y:,}\n\n"
            f"Choose which cost to modify:"
        )
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📅 30-Day", callback_data="set_lea_30d_cost"),
                InlineKeyboardButton("📅 6-Month", callback_data="set_lea_6m_cost")
            ],
            [
                InlineKeyboardButton("📅 1-Year", callback_data="set_lea_1y_cost")
            ],
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_pricing"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_PANEL
    
    async def set_lea_cost_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for setting LEA plan cost"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        cost_type = query.data.replace("set_lea_", "").replace("_cost", "")
        
        cost_map = {
            "30d": ("30-Day Plan", "lea_30d_cost", 500),
            "6m": ("6-Month Plan", "lea_6m_cost", 2500),
            "1y": ("1-Year Plan", "lea_1y_cost", 5000)
        }
        
        if cost_type not in cost_map:
            return ADMIN_PANEL
        
        plan_name, setting_key, default_cost = cost_map[cost_type]
        current_cost = self.settings_model.get_setting(setting_key, default_cost)
        
        context.user_data["admin_state"] = f"set_lea_{cost_type}_cost"
        context.user_data["cost_setting_key"] = setting_key
        
        text = (
            f"🏛️ **Set {plan_name} Cost**\n\n"
            f"Current cost: ₹{current_cost:,}\n\n"
            f"Send new cost (number only, without ₹ symbol):"
        )
        
        keyboard = self.keyboard_builder.admin_back_keyboard("admin_lea_costs")
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_ADD_USER
    
    async def show_pricing_preview(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show pricing preview for different user types"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        # Get pricing displays
        lea_display = self.settings_model.get_pricing_display("lea_officer")
        normal_display = self.settings_model.get_pricing_display("normal_user")
        
        text = (
            f"👁️ **Pricing Preview**\n\n"
            f"How pricing appears to different user types:\n\n"
            f"👮 **LEA Officer View:**\n"
            f"└─ {lea_display}\n\n"
            f"👤 **Normal User View:**\n"
            f"└─ {normal_display}\n\n"
            f"*LEA officers never see pricing information*"
        )
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_pricing"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_PANEL
    
    async def handle_pricing_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle pricing cost input"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        text = update.message.text.strip()
        current_state = context.user_data.get("admin_state")
        setting_key = context.user_data.get("cost_setting_key")
        
        if not current_state or not setting_key:
            await update.message.reply_text("❌ Invalid operation")
            return ADMIN_PANEL
        
        try:
            new_cost = int(text)
            if new_cost < 0:
                await update.message.reply_text("❌ Cost cannot be negative")
                return ADMIN_ADD_USER
            
            # Set the new cost
            self.settings_model.set_setting(setting_key, new_cost)
            
            # Format success message and show input prompt again
            if "lea" in setting_key:
                success_msg = f"✅ LEA plan cost updated to ₹{new_cost:,}"
            else:
                success_msg = f"✅ Search cost updated to {new_cost} credits"

            self.log_admin_action(user["user_id"], "update_pricing", f"{setting_key} -> {new_cost}")

            # Show success message first
            await update.message.reply_text(success_msg)

            # Then show the input prompt again for convenience
            await self.show_input_prompt_again(update, context, current_state, setting_key)
            
        except ValueError:
            await update.message.reply_text("❌ Please enter a valid number")
            return ADMIN_ADD_USER
        except Exception as e:
            logger.error(f"Error updating pricing: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
        
        return ADMIN_PANEL

    async def show_input_prompt_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE, current_state: str, setting_key: str):
        """Show the input prompt again after successful update"""
        try:
            # Determine the prompt details based on current state
            if current_state == "set_global_cost":
                current_cost = self.settings_model.get_setting("search_credit_cost", 1)
                text = (
                    f"💰 **Set Global Search Cost**\n\n"
                    f"Current cost: {current_cost} credits per search\n\n"
                    f"This cost applies to all search types.\n\n"
                    f"Enter new cost (number only):\n"
                    f"Example: `1`"
                )
                cancel_callback = "admin_pricing"

            elif current_state == "set_mobile_cost":
                current_cost = self.settings_model.get_setting("mobile_search_cost", 1)
                text = (
                    f"📱 **Set Mobile Search Cost**\n\n"
                    f"Current cost: {current_cost} credits\n\n"
                    f"Enter new cost (number only):\n"
                    f"Example: `2`"
                )
                cancel_callback = "admin_pricing"

            elif current_state == "set_aadhar_cost":
                current_cost = self.settings_model.get_setting("aadhar_search_cost", 2)
                text = (
                    f"🆔 **Set AADHAR Search Cost**\n\n"
                    f"Current cost: {current_cost} credits\n\n"
                    f"Enter new cost (number only):\n"
                    f"Example: `3`"
                )
                cancel_callback = "admin_pricing"

            elif current_state == "set_alternate_cost":
                current_cost = self.settings_model.get_setting("alternate_search_cost", 1)
                text = (
                    f"📞 **Set Alternative Search Cost**\n\n"
                    f"Current cost: {current_cost} credits\n\n"
                    f"Enter new cost (number only):\n"
                    f"Example: `1`"
                )
                cancel_callback = "admin_pricing"

            elif current_state == "set_vehicle_cost":
                current_cost = self.settings_model.get_setting("vehicle_search_cost", 3)
                text = (
                    f"🚗 **Set Vehicle Search Cost**\n\n"
                    f"Current cost: {current_cost} credits\n\n"
                    f"Enter new cost (number only):\n"
                    f"Example: `4`"
                )
                cancel_callback = "admin_pricing"

            elif current_state.startswith("set_lea_"):
                # LEA cost settings
                if "30d" in current_state:
                    current_cost = self.settings_model.get_setting("lea_30d_cost", 500)
                    text = (
                        f"💰 **Set 30-Day LEA Plan Cost**\n\n"
                        f"Current cost: ₹{current_cost:,}\n\n"
                        f"Enter new cost (number only):\n"
                        f"Example: `500`"
                    )
                    cancel_callback = "admin_lea_30d"
                elif "6m" in current_state:
                    current_cost = self.settings_model.get_setting("lea_6m_cost", 2500)
                    text = (
                        f"💰 **Set 6-Month LEA Plan Cost**\n\n"
                        f"Current cost: ₹{current_cost:,}\n\n"
                        f"Enter new cost (number only):\n"
                        f"Example: `2500`"
                    )
                    cancel_callback = "admin_lea_6m"
                elif "1y" in current_state:
                    current_cost = self.settings_model.get_setting("lea_1y_cost", 5000)
                    text = (
                        f"💰 **Set 1-Year LEA Plan Cost**\n\n"
                        f"Current cost: ₹{current_cost:,}\n\n"
                        f"Enter new cost (number only):\n"
                        f"Example: `5000`"
                    )
                    cancel_callback = "admin_lea_1y"
                else:
                    return  # Unknown LEA state
            else:
                return  # Unknown state

            # Create keyboard with navigation options
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("⬅️ Back", callback_data=cancel_callback),
                    InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
                ]
            ])

            # Send the input prompt again
            await update.message.reply_text(
                text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"Error showing input prompt again: {e}")
            # Fallback - clear context and return to admin panel
            context.user_data.pop("admin_state", None)
            context.user_data.pop("cost_setting_key", None)

    async def set_global_cost_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for global search cost"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "set_global_cost"
        context.user_data["cost_setting_key"] = "search_credit_cost"

        current_cost = self.settings_model.get_setting("search_credit_cost", 1)

        await query.edit_message_text(
            f"💰 **Set Global Search Cost**\n\n"
            f"Current cost: {current_cost} credits per search\n\n"
            f"This cost applies to all search types.\n\n"
            f"Enter new cost (number only):\n"
            f"Example: `1`",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="admin_pricing")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return ADMIN_ADD_USER

    # Individual cost setting methods
    async def set_mobile_cost_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for mobile search cost"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "set_mobile_cost"
        context.user_data["cost_setting_key"] = "mobile_search_cost"

        current_cost = self.settings_model.get_setting("mobile_search_cost", 1)

        await query.edit_message_text(
            f"📱 **Set Mobile Search Cost**\n\n"
            f"Current cost: {current_cost} credits\n\n"
            f"Enter new cost (number only):\n"
            f"Example: `2`",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="admin_pricing")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return ADMIN_ADD_USER

    async def set_aadhar_cost_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for aadhar search cost"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "set_aadhar_cost"
        context.user_data["cost_setting_key"] = "aadhar_search_cost"

        current_cost = self.settings_model.get_setting("aadhar_search_cost", 2)

        await query.edit_message_text(
            f"🆔 **Set AADHAR Search Cost**\n\n"
            f"Current cost: {current_cost} credits\n\n"
            f"Enter new cost (number only):\n"
            f"Example: `3`",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="admin_pricing")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return ADMIN_ADD_USER

    async def set_alternate_cost_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for alternate search cost"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "set_alternate_cost"
        context.user_data["cost_setting_key"] = "alternate_search_cost"

        current_cost = self.settings_model.get_setting("alternate_search_cost", 1)

        await query.edit_message_text(
            f"📞 **Set Alternate Search Cost**\n\n"
            f"Current cost: {current_cost} credits\n\n"
            f"Enter new cost (number only):\n"
            f"Example: `2`",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="admin_pricing")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return ADMIN_ADD_USER

    async def set_vehicle_cost_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for vehicle search cost"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "set_vehicle_cost"
        context.user_data["cost_setting_key"] = "vehicle_search_cost"

        current_cost = self.settings_model.get_setting("vehicle_search_cost", 3)

        await query.edit_message_text(
            f"🚗 **Set Vehicle Search Cost**\n\n"
            f"Current cost: {current_cost} credits\n\n"
            f"Enter new cost (number only):\n"
            f"Example: `4`",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="admin_pricing")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return ADMIN_ADD_USER
