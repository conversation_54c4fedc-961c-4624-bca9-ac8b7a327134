# handlers/start.py
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.base import <PERSON>Handler
from config.constants import MENU, SEARCH_MENU
from services.formatting import FormattingService
from database.models import SearchModel
from database.models.settings import SettingsModel
from utils.error_logger import log_callback_error, log_callback_interaction, log_message_error

logger = logging.getLogger(__name__)

class StartHandler(BaseHandler):
    """Handler for start command and main menu"""
    
    def __init__(self):
        super().__init__()
        self.search_model = SearchModel()
        self.settings_model = SettingsModel()

        # Import handlers for pagination callbacks
        from handlers.search.mobile import MobileSearchHandler
        from handlers.search.aadhar import AadharSearchHandler
        from handlers.search.alternate import AlternateSearchHandler
        from handlers.search.vehicle import VehicleSearchHandler

        self.mobile_handler = MobileSearchHandler()
        self.aadhar_handler = AadharSearchHandler()
        self.alternate_handler = AlternateSearchHandler()
        self.vehicle_handler = VehicleSearchHandler()

    async def delete_success_message_if_exists(self, context: ContextTypes.DEFAULT_TYPE):
        """Delete success message if it exists"""
        try:
            success_message_id = context.user_data.get("success_message_id")
            success_chat_id = context.user_data.get("success_chat_id")

            if success_message_id and success_chat_id:
                await context.bot.delete_message(
                    chat_id=success_chat_id,
                    message_id=success_message_id
                )
                context.user_data.pop("success_message_id", None)
                context.user_data.pop("success_chat_id", None)
                logger.info(f"Deleted success message {success_message_id}")
        except Exception as e:
            logger.debug(f"Could not delete success message: {e}")
            context.user_data.pop("success_message_id", None)
            context.user_data.pop("success_chat_id", None)

        # PAGINATION FIX: Also clear results message IDs when navigating away
        context.user_data.pop("results_message_id", None)
        context.user_data.pop("results_chat_id", None)
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with User ID display for unauthorized users"""
        from telegram.ext import ConversationHandler

        user = await self.get_user_from_update(update)

        if not user:
            # CRITICAL FIX: Follow old bot approach - show welcome message first, then access denied
            user_id = update.effective_user.id
            start_msg = self.settings_model.get_setting(
                "start_message",
                "👮 Welcome to Law Enforcement Bot!"
            )

            await update.message.reply_text(
                f"{start_msg}\n\n"
                f"**Your Unique User ID:** `{user_id}`",
                parse_mode=ParseMode.MARKDOWN
            )

            # Then show access denied message
            await self.send_access_denied_with_user_id(update, context)
            # CRITICAL FIX: End conversation for unauthorized users (like old bot)
            return ConversationHandler.END

        if not self.auth_service.is_user_authorized(user):
            await self.send_access_denied_with_user_id(update, context, user)
            # CRITICAL FIX: End conversation for unauthorized users (like old bot)
            return ConversationHandler.END

        await self.show_main_menu(update, context, user)
        return MENU
    
    async def show_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user=None):
        """Show main menu"""
        from telegram.ext import ConversationHandler

        if not user:
            user = await self.get_user_from_update(update)

        if not user:
            await self.send_access_denied_with_user_id(update, context)
            # CRITICAL FIX: End conversation for unauthorized users (like old bot)
            return ConversationHandler.END

        if not self.auth_service.is_user_authorized(user):
            await self.send_access_denied_with_user_id(update, context, user)
            # CRITICAL FIX: End conversation for unauthorized users (like old bot)
            return ConversationHandler.END
        
        message = self.message_templates.welcome_message(user)
        keyboard = self.keyboard_builder.main_menu_keyboard(user)
        
        if update.callback_query:
            try:
                await update.callback_query.edit_message_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e:
                if "Message is not modified" in str(e):
                    await update.callback_query.answer("Menu refreshed")
                else:
                    raise e
        else:
            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        
        # CRITICAL FIX: Clear search states when returning to main menu
        # This prevents search validation from interfering with other operations
        context.user_data.pop("current_search_field", None)
        context.user_data["conversation_state"] = "MENU"
        context.user_data["user_id"] = user["user_id"]

        self.log_user_action(user["user_id"], "viewed_main_menu")
        return MENU
    
    async def show_search_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Enhanced search menu with user-specific features and error handling"""
        try:
            user = await self.check_user_authorization(update, context)
            if not user:
                return MENU

            # Enhanced: Check if user can search with error handling
            try:
                can_search = self.auth_service.can_search(user)
                logger.info(f"User {user['user_id']} can search: {can_search}")

                if not can_search:
                    expired_msg = self.auth_service.get_expired_message(user)
                    await update.callback_query.edit_message_text(
                        expired_msg,
                        reply_markup=self.keyboard_builder.main_menu_keyboard(user),
                        parse_mode=ParseMode.MARKDOWN
                    )
                    return MENU
            except Exception as search_check_error:
                logger.error(f"Error checking search permission: {search_check_error}")
                # Allow search by default if check fails
                pass

            # Enhanced: Build search menu text with pricing info
            text = "🔍 **Search Database**\n\n"

            # Add pricing info for normal users with error handling
            try:
                if not self.auth_service.is_lea_officer(user):
                    pricing_display = self.auth_service.get_pricing_display(user)
                    if pricing_display:
                        text += f"💰 **Pricing:** {pricing_display}\n\n"
            except Exception as pricing_error:
                logger.error(f"Error getting pricing display: {pricing_error}")
                # Continue without pricing info

            text += "Choose search type:"

            # Enhanced: Build keyboard with user-specific features
            try:
                keyboard = self.keyboard_builder.search_menu_keyboard(user)
            except Exception as keyboard_error:
                logger.error(f"Error building search menu keyboard: {keyboard_error}")
                # Fallback keyboard
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("📱 Mobile", callback_data="do_search_mobile")],
                    [InlineKeyboardButton("🆔 AADHAR", callback_data="do_search_aadhar")],
                    [InlineKeyboardButton("📞 Alternate", callback_data="do_search_alt")],
                    [InlineKeyboardButton("🚗 Vehicle", callback_data="do_search_vehicle")],
                    [InlineKeyboardButton("🔙 Back to Menu", callback_data="main_menu")]
                ])

            try:
                await update.callback_query.edit_message_text(
                    text,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e:
                if "Message is not modified" in str(e):
                    await update.callback_query.answer("Search menu refreshed")
                else:
                    raise e

            # CRITICAL FIX: Clear previous search states when entering search menu
            # This ensures clean state for new searches
            context.user_data.pop("current_search_field", None)
            context.user_data["conversation_state"] = "SEARCH_MENU"
            context.user_data["user_id"] = user["user_id"]

            self.log_user_action(user["user_id"], "viewed_search_menu")
            logger.info(f"Successfully showed search menu to user {user['user_id']} - State: SEARCH_MENU")
            return SEARCH_MENU

        except Exception as e:
            logger.error(f"Critical error in show_search_menu: {e}")
            try:
                await update.callback_query.answer("❌ Error loading search menu")
                await self.show_main_menu(update, context)
            except:
                pass
            return MENU
    
    async def show_user_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user information"""
        user = await self.check_user_authorization(update, context)
        if not user:
            return MENU
        
        user_info = self.auth_service.get_user_info(user)
        message = FormattingService.format_user_info(user_info)
        keyboard = self.keyboard_builder.user_info_keyboard()
        
        await update.callback_query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        self.log_user_action(user["user_id"], "viewed_user_info")
        return MENU
    
    async def show_user_logs(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page=0):
        """Show user search logs"""
        user = await self.check_user_authorization(update, context)
        if not user:
            return MENU
        
        logs = self.search_model.get_user_logs(user["user_id"], limit=50)
        message, total_pages = self.pagination_service.format_log_list(logs, page)
        
        keyboard = self.pagination_service.create_admin_list_keyboard(
            page, total_pages, "user_logs", "back_main"
        )
        
        await update.callback_query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        self.log_user_action(user["user_id"], "viewed_user_logs", f"page_{page}")
        return MENU
    
    async def show_pricing(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show pricing information"""
        user = await self.check_user_authorization(update, context)
        if not user:
            return MENU
        
        message = self.message_templates.pricing_message()
        keyboard = self.keyboard_builder.simple_back_keyboard()
        
        await update.callback_query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        self.log_user_action(user["user_id"], "viewed_pricing")
        return MENU
    
    async def menu_callback_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle menu callback queries with comprehensive error logging"""
        query = update.callback_query
        data = query.data

        try:
            # Log the callback attempt
            log_callback_interaction(update, "MENU_CALLBACK_ATTEMPT", success=True,
                                   details=f"Callback: {data}")

            await query.answer()

        except Exception as e:
            log_callback_error(update, context, e, "menu_callback_handler_init")
            return MENU
        
        # Main menu callbacks with error logging
        try:
            if data == "back_main" or data == "start_bot":
                log_callback_interaction(update, "MAIN_MENU_ACCESS", success=True)
                # CRITICAL FIX: Clear search states when going back to main menu
                context.user_data.pop("current_search_field", None)
                await self.delete_success_message_if_exists(context)
                return await self.show_main_menu(update, context)

            elif data == "search_menu":
                log_callback_interaction(update, "SEARCH_MENU_ACCESS", success=True)
                try:
                    # CRITICAL FIX: Clear previous search states when going to search menu
                    context.user_data.pop("current_search_field", None)
                    await self.delete_success_message_if_exists(context)
                    return await self.show_search_menu(update, context)
                except Exception as search_menu_error:
                    logger.error(f"Error showing search menu: {search_menu_error}")
                    await query.answer("❌ Error loading search menu")
                    return MENU

            elif data == "user_info":
                log_callback_interaction(update, "USER_INFO_ACCESS", success=True)
                return await self.show_user_info(update, context)

            elif data == "user_logs":
                log_callback_interaction(update, "USER_LOGS_ACCESS", success=True)
                return await self.show_user_logs(update, context)

            elif data == "pricing":
                log_callback_interaction(update, "PRICING_ACCESS", success=True)
                return await self.show_pricing(update, context)

            # Search type callbacks
            elif data == "do_search_mobile":
                log_callback_interaction(update, "MOBILE_SEARCH_INITIATE", success=True)
                await self.delete_success_message_if_exists(context)
                return await self.initiate_mobile_search(update, context)

            elif data == "do_search_aadhar":
                log_callback_interaction(update, "AADHAR_SEARCH_INITIATE", success=True)
                await self.delete_success_message_if_exists(context)
                return await self.initiate_aadhar_search(update, context)

            elif data == "do_search_alt":
                log_callback_interaction(update, "ALT_SEARCH_INITIATE", success=True)
                await self.delete_success_message_if_exists(context)
                return await self.initiate_alternate_search(update, context)

            elif data == "do_search_vehicle":
                log_callback_interaction(update, "VEHICLE_SEARCH_INITIATE", success=True)
                await self.delete_success_message_if_exists(context)
                return await self.initiate_vehicle_search(update, context)

            # Search result pagination callbacks
            elif data.startswith("mobile_page:"):
                log_callback_interaction(update, "MOBILE_PAGINATION", success=True)
                page = int(data.split(':')[1])
                return await self.mobile_handler.handle_pagination(update, context, page)

            elif data.startswith("aadhar_page:"):
                log_callback_interaction(update, "AADHAR_PAGINATION", success=True)
                page = int(data.split(':')[1])
                return await self.aadhar_handler.handle_pagination(update, context, page)

            elif data.startswith("alt_page:"):
                log_callback_interaction(update, "ALT_PAGINATION", success=True)
                page = int(data.split(':')[1])
                return await self.alternate_handler.handle_pagination(update, context, page)

            elif data.startswith("vehicle_page:"):
                log_callback_interaction(update, "VEHICLE_PAGINATION", success=True)
                page = int(data.split(':')[1])
                return await self.vehicle_handler.handle_pagination(update, context, page)

            # Other pagination callbacks
            elif data.startswith("user_logs_page:"):
                log_callback_interaction(update, "USER_LOGS_PAGINATION", success=True)
                page = self.pagination_service.get_page_from_callback(data)
                return await self.show_user_logs(update, context, page)

            # Search again callback
            elif data == "search_again":
                log_callback_interaction(update, "SEARCH_AGAIN", success=True)
                return await self.handle_search_again(update, context)

            # Default fallback - log unhandled callbacks for debugging
            else:
                log_callback_interaction(update, "UNHANDLED_MENU_CALLBACK", success=False,
                                       details=f"Unhandled callback: {data}")
                logger.warning(f"Unhandled callback in menu handler: {data}")
                return await self.show_main_menu(update, context)

        except Exception as e:
            log_callback_error(update, context, e, f"menu_callback_{data}")
            await query.answer("❌ Error processing request")
            return MENU
    
    async def initiate_mobile_search(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Initiate mobile search with comprehensive error handling"""
        try:
            from config.constants import MOBILE_SEARCH

            user = await self.check_user_authorization(update, context)
            if not user:
                await update.callback_query.answer("❌ Authorization failed")
                return MENU

            # Check feature enabled with detailed logging
            try:
                feature_enabled = self.auth_service.is_feature_enabled("mobile", user)
                logger.info(f"Mobile search feature enabled for user {user['user_id']}: {feature_enabled}")

                if not feature_enabled:
                    await self.send_feature_disabled(update, context, "Mobile Search")
                    return MENU
            except Exception as feature_error:
                logger.error(f"Error checking mobile feature: {feature_error}")
                await update.callback_query.answer("❌ Error checking feature access")
                return MENU

            # Get search cost with error handling
            try:
                cost = self.auth_service.get_search_cost("mobile")
                logger.info(f"Mobile search cost for user {user['user_id']}: {cost}")
            except Exception as cost_error:
                logger.error(f"Error getting mobile search cost: {cost_error}")
                cost = 1  # Default cost

            # Generate message and keyboard
            try:
                message = self.message_templates.search_prompt_message("mobile", cost)
                keyboard = self.keyboard_builder.search_input_keyboard()
            except Exception as ui_error:
                logger.error(f"Error generating mobile search UI: {ui_error}")
                message = f"📱 **Mobile Search**\n\nSend the mobile number to search.\n\nCost: {cost} credits"
                keyboard = InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Menu", callback_data="search_menu")]])

            # Send the message
            await update.callback_query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

            # Ensure conversation state is properly set
            context.user_data["current_search_field"] = "mobile"
            context.user_data["conversation_state"] = "MOBILE_SEARCH"
            context.user_data["user_id"] = user["user_id"]

            self.log_user_action(user["user_id"], "initiated_mobile_search")
            logger.info(f"Successfully initiated mobile search for user {user['user_id']} - State: MOBILE_SEARCH")
            return MOBILE_SEARCH

        except Exception as e:
            logger.error(f"Critical error in initiate_mobile_search: {e}")
            try:
                await update.callback_query.answer("❌ Error starting mobile search")
                await self.show_main_menu(update, context)
            except:
                pass
            return MENU
    
    async def initiate_aadhar_search(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Initiate Aadhar search"""
        from config.constants import AADHAR_SEARCH
        
        user = await self.check_user_authorization(update, context)
        if not user:
            return MENU

        if not self.auth_service.is_feature_enabled("aadhar", user):
            await self.send_feature_disabled(update, context, "Aadhar Search")
            return MENU

        cost = self.auth_service.get_search_cost("aadhar")
        message = self.message_templates.search_prompt_message("aadhar", cost)
        keyboard = self.keyboard_builder.search_input_keyboard()
        
        await update.callback_query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Ensure conversation state is properly set
        context.user_data["current_search_field"] = "id"
        context.user_data["conversation_state"] = "AADHAR_SEARCH"
        context.user_data["user_id"] = user["user_id"]

        self.log_user_action(user["user_id"], "initiated_aadhar_search")
        logger.info(f"Successfully initiated aadhar search for user {user['user_id']} - State: AADHAR_SEARCH")
        return AADHAR_SEARCH
    
    async def initiate_alternate_search(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Initiate alternate search"""
        from config.constants import ALT_SEARCH
        
        user = await self.check_user_authorization(update, context)
        if not user:
            return MENU

        if not self.auth_service.is_feature_enabled("alternate", user):
            await self.send_feature_disabled(update, context, "Alternate Search")
            return MENU

        cost = self.auth_service.get_search_cost("alternate")
        message = self.message_templates.search_prompt_message("alt", cost)
        keyboard = self.keyboard_builder.search_input_keyboard()
        
        await update.callback_query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Ensure conversation state is properly set
        context.user_data["current_search_field"] = "alt"
        context.user_data["conversation_state"] = "ALT_SEARCH"
        context.user_data["user_id"] = user["user_id"]

        self.log_user_action(user["user_id"], "initiated_alternate_search")
        logger.info(f"Successfully initiated alternate search for user {user['user_id']} - State: ALT_SEARCH")
        return ALT_SEARCH
    
    async def initiate_vehicle_search(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Initiate vehicle search"""
        from config.constants import VEHICLE_SEARCH
        
        user = await self.check_user_authorization(update, context)
        if not user:
            return MENU

        if not self.auth_service.is_feature_enabled("vehicle", user):
            await self.send_feature_disabled(update, context, "Vehicle Search")
            return MENU

        cost = self.auth_service.get_search_cost("vehicle")
        message = self.message_templates.search_prompt_message("vehicle", cost)
        keyboard = self.keyboard_builder.search_input_keyboard()
        
        await update.callback_query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Ensure conversation state is properly set
        context.user_data["current_search_field"] = "vehicle"
        context.user_data["conversation_state"] = "VEHICLE_SEARCH"
        context.user_data["user_id"] = user["user_id"]

        self.log_user_action(user["user_id"], "initiated_vehicle_search")
        logger.info(f"Successfully initiated vehicle search for user {user['user_id']} - State: VEHICLE_SEARCH")
        return VEHICLE_SEARCH
    
    async def handle_search_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle search again callback"""
        field = context.user_data.get("current_search_field", "mobile")
        
        if field == "mobile":
            return await self.initiate_mobile_search(update, context)
        elif field == "id":
            return await self.initiate_aadhar_search(update, context)
        elif field == "alt":
            return await self.initiate_alternate_search(update, context)
        elif field == "vehicle":
            return await self.initiate_vehicle_search(update, context)
        else:
            return await self.show_search_menu(update, context)
    
    async def cancel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /cancel command"""
        user = await self.get_user_from_update(update)
        
        await update.message.reply_text(
            "❌ **Operation Cancelled**\n\nReturning to main menu...",
            parse_mode=ParseMode.MARKDOWN
        )
        
        if user:
            self.log_user_action(user["user_id"], "cancelled_operation")
        
        return await self.show_main_menu(update, context, user)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_text = (
            "🤖 **Bot Help**\n\n"
            "**Available Commands:**\n"
            "/start - Start the bot\n"
            "/help - Show this help message\n"
            "/cancel - Cancel current operation\n\n"
            "**Search Types:**\n"
            "📱 Mobile Number Search\n"
            "🆔 Aadhar Number Search\n"
            "📞 Alternate Number Search\n"
            "🚗 Vehicle Number Search\n\n"
            "**Features:**\n"
            "• Credit-based search system\n"
            "• Smart hiding for privacy\n"
            "• Search history and logs\n"
            "• Admin panel for management\n\n"
            "For support, contact the administrator."
        )
        
        keyboard = self.keyboard_builder.simple_back_keyboard()
        
        await update.message.reply_text(
            help_text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        user = await self.get_user_from_update(update)
        if user:
            self.log_user_action(user["user_id"], "viewed_help")
        
        return MENU
