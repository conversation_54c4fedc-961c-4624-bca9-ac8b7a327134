# handlers.py
import logging
import json
from telegram import InlineKeyboardMarkup, InlineKeyboardButton, Update
from telegram.constants import ParseMode
from telegram.ext import ContextTypes, ConversationHandler
from datetime import datetime, timezone
import config
import db_utils

logger = logging.getLogger(__name__)

def validate_mobile_number(input_text):
    """Validate mobile number input (10 digits, handle +91 prefix and spaces)"""
    if not input_text:
        return False, "❌ Please enter a mobile number"

    # Step 1: Remove +91 prefix if present
    cleaned_input = input_text.strip()
    if cleaned_input.startswith('+91'):
        cleaned_input = cleaned_input[3:]
    elif cleaned_input.startswith('91') and len(cleaned_input) == 12:
        # Handle case like "919876543210"
        cleaned_input = cleaned_input[2:]

    # Step 2: Remove all spaces
    cleaned_input = cleaned_input.replace(' ', '')

    # Step 3: Check if only digits
    if not cleaned_input.isdigit():
        return False, "❌ Please enter only numbers (no letters or special characters)"

    # Step 4: Check length (exactly 10 digits)
    if len(cleaned_input) != 10:
        return False, f"❌ Please enter exactly 10 digits (you entered {len(cleaned_input)} digits)"

    return True, cleaned_input

def validate_id_number(input_text):
    """Validate ID/Aadhar number input (exactly 12 digits)"""
    if not input_text:
        return False, "❌ Please enter an ID/Aadhar number"

    # Step 1: Remove all spaces and dashes
    cleaned_input = input_text.strip().replace(' ', '').replace('-', '')

    # Step 2: Check if only digits
    if not cleaned_input.isdigit():
        return False, "❌ Please enter only numbers (no letters or special characters)"

    # Step 3: Check length (exactly 12 digits)
    if len(cleaned_input) != 12:
        return False, f"❌ Please enter exactly 12 digits (you entered {len(cleaned_input)} digits)"

    return True, cleaned_input

def validate_vehicle_number(input_text):
    """Validate vehicle number input (7-10 characters, no special characters)"""
    if not input_text:
        return False, "❌ Please enter a vehicle number"

    # Step 1: Remove spaces and dashes, convert to uppercase
    cleaned_input = input_text.strip().replace(' ', '').replace('-', '').upper()

    # Step 2: Check length (7-10 characters)
    if len(cleaned_input) < 7:
        return False, f"❌ Please enter at least 7 characters (you entered {len(cleaned_input)} characters)"
    elif len(cleaned_input) > 10:
        return False, f"❌ Please enter maximum 10 characters (you entered {len(cleaned_input)} characters)"

    # Step 3: Check if only letters and numbers (no special characters)
    if not cleaned_input.isalnum():
        return False, "❌ Please enter only letters and numbers (no special characters)"

    return True, cleaned_input

async def show_validation_error(update: Update, context: ContextTypes.DEFAULT_TYPE, error_message: str, search_type: str):
    """Show validation error and keep user in input state"""
    # Determine the appropriate buttons based on search type
    buttons = [
        [
            InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
            InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
        ]
    ]

    await update.message.reply_text(
        f"{error_message}\n\nPlease try again:",
        reply_markup=InlineKeyboardMarkup(buttons),
        parse_mode=ParseMode.MARKDOWN
    )

def escape_markdown(text):
    """Escape special Markdown characters"""
    if not text:
        return text
    # Escape Markdown special characters
    special_chars = ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
    for char in special_chars:
        text = str(text).replace(char, f'\\{char}')
    return text

def format_user_info(user):
    """Format user information for display"""
    plan = db_utils.get_plan(user)
    expiry = db_utils.normalize_datetime(user.get("expiry"))
    
    # Safely escape username for Markdown
    username = user.get('username', 'Unknown')
    if username:
        # Escape special Markdown characters
        username = username.replace("_", "\\_").replace("*", "\\*").replace("`", "\\`").replace("[", "\\[").replace("]", "\\]")
    
    if user.get("is_admin"):
        credits_str = "Unlimited"
        expiry_str = "Lifetime"
    else:
        credits_str = str(user.get("credits", 0))
        expiry_str = expiry.strftime("%Y-%m-%d") if expiry else "N/A"
    
    suspension_info = ""
    if user.get("is_suspended", False):
        suspend_until = db_utils.normalize_datetime(user.get("suspended_until"))
        if suspend_until:
            suspension_info = f"\n🚫 Suspended until: {suspend_until.strftime('%Y-%m-%d')}"
        else:
            suspension_info = "\n🚫 Suspended: Permanently"
    
    return (
        f"👤 **Name:** {username}\n"
        f"🆔 **ID:** `{user.get('user_id')}`\n"
        f"🛡 **Plan:** {plan}\n"
        f"💎 **Credits:** {credits_str}\n"
        f"⏳ **Expiry:** {expiry_str}"
        f"{suspension_info}"
    )

def format_search_result(item):
    """Format search result with standardized display format"""
    formatted = []

    # Standardized field order and mapping
    field_order = [
        ("mobile", "📱", "Mobile"),
        ("name", "👤", "Name"),
        ("careof", "👥", "Father's Name"),  # Standardized as Father's Name
        ("fname", "👥", "Father's Name"),   # Handle userdata fname field
        ("address", "📍", "Address"),
        ("operator", "📡", "Operator"),
        ("circle", "📡", "Circle"),         # Handle userdata circle field
        ("alt", "📞", "Alternative No."),
        ("id", "🆔", "Govt ID/Aadhar ID"), # Standardized name
        ("dob", "🎂", "DOB")
    ]

    # Process fields in the specified order
    for field_key, emoji, display_name in field_order:
        value = item.get(field_key)
        if value and str(value).strip():
            # Special handling for DOB - remove escaping for dates
            if field_key == "dob":
                formatted.append(f"{emoji} {display_name} : {value}")
            else:
                # Escape the value to prevent Markdown parsing errors
                escaped_value = escape_markdown(str(value))
                formatted.append(f"{emoji} {display_name} : {escaped_value}")

    return "\n".join(formatted)

def format_results(data, page=0):
    """Format paginated results"""
    start = page * config.RESULTS_PER_PAGE
    page_data = data[start: start + config.RESULTS_PER_PAGE]
    total_pages = (len(data) - 1) // config.RESULTS_PER_PAGE + 1
    
    if not page_data:
        return "❌ No results found.", 0
    
    results = []
    for idx, item in enumerate(page_data, start=start + 1):
        results.append(f"**📋 Result #{idx}**\n{format_search_result(item)}")
    
    content = "\n\n" + "─" * 30 + "\n\n".join([""] + results) + "\n\n"
    footer = f"📄 Page {page + 1} of {total_pages} | Total: {len(data)} results"
    
    return content + footer, total_pages

def pagination_buttons(page, total_pages, prefix, additional_buttons=None):
    """Create pagination buttons"""
    buttons = []
    
    # Navigation buttons
    nav_buttons = []
    if page > 0:
        nav_buttons.append(InlineKeyboardButton("⬅️ Prev", callback_data=f"{prefix}:{page-1}"))
    if page < total_pages - 1:
        nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"{prefix}:{page+1}"))
    
    if nav_buttons:
        buttons.append(nav_buttons)
    
    # Additional buttons
    if additional_buttons:
        buttons.extend(additional_buttons)
    
    # Back buttons with Search Again
    buttons.append([
        InlineKeyboardButton("🔍 Search Again", callback_data="search_again"),
        InlineKeyboardButton("⬅️ Back", callback_data="search_menu")
    ])
    buttons.append([
        InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
    ])
    
    return InlineKeyboardMarkup(buttons)

def build_main_menu(user):
    """Build main menu keyboard"""
    # First row: Search and Account
    kb = [
        [
            InlineKeyboardButton("🔍 Search", callback_data="search_menu"),
            InlineKeyboardButton("👤 Account", callback_data="account"),
        ],
    ]
    
    # Second row: Pricing and Join Channel
    second_row = []
    if db_utils.get_setting("pricing_button_enabled", True):
        second_row.append(InlineKeyboardButton(
            db_utils.get_setting("pricing_button_text", "💰 Pricing"), 
            callback_data="pricing"
        ))
    
    second_row.append(InlineKeyboardButton(
        db_utils.get_setting("join_channel_text", "📢 Join Channel"), 
        url=db_utils.get_setting("join_channel_link", "https://t.me/your_channel_here")
    ))
    
    if second_row:
        kb.append(second_row)
    
    # Third row: View History
    kb.append([
        InlineKeyboardButton("📜 View History", callback_data="view_logs")
    ])
    
    # Admin panel button for admins
    if user.get("is_admin", False):
        kb.append([InlineKeyboardButton("⚙️ Admin Panel", callback_data="admin_panel")])
    
    return InlineKeyboardMarkup(kb)

async def pre_start_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle messages before /start"""
    user = db_utils.get_user(update.effective_user.id)
    if not user:
        prestart_msg = db_utils.get_setting(
            "prestart_message", 
            "👋 Welcome! Please click /start to begin."
        )
        await update.message.reply_text(
            prestart_msg,
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🚀 Start Bot", callback_data="start_bot")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
    return

async def start_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command"""
    user = db_utils.get_user(update.effective_user.id)
    
    if not user:
        # Show custom start message for unauthorized users too
        user_id = update.effective_user.id
        start_msg = db_utils.get_setting(
            "start_message", 
            "👮 Welcome to Law Enforcement Bot!"
        )
        
        await update.message.reply_text(
            f"{start_msg}\n\n"
            f"**Your Unique User ID:** `{user_id}`",
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Then show access denied message
        access_denied_msg = db_utils.get_setting(
            "access_denied_message",
            "❌ **Access Denied**\n\nYou are not authorized to use this bot. Please contact the administrator to get access."
        )
        await update.message.reply_text(
            access_denied_msg,
            parse_mode=ParseMode.MARKDOWN
        )
        return ConversationHandler.END
    
    await show_main_menu(update, context)
    return config.MENU

async def show_main_menu(update, context):
    """Show main menu"""
    # Build full name from first and last name
    full_name = ""
    if update.effective_user.first_name:
        full_name = update.effective_user.first_name
        if update.effective_user.last_name:
            full_name += f" {update.effective_user.last_name}"
    
    user = db_utils.create_user_if_missing(
        update.effective_user.id, 
        update.effective_user.username or full_name or f"user_{update.effective_user.id}"
    )
    
    if not user:
        return
    
    kb = build_main_menu(user)
    start_msg = db_utils.get_setting(
        "start_message", 
        "👮 Welcome to Law Enforcement Bot!"
    )
    
    # Make usernames clickable
    import re
    start_msg = re.sub(r'@(\w+)', r'[@\1](tg://user?username=\1)', start_msg)
    
    text = f"{start_msg}\n\n**Your Unique User ID:** `{user['user_id']}`"
    
    if update.callback_query:
        await update.callback_query.answer()
        await update.callback_query.edit_message_text(
            text, 
            reply_markup=kb, 
            parse_mode=ParseMode.MARKDOWN
        )
    else:
        await update.message.reply_text(
            text, 
            reply_markup=kb, 
            parse_mode=ParseMode.MARKDOWN
        )

async def menu_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle menu button clicks"""
    query = update.callback_query
    await query.answer()
    data = query.data
    
    # Smart recovery: If user data is missing or corrupted, restart the flow
    try:
        # Handle start_bot callback for unauthorized users
        if data == "start_bot":
            user_id = update.effective_user.id
            start_msg = db_utils.get_setting(
                "start_message", 
                "👮 Welcome to Law Enforcement Bot!"
            )
            
            await query.edit_message_text(
                f"{start_msg}\n\n"
                f"**Your Unique User ID:** `{user_id}`",
                parse_mode=ParseMode.MARKDOWN
            )
            
            # Then show access denied message
            access_denied_msg = db_utils.get_setting(
                "access_denied_message",
                "❌ **Access Denied**\n\nYou are not authorized to use this bot. Please contact the administrator to get access."
            )
            await update.effective_chat.send_message(
                access_denied_msg,
                parse_mode=ParseMode.MARKDOWN
            )
            return ConversationHandler.END
        
        # Copy functionality removed - no longer supported
        
        # Get user for all other operations
        # Build full name from first and last name
        full_name = ""
        if update.effective_user.first_name:
            full_name = update.effective_user.first_name
            if update.effective_user.last_name:
                full_name += f" {update.effective_user.last_name}"
        
        user = db_utils.create_user_if_missing(
            update.effective_user.id, 
            update.effective_user.username or full_name or f"user_{update.effective_user.id}"
        )
        
        if not user:
            access_denied_msg = db_utils.get_setting(
                "access_denied_message",
                "❌ **Access Denied**\n\nYou are not authorized to use this bot. Please contact the administrator to get access."
            )
            await query.edit_message_text(
                access_denied_msg,
                parse_mode=ParseMode.MARKDOWN
            )
            return ConversationHandler.END

        if data == "back_main":
            await show_main_menu(update, context)
            return config.MENU
            
        elif data == "search_menu":
            # Build search menu based on enabled options
            search_buttons = []
            
            if db_utils.get_setting("mobile_search_enabled", True):
                search_buttons.append(InlineKeyboardButton("📱 Mobile", callback_data="do_search_mobile"))
            
            if db_utils.get_setting("aadhar_search_enabled", True):
                search_buttons.append(InlineKeyboardButton("🆔 Aadhar", callback_data="do_search_aadhar"))
            
            if db_utils.get_setting("alternate_search_enabled", True):
                search_buttons.append(InlineKeyboardButton("📞 Alternate", callback_data="do_search_alt"))

            if db_utils.get_setting("vehicle_search_enabled", True):
                search_buttons.append(InlineKeyboardButton("🚗 Vehicle", callback_data="do_search_vehicle"))

            # Arrange buttons in rows of 2
            kb_rows = []
            for i in range(0, len(search_buttons), 2):
                kb_rows.append(search_buttons[i:i+2])
            
            # Add back button
            kb_rows.append([InlineKeyboardButton("⬅️ Back", callback_data="back_main")])
            
            kb = InlineKeyboardMarkup(kb_rows)
            
            if not search_buttons:
                text = "🔍 **Search Menu**\n\n❌ No search options are currently enabled.\nContact admin."
            else:
                text = "🔍 **Choose Search Type:**"
                
            await query.edit_message_text(
                text, 
                reply_markup=kb,
                parse_mode=ParseMode.MARKDOWN
            )
            return config.SEARCH_MENU
            
        elif data == "do_search_mobile":
            if not db_utils.get_setting("mobile_search_enabled", True):
                await query.answer("Mobile search is currently disabled.")
                return config.MENU
            
            cost = db_utils.get_setting("search_credit_cost", 1)
            await query.edit_message_text(
                f"📱 **Enter Mobile Number:**\n\n"
                f"Please enter the mobile number you want to search for.\n"
                f"💰 **Per Search Cost:** {cost} credits\n\n"
                f"*(without country code +91)*",
                reply_markup=InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                        InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                    ]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return config.MOBILE_SEARCH
            
        elif data == "do_search_aadhar":
            if not db_utils.get_setting("aadhar_search_enabled", True):
                await query.answer("Aadhar search is currently disabled.")
                return config.MENU
            
            cost = db_utils.get_setting("search_credit_cost", 1)
            await query.edit_message_text(
                f"🆔 **Enter Aadhar Number:**\n\n"
                f"Please enter the Aadhar number you want to search for.\n"
                f"💰 **Per Search Cost:** {cost} credits",
                reply_markup=InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                        InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                    ]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return config.AADHAR_SEARCH
            
        elif data == "do_search_alt":
            if not db_utils.get_setting("alternate_search_enabled", True):
                await query.answer("Alternate search is currently disabled.")
                return config.MENU
            
            cost = db_utils.get_setting("search_credit_cost", 1)
            await query.edit_message_text(
                f"📞 **Enter Alternate Number:**\n\n"
                f"Please enter the alternate number you want to search for.\n"
                f"💰 **Per Search Cost:** {cost} credits\n\n"
                f"*(without country code +91)*",
                reply_markup=InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                        InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                    ]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return config.ALT_SEARCH

        elif data == "do_search_vehicle":
            if not db_utils.get_setting("vehicle_search_enabled", True):
                await query.answer("Vehicle search is currently disabled.")
                return config.MENU

            cost = db_utils.get_setting("search_credit_cost", 1)
            await query.edit_message_text(
                f"🚗 **Enter Vehicle Number:**\n\n"
                f"Please enter the vehicle registration number you want to search for.\n"
                f"💰 **Per Search Cost:** {cost} credits\n\n"
                f"*Examples: DL1AB123, MH12345, UP16BC7890*",
                reply_markup=InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                        InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                    ]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return config.VEHICLE_SEARCH
            
        elif data == "account":
            await query.edit_message_text(
                format_user_info(user),
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("⬅️ Back", callback_data="back_main")
                ]]),
                parse_mode=ParseMode.MARKDOWN
            )
            return config.MENU
            
        elif data == "pricing":
            pricing_text = db_utils.get_setting(
                "pricing_text", 
                "Each search costs 1 credit.\nContact admin to recharge."
            )
            await query.edit_message_text(
                f"💲 **Pricing Information**\n\n{pricing_text}",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("⬅️ Back", callback_data="back_main")
                ]]),
                parse_mode=ParseMode.MARKDOWN
            )
            return config.MENU
            
        elif data == "view_logs":
            return await show_user_logs(update, context, user["user_id"])
            
        elif data == "admin_panel" and user.get("is_admin", False):
            from admin import admin_panel_handler
            return await admin_panel_handler(update, context)
            
        elif data.startswith("logs_page:"):
            page = int(data.split(":")[1])
            return await show_user_logs(update, context, user["user_id"], page)
            
        elif data.startswith("mobile_page:"):
            page = int(data.split(":")[1])
            # Check if results exist, if not restart
            if not context.user_data.get("mobile_results"):
                await query.answer("Session expired. Redirecting to main menu...")
                await show_main_menu(update, context)
                return config.MENU
            await query.answer()
            await show_paginated_results_new_message(context, "mobile_results", page, "mobile_page", query.from_user.id)
            return config.MENU

        elif data.startswith("id_page:"):
            page = int(data.split(":")[1])
            # Check if results exist, if not restart
            if not context.user_data.get("id_results"):
                await query.answer("Session expired. Redirecting to main menu...")
                await show_main_menu(update, context)
                return config.MENU
            await query.answer()
            await show_paginated_results_new_message(context, "id_results", page, "id_page", query.from_user.id)
            return config.MENU

        elif data.startswith("alt_page:"):
            page = int(data.split(":")[1])
            # Check if results exist, if not restart
            if not context.user_data.get("alt_results"):
                await query.answer("Session expired. Redirecting to main menu...")
                await show_main_menu(update, context)
                return config.MENU
            await query.answer()
            await show_paginated_results_new_message(context, "alt_results", page, "alt_page", query.from_user.id)
            return config.MENU
            

        elif data == "search_again":
            # Get the current search field and prompt for new search
            field = context.user_data.get("current_search_field", "mobile")
            await query.answer()

            # Send NEW message for input prompt (preserves previous results)
            if field == "mobile":
                if not db_utils.get_setting("mobile_search_enabled", True):
                    await query.answer("Mobile search is currently disabled.")
                    return config.MENU

                cost = db_utils.get_setting("search_credit_cost", 1)
                await context.bot.send_message(
                    chat_id=query.from_user.id,
                    text=f"📱 **Enter Mobile Number:**\n\n"
                         f"Please enter the mobile number you want to search for.\n"
                         f"💰 **Per Search Cost:** {cost} credits\n\n"
                         f"*(without country code +91)*",
                    reply_markup=InlineKeyboardMarkup([
                        [
                            InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                            InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                        ]
                    ]),
                    parse_mode=ParseMode.MARKDOWN
                )
                return config.MOBILE_SEARCH

            elif field == "id":
                if not db_utils.get_setting("aadhar_search_enabled", True):
                    await query.answer("Aadhar search is currently disabled.")
                    return config.MENU

                cost = db_utils.get_setting("search_credit_cost", 1)
                await context.bot.send_message(
                    chat_id=query.from_user.id,
                    text=f"🆔 **Enter Aadhar Number:**\n\n"
                         f"Please enter the Aadhar number you want to search for.\n"
                         f"💰 **Per Search Cost:** {cost} credits",
                    reply_markup=InlineKeyboardMarkup([
                        [
                            InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                            InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                        ]
                    ]),
                    parse_mode=ParseMode.MARKDOWN
                )
                return config.AADHAR_SEARCH

            elif field == "alt":
                if not db_utils.get_setting("alt_search_enabled", True):
                    await query.answer("Alternate search is currently disabled.")
                    return config.MENU

                cost = db_utils.get_setting("search_credit_cost", 1)
                await context.bot.send_message(
                    chat_id=query.from_user.id,
                    text=f"📞 **Enter Alternate Number:**\n\n"
                         f"Please enter the alternate number you want to search for.\n"
                         f"💰 **Per Search Cost:** {cost} credits\n\n"
                         f"*(without country code +91)*",
                    reply_markup=InlineKeyboardMarkup([
                        [
                            InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                            InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                        ]
                    ]),
                    parse_mode=ParseMode.MARKDOWN
                )
                return config.ALT_SEARCH

            elif field == "vehicle":
                if not db_utils.get_setting("vehicle_search_enabled", True):
                    await query.answer("Vehicle search is currently disabled.")
                    return config.MENU

                cost = db_utils.get_setting("search_credit_cost", 1)
                await context.bot.send_message(
                    chat_id=query.from_user.id,
                    text=f"🚗 **Enter Vehicle Number:**\n\n"
                         f"Please enter the vehicle registration number you want to search for.\n"
                         f"💰 **Per Search Cost:** {cost} credits\n\n"
                         f"*Examples: DL1AB123, MH12345, UP16BC7890*",
                    reply_markup=InlineKeyboardMarkup([
                        [
                            InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                            InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                        ]
                    ]),
                    parse_mode=ParseMode.MARKDOWN
                )
                return config.VEHICLE_SEARCH

            else:
                # Default to search menu if unknown field
                await context.bot.send_message(
                    chat_id=query.from_user.id,
                    text="🔍 **Search Menu**\n\nPlease select a search option:",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")]
                    ]),
                    parse_mode=ParseMode.MARKDOWN
                )
                return config.SEARCH_MENU
        
        else:
            await query.answer("Unknown action.")
            return config.MENU
            
    except Exception as e:
        logger.error(f"Menu handler error: {e}")
        # Smart recovery: On any error, restart the bot flow
        await query.answer("Bot restarted. Redirecting to main menu...")
        try:
            await show_main_menu(update, context)
            return config.MENU
        except:
            # If even main menu fails, send a simple message
            restart_msg = db_utils.get_setting("bot_restart_message", "🔄 **CyberNetra is Updated.**\n\nPlease use /start to continue.")
            await query.edit_message_text(
                restart_msg,
                parse_mode=ParseMode.MARKDOWN
            )
            return ConversationHandler.END

async def perform_search(update: Update, context: ContextTypes.DEFAULT_TYPE, field, field_label, next_state):
    """Perform database search"""
    # Build full name from first and last name
    full_name = ""
    if update.effective_user.first_name:
        full_name = update.effective_user.first_name
        if update.effective_user.last_name:
            full_name += f" {update.effective_user.last_name}"
    
    user = db_utils.create_user_if_missing(
        update.effective_user.id, 
        update.effective_user.username or full_name or f"user_{update.effective_user.id}"
    )
    
    if not user or not db_utils.is_user_authorized(user):
        await update.message.reply_text(
            "❌ **Access Denied**\n\n"
            "No credits remaining or subscription expired. Contact admin.",
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return config.MENU

    query_text = update.message.text.strip()

    # Validate input based on search type
    if field in ["mobile", "alt"]:
        # Mobile number validation
        is_valid, result = validate_mobile_number(query_text)
        if not is_valid:
            await show_validation_error(update, context, result, field)
            return next_state  # Stay in same input state
        query_text = result  # Use cleaned input

    elif field == "id":
        # ID/Aadhar number validation
        is_valid, result = validate_id_number(query_text)
        if not is_valid:
            await show_validation_error(update, context, result, field)
            return next_state  # Stay in same input state
        query_text = result  # Use cleaned input

    elif field == "vehicle":
        # Vehicle number validation
        is_valid, result = validate_vehicle_number(query_text)
        if not is_valid:
            await show_validation_error(update, context, result, field)
            return next_state  # Stay in same input state
        query_text = result  # Use cleaned input

    # Check credit balance before search
    cost = db_utils.get_setting("search_credit_cost", 1)
    if not user.get("is_admin", False) and user.get("credits", 0) < cost:
        insufficient_msg = db_utils.get_setting(
            "insufficient_credits_message", 
            "❌ **Insufficient Credits**\n\nYou don't have enough credits to perform this search.\nContact admin to recharge your account."
        )
        await update.message.reply_text(
            insufficient_msg,
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return config.MENU

    wait_msg = await update.message.reply_text(f"🔍 **Searching for {query_text}...**\n\nPlease wait while we search the database.")

    try:
        results = db_utils.search_data(field, query_text)
        
        if not results:
            await wait_msg.edit_text(
                f"❌ **No Results Found**\n\n"
                f"No data found for {field_label}: `{query_text}`",
                reply_markup=InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("🔍 Search Again", callback_data="search_again"),
                        InlineKeyboardButton("⬅️ Back", callback_data="search_menu")
                    ],
                    [
                        InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                    ]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            
            # No automatic continuation message for no results - user can use buttons in the "No Results" message
            return config.MENU

        db_utils.log_search(user["user_id"], field, query_text, len(results))
        
        if not user.get("is_admin", False):
            db_utils.users.update_one(
                {"user_id": user["user_id"]}, 
                {"$inc": {"credits": -cost}}
            )
            db_utils.increment_search_count(user)
            db_utils.update_search_time(user["user_id"])

        context.user_data[f"{field}_results"] = results
        context.user_data["current_search_field"] = field

        # Send results in NEW message (preserves search history)
        await show_paginated_results_new_message(context, f"{field}_results", 0, f"{field}_page", user["user_id"])

        # Send credit notification first (if enabled)
        if db_utils.get_setting("notifications_enabled", True) and not user.get("is_admin", False):
            updated_user = db_utils.get_user(user["user_id"])
            credit_msg = (
                f"✅ **Search Completed**\n\n"
                f"💳 Used {cost} credit(s)\n"
                f"💎 Credits remaining: {updated_user.get('credits', 0)}"
            )
            try:
                await context.bot.send_message(
                    chat_id=user["user_id"],
                    text=credit_msg,
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e:
                logger.error(f"Failed to send credit notification: {e}")

        # Return MENU state so pagination buttons work correctly
        return config.MENU

    except Exception as e:
        logger.error(f"Search error: {e}")
        await wait_msg.edit_text(
            "❌ **Search Error**\n\nAn error occurred while searching. Please try again.",
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Send continuation message for error - LAST
        continuation_msg = get_search_continuation_message(field, cost)
        await update.message.reply_text(
            continuation_msg,
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return config.MENU

async def show_paginated_results(msg_obj, context, data_key, page, prefix, is_edit=False):
    """Show paginated search results"""
    data = context.user_data.get(data_key, [])
    text, total_pages = format_results(data, page)

    # No copy buttons - clean interface
    kb = pagination_buttons(page, total_pages, prefix, None)

    if is_edit:
        await msg_obj.edit_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    else:
        await msg_obj.reply_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)

async def show_paginated_results_new_message(context, data_key, page, prefix, user_id):
    """Show paginated search results in NEW message (preserves search history)"""
    data = context.user_data.get(data_key, [])
    text, total_pages = format_results(data, page)

    # No copy buttons - clean interface
    kb = pagination_buttons(page, total_pages, prefix, None)

    # Send as NEW message to preserve previous results
    await context.bot.send_message(
        chat_id=user_id,
        text=text,
        reply_markup=kb,
        parse_mode=ParseMode.MARKDOWN
    )

# Copy functionality removed - clean interface with formatted results only

async def show_user_logs(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id, page=0):
    """Show user search logs"""
    logs = db_utils.get_user_logs(user_id, limit=config.LOGS_PER_PAGE * 10)
    
    if not logs:
        text = "📑 **Your Search History**\n\n❌ No search history found."
        kb = InlineKeyboardMarkup([[
            InlineKeyboardButton("⬅️ Back", callback_data="back_main")
        ]])
    else:
        start = page * config.LOGS_PER_PAGE
        page_logs = logs[start:start + config.LOGS_PER_PAGE]
        total_pages = (len(logs) - 1) // config.LOGS_PER_PAGE + 1
        
        log_lines = []
        for log in page_logs:
            timestamp = log["timestamp"].strftime("%Y-%m-%d %H:%M")
            query_type = log["query_type"].title()
            query = log["query"]
            results = log.get("results_count", 0)
            log_lines.append(f"🔍 **{query_type}:** `{query}`\n⏰ {timestamp} | 📊 {results} results")
        
        text = f"📑 **Your Search History**\n\n" + "\n\n".join(log_lines)
        text += f"\n\n📄 Page {page + 1} of {total_pages}"
        
        buttons = []
        nav_buttons = []
        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ Prev", callback_data=f"logs_page:{page-1}"))
        if page < total_pages - 1:
            nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"logs_page:{page+1}"))
        
        if nav_buttons:
            buttons.append(nav_buttons)
        
        buttons.append([InlineKeyboardButton("⬅️ Back", callback_data="back_main")])
        kb = InlineKeyboardMarkup(buttons)
    
    if update.callback_query:
        await update.callback_query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    else:
        await update.message.reply_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    
    return config.VIEW_LOGS

# Search handlers
async def handle_mobile_search(update: Update, context: ContextTypes.DEFAULT_TYPE):
    return await perform_search(update, context, "mobile", "Mobile Number", config.MOBILE_SEARCH)

async def handle_aadhar_search(update: Update, context: ContextTypes.DEFAULT_TYPE):
    return await perform_search(update, context, "id", "Aadhar Number", config.AADHAR_SEARCH)

async def handle_alt_search(update: Update, context: ContextTypes.DEFAULT_TYPE):
    return await perform_search(update, context, "alt", "Alternate Number", config.ALT_SEARCH)

async def handle_vehicle_search(update: Update, context: ContextTypes.DEFAULT_TYPE):
    return await perform_vehicle_search(update, context)

async def perform_vehicle_search(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Perform comprehensive vehicle search using multiple APIs"""
    import requests
    import json
    import asyncio

    # Build full name from first and last name
    full_name = ""
    if update.effective_user.first_name:
        full_name = update.effective_user.first_name
        if update.effective_user.last_name:
            full_name += f" {update.effective_user.last_name}"

    user = db_utils.create_user_if_missing(
        update.effective_user.id,
        update.effective_user.username or full_name or f"user_{update.effective_user.id}"
    )

    if not user or not db_utils.is_user_authorized(user):
        await update.message.reply_text(
            "❌ **Access Denied**\n\n"
            "No credits remaining or subscription expired. Contact admin.",
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return config.MENU

    query_text = update.message.text.strip()

    # Validate vehicle number
    is_valid, result = validate_vehicle_number(query_text)
    if not is_valid:
        await show_validation_error(update, context, result, "vehicle")
        return config.VEHICLE_SEARCH
    query_text = result  # Use cleaned input

    # Check credit balance before search
    cost = db_utils.get_setting("search_credit_cost", 1)
    if not user.get("is_admin", False) and user.get("credits", 0) < cost:
        insufficient_msg = db_utils.get_setting(
            "insufficient_credits_message",
            "❌ **Insufficient Credits**\n\nYou don't have enough credits to perform this search.\nContact admin to recharge your account."
        )
        await update.message.reply_text(
            insufficient_msg,
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu"),
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return config.MENU

    # Check our vehicle database first
    cached_vehicle = db_utils.get_cached_vehicle(query_text)

    if cached_vehicle:
        # Found in our database - instant response!
        db_utils.update_vehicle_search_stats(query_text)

        # Store results for search again functionality
        context.user_data["vehicle_results"] = [cached_vehicle["formatted_result"]]
        context.user_data["current_search_field"] = "vehicle"

        # Deduct credits and log search
        db_utils.log_search(user["user_id"], "vehicle", query_text, 1)

        if not user.get("is_admin", False):
            db_utils.users.update_one(
                {"user_id": user["user_id"]},
                {"$inc": {"credits": -cost}}
            )
            db_utils.increment_search_count(user)
            db_utils.update_search_time(user["user_id"])

        # Send results from our database (show source only to admin)
        result_text = cached_vehicle['formatted_result']
        if user.get("is_admin", False):
            result_text = f"📊 **From Our Database**\n\n{result_text}"

        await context.bot.send_message(
            chat_id=update.effective_user.id,
            text=result_text,
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("🔍 Search Again", callback_data="search_again"),
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu")
                ],
                [
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

        # Send credit notification
        if db_utils.get_setting("notifications_enabled", True) and not user.get("is_admin", False):
            updated_user = db_utils.get_user(user["user_id"])
            credit_msg = (
                f"✅ **Search Completed**\n\n"
                f"💳 Used {cost} credit(s)\n"
                f"💎 Credits remaining: {updated_user.get('credits', 0)}"
            )
            # Show search count only to admin
            if user.get("is_admin", False):
                credit_msg += f"\n📊 This vehicle was searched {cached_vehicle.get('search_count', 1)} times"
            try:
                await context.bot.send_message(
                    chat_id=user["user_id"],
                    text=credit_msg,
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e:
                logger.error(f"Failed to send credit notification: {e}")

        return config.MENU

    # Not in our database - search APIs
    wait_msg = await update.message.reply_text(f"🔍 **Searching for {query_text}...**\n\nPlease wait while we gather comprehensive vehicle information.")

    try:
        # Call all 4 APIs with 10-second timeout
        vehicle_data = await call_vehicle_apis(query_text)

        if not any(vehicle_data.values()):
            await wait_msg.edit_text(
                f"❌ **Vehicle Not Found**\n\n"
                f"No information found for vehicle number: `{query_text}`\n\n"
                f"This could mean:\n"
                f"• Vehicle number doesn't exist\n"
                f"• Vehicle not registered in available databases\n"
                f"• Temporary service unavailability",
                reply_markup=InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("🔍 Search Again", callback_data="search_again"),
                        InlineKeyboardButton("⬅️ Back", callback_data="search_menu")
                    ],
                    [
                        InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                    ]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )

            # Store empty results for search again functionality
            context.user_data["vehicle_results"] = []
            context.user_data["current_search_field"] = "vehicle"
            return config.MENU

        # Format and display results
        formatted_result = format_vehicle_results(vehicle_data, query_text)

        # Store in our permanent database
        db_utils.store_vehicle_data(query_text, vehicle_data, formatted_result, user["user_id"])

        # Store results for search again functionality
        context.user_data["vehicle_results"] = [formatted_result]
        context.user_data["current_search_field"] = "vehicle"

        # Deduct credits and log search
        db_utils.log_search(user["user_id"], "vehicle", query_text, len([formatted_result]))

        if not user.get("is_admin", False):
            db_utils.users.update_one(
                {"user_id": user["user_id"]},
                {"$inc": {"credits": -cost}}
            )
            db_utils.increment_search_count(user)
            db_utils.update_search_time(user["user_id"])

        # Send results in NEW message (preserves search history)
        await context.bot.send_message(
            chat_id=update.effective_user.id,
            text=formatted_result,
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("🔍 Search Again", callback_data="search_again"),
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu")
                ],
                [
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

        # Send credit notification (if enabled)
        if db_utils.get_setting("notifications_enabled", True) and not user.get("is_admin", False):
            updated_user = db_utils.get_user(user["user_id"])
            credit_msg = (
                f"✅ **Search Completed**\n\n"
                f"💳 Used {cost} credit(s)\n"
                f"💎 Credits remaining: {updated_user.get('credits', 0)}"
            )
            try:
                await context.bot.send_message(
                    chat_id=user["user_id"],
                    text=credit_msg,
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e:
                logger.error(f"Failed to send credit notification: {e}")

        return config.MENU

    except Exception as e:
        logger.error(f"Vehicle search error: {e}")
        await wait_msg.edit_text(
            "⚠️ **Service Temporarily Unavailable**\n\n"
            "Unable to fetch vehicle information at the moment.\n\n"
            "Please try again in a few minutes.",
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("🔍 Search Again", callback_data="search_again"),
                    InlineKeyboardButton("⬅️ Back", callback_data="search_menu")
                ],
                [
                    InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
                ]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return config.MENU

async def call_vehicle_apis(vehicle_number):
    """Call all 4 vehicle APIs with 10-second timeout each"""
    import requests
    import asyncio
    from concurrent.futures import ThreadPoolExecutor

    def call_rc_api(vehicle_number):
        """Call RC Information API"""
        try:
            url = "https://glacier.on-track.in/api/external/v2/rc_info"
            headers = {
                "user-agent": "Dart/3.4 (dart:io)",
                "content-type": "application/json",
                "accept-encoding": "gzip",
                "host": "glacier.on-track.in"
            }
            data = {"vehicleId": vehicle_number}
            response = requests.post(url, headers=headers, json=data, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"RC API error: {e}")
            return None

    def call_gtplay_api(vehicle_number):
        """Call GTPlay Vehicle Info API"""
        try:
            url = "https://gtplay.in/API/vehicle_challan_info/vehicle_info.php"
            headers = {
                "Host": "gtplay.in",
                "content-type": "application/x-www-form-urlencoded",
                "accept-encoding": "gzip",
                "user-agent": "okhttp/4.10.0"
            }
            data = {"vehicle_no": vehicle_number}
            response = requests.post(url, headers=headers, data=data, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"GTPlay API error: {e}")
            return None

    def call_challan_api(vehicle_number):
        """Call Challan Details API"""
        try:
            url = "https://core.cuvora.com/car/velocity/armor/v1/challan/scrape"
            headers = {
                "searchcount": "3",
                "masktype": "NORMAL",
                "deviceheight": "2306",
                "appflavour": "car",
                "appversion": "492",
                "model": "23049PCD8I",
                "accept-encoding": "gzip",
                "segment": "5",
                "locale": "en",
                "authorization": "",
                "insurancebannertype": "default",
                "os_version": "33",
                "clientid": "android_car-info",
                "appopencount": "7",
                "salt": "255",
                "ghacfk": "234b2a89a7231524bc1fd88ed6c70fd2bb2b0a9945b5e398f532343f7efd7c38ed5a943cef041df6460f51ee7ccec4da713a03d4515889eba0124cc26da50095",
                "ts": "1745396259079",
                "src": "android_car-info",
                "analyticsappinstanceid": "a71f70ddb739dcc25239ce50d7efba02",
                "uniquesearchcount": "1",
                "region": "",
                "content-length": "207",
                "rewardedadstate": "NOT_SHOWN",
                "zip": "",
                "source": "launcher",
                "user-agent": "Dart/3.6 (dart:io)",
                "cmsconfig": '{"enable": true, "homeCMS": true, "rcDetailCMS": false}',
                "installerpackagename": "null",
                "deviceid": "pW7lGZQR4uf3Otc3vMKmpbpjhgi+EaEyDxgpVfOtMrY=",
                "userid": "6803a8d35f8e2f1ff7239337",
                "mobile": "",
                "adsshowncount": "9",
                "auth": "b5462b5b2116c44c8b76af4d82afd1db3157c925fd9d0c138311b29ead5cf8190f75e5d382ed29d52fd3896b85d32be92df63414061fb4eb4e54a5fbcdb6ecdf",
                "city": "Jaipur",
                "content-type": "application/x-www-form-urlencoded",
                "topics_subscribed": "ALL_USERS_V2",
                "apikey": "",
                "cityid": "10387",
                "fcmtoken": "fVp4INHKTy-FTUsTGz4sh9:APA91bGwvfvtMcIv9IcDPvu38rO1lxG4HD6mFr9xe_IUrp8240pquQkxOzbzod3Xay4YVA9THH_RwQwuS07K9BfGUuv9opq9JMgLFEl3GRkWQVCjkRNF-lw",
                "devicewidth": "1080",
                "homepagetype": "NORMAL",
                "encryption": "custom",
                "host": "core.cuvora.com",
                "manufacturer": "Xiaomi"
            }
            params = {"number": vehicle_number, "type": "flutter"}
            data = {
                "number": vehicle_number,
                "clientId": "ECHALLAN_INIT",
                "requestId": "",
                "body": "",
                "responseCode": "",
                "challanSessionId": "a7db87d8-05d8-4ced-9ea0-db00430b4236",
                "additionalData": "",
                "isRefresh": "false",
                "challanState": "",
                "type": "flutter",
                "respHeaders": "",
                "source": "loader"
            }
            response = requests.post(url, params=params, headers=headers, data=data, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Challan API error: {e}")
            return None

    def call_fastag_api(vehicle_number):
        """Call FASTag Details API"""
        try:
            url = f'https://b2c-gateway-india.c24.tech/api/v3/bill-payment/fastag/register?inputParamValue={vehicle_number}'
            headers = {
                'Host': 'b2c-gateway-india.c24.tech',
                'accept': 'application/json, text/plain, */*',
                'x_vehicle_type': 'CAR',
                'x_country': 'IN',
                'source': 'MobileApp',
                'pincode': 'undefined',
                'usercityid': '',
                'appversion': '',
                'osname': 'android',
                'mediasource': 'HELLO_AR',
                'useragent': 'cars24CustomerApp/',
                'userid': 'b6cc0c21-e235-48c0-a429-5ab1ce3b5bbb',
                'x-user-city-id': '',
                'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwaG9uZU51bWJlciI6ODAwNTk3MzkwNiwidXNlcklkIjoiYjZjYzBjMjEtZTIzNS00OGMwLWE0MjktNWFiMWNlM2I1YmJiIiwiZW1haWwiOm51bGwsImlhdCI6MTc0NDc3MzY3MiwiZXhwIjoxNzYwNTg0ODcyfQ.eZWjL4fUEDnoxuA9fd63S6u_zHJ6sLfQrxPP3FPSQdE',
                'content-type': 'application/json',
                'accept-encoding': 'gzip',
                'cookie': '__cf_bm=LCqQzkRgQzVddxTGqHGfS.WzeYB_i.wRcfwf6P6a3Q0-1744776894-*******-4Fe5Vo7EIKVLtwnNXk4SJ_Os4gbr7Zm2CxCY87N3iZz60OpOER_kyZVQdYfvAXKfsF3ggOqPhcz5NHqh6_T8p1Qfsun.Uyf_ikhS2vzu9zc',
                'user-agent': 'okhttp/4.12.0'
            }
            response = requests.post(url, headers=headers, json={}, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 403:
                logger.warning(f"FASTag API access forbidden (403) - service may be restricted")
            else:
                logger.error(f"FASTag API HTTP error: {e}")
            return None
        except Exception as e:
            logger.error(f"FASTag API error: {e}")
            return None

    # Execute all APIs in parallel with ThreadPoolExecutor
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor(max_workers=4) as executor:
        tasks = [
            loop.run_in_executor(executor, call_rc_api, vehicle_number),
            loop.run_in_executor(executor, call_gtplay_api, vehicle_number),
            loop.run_in_executor(executor, call_challan_api, vehicle_number),
            loop.run_in_executor(executor, call_fastag_api, vehicle_number)
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

    return {
        "rc_data": results[0] if not isinstance(results[0], Exception) else None,
        "gtplay_data": results[1] if not isinstance(results[1], Exception) else None,
        "challan_data": results[2] if not isinstance(results[2], Exception) else None,
        "fastag_data": results[3] if not isinstance(results[3], Exception) else None
    }

def format_vehicle_results(vehicle_data, vehicle_number):
    """Format comprehensive vehicle information from multiple APIs"""
    rc_data = vehicle_data.get("rc_data")
    gtplay_data = vehicle_data.get("gtplay_data", {}).get("data", {}) if vehicle_data.get("gtplay_data") else {}
    challan_data = vehicle_data.get("challan_data")
    fastag_data = vehicle_data.get("fastag_data")

    # Helper function to get value with fallback
    def get_value(primary, secondary, key, default="N/A"):
        if primary and primary.get(key):
            return primary.get(key, default)
        elif secondary and secondary.get(key):
            return secondary.get(key, default)
        return default

    # Build comprehensive result
    result = f"🚗 **Vehicle Information**\n\n"

    # Registration Details
    result += f"📋 **Registration Details:**\n"
    result += f"🔢 Registration No : {get_value(rc_data, gtplay_data, 'license_plate', vehicle_number)}\n"
    result += f"📅 Registration Date : {get_value(rc_data, gtplay_data, 'registration_date')}\n"
    result += f"🏛️ RC Status : {get_value(rc_data, gtplay_data, 'rc_status')}\n\n"

    # Owner Information
    result += f"👤 **Owner Information:**\n"
    result += f"👨 Owner Name : {get_value(rc_data, gtplay_data, 'owner_name')}\n"

    if rc_data and rc_data.get('father_name'):
        result += f"👥 Father's Name : {rc_data.get('father_name', 'N/A')}\n"

    if rc_data and rc_data.get('present_address'):
        result += f"📍 Present Address : {rc_data.get('present_address', 'N/A')}\n"

    if rc_data and rc_data.get('permanent_address'):
        result += f"📍 Permanent Address : {rc_data.get('permanent_address', 'N/A')}\n"

    result += f"🔢 Ownership : {get_value(rc_data, gtplay_data, 'owner_count', get_value(rc_data, gtplay_data, 'ownership_desc', 'N/A'))}\n"

    if rc_data and rc_data.get('is_financed') == '1':
        result += f"🏢 Financer : {rc_data.get('financer', 'N/A')}\n"
    else:
        result += f"🏢 Financer : Not Financed\n"

    result += f"\n"

    # Vehicle Specifications
    result += f"🚗 **Vehicle Specifications:**\n"
    result += f"🏭 Maker/Model : {get_value(rc_data, gtplay_data, 'brand_name', get_value(rc_data, gtplay_data, 'maker_model'))}\n"

    if rc_data and rc_data.get('brand_model'):
        result += f"🚙 Body Type : {rc_data.get('brand_model', 'N/A')}\n"
    elif gtplay_data and gtplay_data.get('body_type_desc'):
        result += f"🚙 Body Type : {gtplay_data.get('body_type_desc', 'N/A')}\n"

    result += f"🎨 Color : {get_value(rc_data, gtplay_data, 'color', get_value(rc_data, gtplay_data, 'vehicle_color'))}\n"
    result += f"⛽ Fuel Type : {get_value(rc_data, gtplay_data, 'fuel_type')}\n"
    result += f"🌱 Fuel Norms : {get_value(rc_data, gtplay_data, 'norms', get_value(rc_data, gtplay_data, 'fuel_norms'))}\n"

    if rc_data and rc_data.get('seating_capacity'):
        result += f"💺 Seat Capacity : {rc_data.get('seating_capacity', 'N/A')}\n"
    elif gtplay_data and gtplay_data.get('seat_capacity'):
        result += f"💺 Seat Capacity : {gtplay_data.get('seat_capacity', 'N/A')}\n"

    if gtplay_data and gtplay_data.get('unload_weight'):
        result += f"⚖️ Unload Weight : {gtplay_data.get('unload_weight', 'N/A')} kg\n"

    result += f"🔩 Chassis No : {get_value(rc_data, gtplay_data, 'chassis_number', get_value(rc_data, gtplay_data, 'chassis_no'))}\n"
    result += f"⚙️ Engine No : {get_value(rc_data, gtplay_data, 'engine_number', get_value(rc_data, gtplay_data, 'engine_no'))}\n\n"

    # Compliance Status
    result += f"📋 **Compliance Status:**\n"

    if gtplay_data and gtplay_data.get('fitness_upto'):
        result += f"✅ Fitness Valid Until : {gtplay_data.get('fitness_upto', 'N/A')}\n"

    insurance_date = get_value(rc_data, gtplay_data, 'insurance_expiry', get_value(rc_data, gtplay_data, 'insurance_upto'))
    result += f"🛡️ Insurance Valid Until : {insurance_date}\n"

    insurance_company = get_value(rc_data, gtplay_data, 'insurance_company')
    result += f"🏢 Insurance Company : {insurance_company}\n"

    puc_date = get_value(rc_data, gtplay_data, 'pucc_upto', get_value(rc_data, gtplay_data, 'puc_upto'))
    result += f"🌿 PUC Valid Until : {puc_date}\n"

    if rc_data and rc_data.get('tax_upto'):
        result += f"💰 Tax Paid Until : {rc_data.get('tax_upto', 'N/A')}\n"

    result += f"\n"

    # Traffic Violations (detailed)
    result += f"🚨 **Traffic Violations:**\n"
    if challan_data:
        try:
            challan_info = challan_data.get("data", {}).get("keys", {}).get("flutterMap", {}).get("flutterData", {})
            if isinstance(challan_info, str):
                import json
                challan_info = json.loads(challan_info)

            pending_challans = []
            paid_challans = []

            # Extract detailed challan information
            for section in challan_info.get("sections", []):
                if section.get("id") == "challan_pending":
                    challan_details = {
                        "number": section.get("title", {}).get("text", "N/A"),
                        "offense": "N/A",
                        "location": "N/A",
                        "violator": "N/A",
                        "date": "N/A",
                        "amount": "N/A"
                    }

                    for element in section.get("elements", []):
                        if element.get("id") == "challan_info":
                            challan_details["offense"] = element.get("content", {}).get("title", {}).get("text", "N/A")
                        elif element.get("type") == "PAIR_ELEMENT":
                            challan_details["location"] = element.get("content", {}).get("subtitle", {}).get("text", "N/A")
                        elif element.get("id") == "challan_date":
                            challan_details["violator"] = element.get("content", {}).get("subtitle", {}).get("text", "N/A")
                            for msg in element.get("messages", []):
                                challan_details["date"] = msg.get("subtext", {}).get("text", "N/A")
                        elif element.get("id") == "challan_amount":
                            challan_details["amount"] = element.get("content", {}).get("title", {}).get("text", "N/A")

                    pending_challans.append(challan_details)

                elif section.get("id") == "challan_paid":
                    challan_details = {
                        "number": section.get("title", {}).get("text", "N/A"),
                        "offense": "N/A",
                        "location": "N/A",
                        "violator": "N/A",
                        "date": "N/A",
                        "amount": "N/A",
                        "receipt": "N/A"
                    }

                    for element in section.get("elements", []):
                        if element.get("id") == "challan_info":
                            challan_details["offense"] = element.get("content", {}).get("title", {}).get("text", "N/A")
                        elif element.get("type") == "PAIR_ELEMENT":
                            challan_details["location"] = element.get("content", {}).get("subtitle", {}).get("text", "N/A")
                        elif element.get("id") == "challan_date":
                            challan_details["violator"] = element.get("content", {}).get("subtitle", {}).get("text", "N/A")
                            for msg in element.get("messages", []):
                                challan_details["date"] = msg.get("subtext", {}).get("text", "N/A")
                        elif element.get("id") == "challan_amount":
                            challan_details["amount"] = element.get("content", {}).get("title", {}).get("text", "N/A")
                            for action in element.get("actions", []):
                                if action.get("type") == "REDIRECT":
                                    challan_details["receipt"] = action.get("redirectUrl", "N/A")

                    paid_challans.append(challan_details)

            # Display pending challans
            if pending_challans:
                result += f"⚠️ **Pending Challans ({len(pending_challans)}):**\n"
                for i, challan in enumerate(pending_challans, 1):
                    result += f"   **#{i}** {challan['number']}\n"
                    result += f"   🚫 Offense : {challan['offense']}\n"
                    result += f"   📍 Location : {challan['location']}\n"
                    result += f"   👤 Violator : {challan['violator']}\n"
                    result += f"   📅 Date : {challan['date']}\n"
                    if challan['amount'] != "N/A":
                        result += f"   💰 Amount : {challan['amount']}\n"
                    result += f"\n"
            else:
                result += f"✅ No Pending Challans\n"

            # Display paid/disposed challans
            if paid_challans:
                result += f"📋 **Paid/Disposed Challans ({len(paid_challans)}):**\n"
                for i, challan in enumerate(paid_challans, 1):
                    result += f"   **#{i}** {challan['number']}\n"
                    result += f"   🚫 Offense : {challan['offense']}\n"
                    result += f"   📍 Location : {challan['location']}\n"
                    result += f"   👤 Violator : {challan['violator']}\n"
                    result += f"   📅 Date : {challan['date']}\n"
                    if challan['amount'] != "N/A":
                        result += f"   💰 Amount : {challan['amount']}\n"
                    result += f"   ✅ Status : Paid/Disposed\n"
                    result += f"\n"

        except Exception as e:
            result += f"⚠️ Traffic history temporarily unavailable\n"
    else:
        result += f"⚠️ Traffic history temporarily unavailable\n"

    result += f"\n"

    # FASTag Information
    result += f"💳 **FASTag Information:**\n"
    if fastag_data:
        result += f"👤 Account Holder : {fastag_data.get('customerName', 'N/A')}\n"
        result += f"💰 Current Balance : ₹{float(fastag_data.get('currentBalance', 0)):.2f}\n"

        biller_info = fastag_data.get('billerInfo', {})
        result += f"🏦 Issuer Bank : {biller_info.get('name', 'N/A')}\n"
        result += f"📊 Tag Status : {fastag_data.get('tagStatus', 'N/A')}\n"
    else:
        result += f"⚠️ FASTag details temporarily unavailable\n"

    return result

async def cancel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle conversation cancellation"""
    await update.message.reply_text(
        "❌ **Cancelled**\n\nUse /start to return to main menu.",
        parse_mode=ParseMode.MARKDOWN
    )
    return ConversationHandler.END

async def help_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Show help message"""
    help_text = (
        "🤖 **Bot Commands**\n\n"
        "• /start - Start the bot\n"
        "• /help - Show this help message\n"
        "• /cancel - Cancel current operation\n\n"
        "Use the menu buttons to navigate through the bot features."
    )
    await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)

def get_search_continuation_message(field, cost):
    """Get continuation message for search types"""
    if field == "mobile":
        return (
            f"📱 **Enter Mobile Number:**\n\n"
            f"Please enter the mobile number you want to search for.\n"
            f"💰 **Per Search Cost:** {cost} credits\n\n"
            f"*(without country code +91)*"
        )
    elif field == "id":
        return (
            f"🆔 **Enter Aadhar Number:**\n\n"
            f"Please enter the Aadhar number you want to search for.\n"
            f"💰 **Per Search Cost:** {cost} credits"
        )
    elif field == "alt":
        return (
            f"📞 **Enter Alternate Number:**\n\n"
            f"Please enter the alternate number you want to search for.\n"
            f"💰 **Per Search Cost:** {cost} credits\n\n"
            f"*(without country code +91)*"
        )
    else:
        return f"Please enter the {field} you want to search for."
