# database/models/vehicle.py
import logging
from datetime import datetime, timezone
from database.connection import get_collection
from config.constants import COLLECTIONS

logger = logging.getLogger(__name__)

class VehicleModel:
    """Vehicle model for managing vehicle data"""
    
    def __init__(self):
        self.collection = get_collection(COLLECTIONS['VEHICLE_DATA'])
    
    def get_cached_vehicle(self, vehicle_number):
        """Get cached vehicle data from our database"""
        return self.collection.find_one({"_id": vehicle_number})
    
    def store_vehicle_data(self, vehicle_number, api_data, formatted_result, user_id):
        """Store vehicle data permanently in our database"""
        try:
            # Check if vehicle already exists
            existing = self.collection.find_one({"_id": vehicle_number})

            if existing:
                # Update existing record
                update_data = {
                    "$set": {
                        "last_searched": datetime.now(timezone.utc),
                        "formatted_result": formatted_result
                    },
                    "$inc": {"search_count": 1}
                }

                # Update API data if we got new data
                if api_data.get("rc_data"):
                    update_data["$set"]["rc_data"] = api_data["rc_data"]
                    update_data["$set"]["rc_updated_at"] = datetime.now(timezone.utc)

                if api_data.get("gtplay_data"):
                    update_data["$set"]["gtplay_data"] = api_data["gtplay_data"]
                    update_data["$set"]["gtplay_updated_at"] = datetime.now(timezone.utc)

                if api_data.get("challan_data"):
                    update_data["$set"]["challan_data"] = api_data["challan_data"]
                    update_data["$set"]["challan_updated_at"] = datetime.now(timezone.utc)

                if api_data.get("fastag_data"):
                    update_data["$set"]["fastag_data"] = api_data["fastag_data"]
                    update_data["$set"]["fastag_updated_at"] = datetime.now(timezone.utc)

                # Update data sources list
                current_sources = set(existing.get("data_sources", []))
                new_sources = set([k.replace("_data", "") for k, v in api_data.items() if v])
                all_sources = list(current_sources.union(new_sources))
                update_data["$set"]["data_sources"] = all_sources

                self.collection.update_one({"_id": vehicle_number}, update_data)
                logger.info(f"Updated vehicle data for {vehicle_number}")

            else:
                # Create new record
                vehicle_doc = {
                    "_id": vehicle_number,
                    "vehicle_number": vehicle_number,
                    "rc_data": api_data.get("rc_data"),
                    "gtplay_data": api_data.get("gtplay_data"),
                    "challan_data": api_data.get("challan_data"),
                    "fastag_data": api_data.get("fastag_data"),
                    "first_searched": datetime.now(timezone.utc),
                    "last_searched": datetime.now(timezone.utc),
                    "search_count": 1,
                    "created_by_user": user_id,
                    "data_sources": [k.replace("_data", "") for k, v in api_data.items() if v],
                    "formatted_result": formatted_result
                }

                # Add timestamps for each data source
                if api_data.get("rc_data"):
                    vehicle_doc["rc_updated_at"] = datetime.now(timezone.utc)
                if api_data.get("gtplay_data"):
                    vehicle_doc["gtplay_updated_at"] = datetime.now(timezone.utc)
                if api_data.get("challan_data"):
                    vehicle_doc["challan_updated_at"] = datetime.now(timezone.utc)
                if api_data.get("fastag_data"):
                    vehicle_doc["fastag_updated_at"] = datetime.now(timezone.utc)

                self.collection.insert_one(vehicle_doc)
                logger.info(f"Stored new vehicle data for {vehicle_number}")

            return True

        except Exception as e:
            logger.error(f"Error storing vehicle data: {e}")
            return False
    
    def update_vehicle_search_stats(self, vehicle_number):
        """Update search statistics for cached vehicle"""
        try:
            result = self.collection.update_one(
                {"_id": vehicle_number},
                {
                    "$set": {"last_searched": datetime.now(timezone.utc)},
                    "$inc": {"search_count": 1}
                }
            )
            if result.modified_count > 0:
                logger.info(f"Updated search stats for vehicle {vehicle_number}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating vehicle search stats: {e}")
            return False
    
    def remove_vehicle(self, vehicle_number):
        """Remove vehicle from database"""
        try:
            result = self.collection.delete_one({"_id": vehicle_number})
            if result.deleted_count > 0:
                logger.info(f"Removed vehicle {vehicle_number} from database")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing vehicle: {e}")
            return False
    
    def get_vehicle_database_stats(self):
        """Get statistics about our vehicle database"""
        try:
            total_vehicles = self.collection.count_documents({})

            # Count by data sources
            rc_count = self.collection.count_documents({"rc_data": {"$ne": None}})
            gtplay_count = self.collection.count_documents({"gtplay_data": {"$ne": None}})
            challan_count = self.collection.count_documents({"challan_data": {"$ne": None}})
            fastag_count = self.collection.count_documents({"fastag_data": {"$ne": None}})

            # Most searched vehicles
            top_vehicles = list(self.collection.find({}, {"vehicle_number": 1, "search_count": 1})
                               .sort("search_count", -1).limit(5))

            return {
                "total_vehicles": total_vehicles,
                "rc_data_count": rc_count,
                "gtplay_data_count": gtplay_count,
                "challan_data_count": challan_count,
                "fastag_data_count": fastag_count,
                "top_vehicles": top_vehicles
            }
        except Exception as e:
            logger.error(f"Error getting vehicle database stats: {e}")
            return None
    
    def get_all_vehicles(self, limit=None):
        """Get all vehicles from database"""
        query = self.collection.find({})
        if limit:
            query = query.limit(limit)
        return list(query.sort("last_searched", -1))
    
    def search_vehicles(self, query_text):
        """Search vehicles by number (partial match)"""
        return list(self.collection.find({
            "vehicle_number": {"$regex": query_text, "$options": "i"}
        }).sort("search_count", -1))
    
    def get_vehicles_by_user(self, user_id):
        """Get vehicles searched by a specific user"""
        return list(self.collection.find({
            "created_by_user": user_id
        }).sort("last_searched", -1))
    
    def get_recent_vehicles(self, limit=10):
        """Get recently searched vehicles"""
        return list(self.collection.find({}).sort("last_searched", -1).limit(limit))
    
    def get_popular_vehicles(self, limit=10):
        """Get most searched vehicles"""
        return list(self.collection.find({}).sort("search_count", -1).limit(limit))
    
    def cleanup_old_vehicles(self, days=365):
        """Clean up old vehicle data (optional maintenance)"""
        from datetime import timedelta
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        # Only remove vehicles that haven't been searched recently and have low search count
        result = self.collection.delete_many({
            "last_searched": {"$lt": cutoff_date},
            "search_count": {"$lte": 1}
        })
        
        if result.deleted_count > 0:
            logger.info(f"Cleaned up {result.deleted_count} old vehicle records")
        
        return result.deleted_count
