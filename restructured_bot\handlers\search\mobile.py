# handlers/search/mobile.py
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.search.base_search import <PERSON><PERSON>earchHandler
from config.constants import <PERSON>NU, M<PERSON><PERSON><PERSON>_SEARCH
from services.validation import ValidationService
from database.models import SearchModel, UserModel
from services.formatting import FormattingService

logger = logging.getLogger(__name__)

class MobileSearchHandler(BaseSearchHandler):
    """Handler for mobile number searches"""
    
    def __init__(self):
        super().__init__()
        self.search_model = SearchModel()
        self.user_model = UserModel()  # Fix: Add missing user_model
    
    async def handle_mobile_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle mobile number input with state verification"""
        logger.info(f"Mobile input handler called for user {update.effective_user.id}")
        logger.info(f"Context user_data: {context.user_data}")

        user = await self.check_user_authorization(update, context)
        if not user:
            logger.warning(f"User authorization failed for {update.effective_user.id}")
            return MENU
        
        input_text = update.message.text.strip()
        
        # Validate input
        is_valid, result = ValidationService.validate_mobile_number(input_text)
        if not is_valid:
            await self.send_validation_error(update, context, result)
            return MOBILE_SEARCH
        
        query_text = result
        
        # Enhanced: Check if user can search (LEA expiry check)
        if not self.auth_service.can_search(user):
            expired_msg = self.auth_service.get_expired_message(user)
            await update.message.reply_text(
                expired_msg,
                reply_markup=self.keyboard_builder.main_menu_keyboard(user),
                parse_mode=ParseMode.MARKDOWN
            )
            return MENU

        # Enhanced: Get search cost based on user type and pricing settings
        cost = self.auth_service.get_search_cost("mobile")

        # Enhanced: Check credits (only validate, don't deduct yet)
        if not self.auth_service.is_lea_officer(user):
            has_credits, credit_msg = self.auth_service.check_credits(user, cost)
            if not has_credits:
                await self.send_insufficient_credits(update, context)
                return MENU

        # Enhanced: Check rate limits (LEA officers exempt)
        rate_ok, rate_msg = self.auth_service.check_rate_limit(user)
        if not rate_ok:
            rate_limited_msg = self.message_templates.search_result_rate_limited_message()
            await update.message.reply_text(
                rate_limited_msg,
                reply_markup=self.keyboard_builder.main_menu_keyboard(user),
                parse_mode=ParseMode.MARKDOWN
            )
            return MENU
        
        # Show searching message
        wait_msg = await update.message.reply_text(
            self.message_templates.searching_message(query_text),
            parse_mode=ParseMode.MARKDOWN
        )
        
        try:
            # Perform search
            results = self.search_model.search_data("mobile", query_text)

            # Enhanced: Check if results found (success-based credit deduction)
            if not results:
                # No results found - don't deduct credits
                no_results_msg = self.message_templates.search_result_no_results_message()
                await wait_msg.edit_text(
                    no_results_msg,
                    reply_markup=self.keyboard_builder.no_results_keyboard(),
                    parse_mode=ParseMode.MARKDOWN
                )

                # Enhanced: Log search as failed (no charge)
                self.search_model.log_search(user["user_id"], "mobile", query_text, 0, success=False)
                self.auth_service.update_search_stats(user["user_id"], success=False, credits_charged=cost)
                self.log_user_action(user["user_id"], "mobile_search_no_results", query_text)
                return MENU

            # Enhanced: Results found - now deduct credits
            credits_charged = 0
            if not self.auth_service.is_admin(user) and not self.auth_service.is_lea_officer(user):
                if self.auth_service.deduct_credits(user["user_id"], cost):
                    credits_charged = cost
                    self.auth_service.update_search_activity(user)

            # Enhanced: Log successful search with credit info
            self.search_model.log_search(user["user_id"], "mobile", query_text, len(results), success=True)
            self.auth_service.update_search_stats(user["user_id"], success=True, credits_charged=credits_charged)
            
            # Store results in context for pagination
            context.user_data["mobile_results"] = results
            context.user_data["current_search_field"] = "mobile"
            
            # Show results first (separate message)
            await self.show_paginated_results_universal(context, update, results, 0, "mobile", "results")

            # Then show action buttons (separate message)
            await self.show_search_action_buttons(update, context, "mobile", len(results))

            # Finally show success message (separate message)
            await self.show_search_success_message(update, user, credits_charged, context)

            self.log_user_action(user["user_id"], "mobile_search_success", f"{query_text} ({len(results)} results)")
            return MENU
            
        except Exception as e:
            logger.error(f"Error in mobile search: {e}")
            await wait_msg.edit_text(
                "❌ **Search Error**\n\nAn error occurred while searching. Please try again.",
                reply_markup=self.keyboard_builder.search_input_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )
            return MENU
    
    async def handle_pagination(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page: int):
        """Handle pagination using universal method"""
        return await self.handle_pagination_universal(update, context, page, "mobile", "mobile_results")
    

    
    async def show_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main menu (imported from start handler)"""
        from handlers.start import StartHandler
        start_handler = StartHandler()
        return await start_handler.show_main_menu(update, context)
