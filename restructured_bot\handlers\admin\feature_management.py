# handlers/admin/feature_management.py
import logging
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.base import <PERSON>Handler
from config.constants import ADMIN_PANEL
from database.models import SettingsModel

logger = logging.getLogger(__name__)

class FeatureManagementHandler(BaseHandler):
    """Handler for feature visibility management"""
    
    def __init__(self):
        super().__init__()
        self.settings_model = SettingsModel()
    
    async def show_feature_matrix(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show feature visibility matrix"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        # Get feature matrix
        matrix = self.settings_model.get_feature_visibility_matrix()
        
        # Build matrix display with emojis
        text = (
            f"🎯 **Feature Visibility Matrix**\n\n"
            f"```\n"
            f"┌─────────┬───────┬─────┬────────┬───────┐\n"
            f"│ Feature │Global │ LEA │ Normal │Result │\n"
            f"├─────────┼───────┼─────┼────────┼───────┤\n"
        )
        
        for feature, data in matrix.items():
            feature_name = feature.capitalize()[:7].ljust(7)
            global_icon = "✅" if data["global"] else "❌"
            lea_icon = "✅" if data["lea"] else "❌"
            normal_icon = "✅" if data["normal"] else "❌"
            
            # Final result for each user type
            lea_final = "✅" if data["lea_final"] else "❌"
            normal_final = "✅" if data["normal_final"] else "❌"
            
            text += f"│{feature_name}│  {global_icon}   │ {lea_icon}  │   {normal_icon}   │L:{lea_final} N:{normal_final}│\n"
        
        text += (
            f"└─────────┴───────┴─────┴────────┴───────┘\n"
            f"```\n\n"
            f"**Legend:**\n"
            f"• Global: Master switch for feature\n"
            f"• LEA: LEA officer specific setting\n"
            f"• Normal: Normal user specific setting\n"
            f"• Result: Final availability (L=LEA, N=Normal)"
        )
        
        keyboard = self.keyboard_builder.admin_features_keyboard()
        
        try:
            await query.edit_message_text(
                text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            if "Message is not modified" in str(e):
                await query.answer("Feature panel refreshed")
            else:
                raise e
        
        self.log_admin_action(user["user_id"], "view_feature_matrix")
        return ADMIN_PANEL
    
    async def toggle_global_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show global feature toggles"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()

        # CRITICAL: Set conversation state to ensure callbacks are routed to admin panel
        context.user_data["conversation_state"] = "ADMIN_PANEL"
        
        # Get current global settings
        mobile_enabled = self.settings_model.get_setting("mobile_search_enabled", True)
        aadhar_enabled = self.settings_model.get_setting("aadhar_search_enabled", True)
        alt_enabled = self.settings_model.get_setting("alternate_search_enabled", True)
        vehicle_enabled = self.settings_model.get_setting("vehicle_search_enabled", True)
        
        text = (
            f"🌐 **Global Feature Control**\n\n"
            f"Master switches that affect all users:\n\n"
            f"📱 Mobile Search: {'✅ Enabled' if mobile_enabled else '❌ Disabled'}\n"
            f"🆔 ID Search: {'✅ Enabled' if aadhar_enabled else '❌ Disabled'}\n"
            f"📞 Alternative Search: {'✅ Enabled' if alt_enabled else '❌ Disabled'}\n"
            f"🚗 Vehicle Search: {'✅ Enabled' if vehicle_enabled else '❌ Disabled'}\n\n"
            f"*Note: Disabling globally will hide feature for all users*"
        )
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton(
                    f"📱 {'Disable' if mobile_enabled else 'Enable'} Mobile",
                    callback_data="toggle_global_mobile"
                ),
                InlineKeyboardButton(
                    f"🆔 {'Disable' if aadhar_enabled else 'Enable'} ID",
                    callback_data="toggle_global_aadhar"
                )
            ],
            [
                InlineKeyboardButton(
                    f"📞 {'Disable' if alt_enabled else 'Enable'} Alt",
                    callback_data="toggle_global_alternate"
                ),
                InlineKeyboardButton(
                    f"🚗 {'Disable' if vehicle_enabled else 'Enable'} Vehicle",
                    callback_data="toggle_global_vehicle"
                )
            ],
            [
                # IMPROVEMENT: Single toggle button instead of two separate buttons
                InlineKeyboardButton(
                    f"🔄 {'Disable' if self.settings_model.are_all_global_features_enabled() else 'Enable'} All Global",
                    callback_data="toggle_all_global"
                )
            ],
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_features"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_PANEL
    
    async def toggle_lea_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show LEA feature toggles"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()

        # CRITICAL: Set conversation state to ensure callbacks are routed to admin panel
        context.user_data["conversation_state"] = "ADMIN_PANEL"
        
        # Get current LEA settings
        mobile_enabled = self.settings_model.get_setting("lea_mobile_search_enabled", True)
        aadhar_enabled = self.settings_model.get_setting("lea_aadhar_search_enabled", True)
        alt_enabled = self.settings_model.get_setting("lea_alternate_search_enabled", True)
        vehicle_enabled = self.settings_model.get_setting("lea_vehicle_search_enabled", True)
        
        text = (
            f"👮 **LEA Officer Features**\n\n"
            f"Control features available to LEA officers:\n\n"
            f"📱 Mobile Search: {'✅ Enabled' if mobile_enabled else '❌ Disabled'}\n"
            f"🆔 ID Search: {'✅ Enabled' if aadhar_enabled else '❌ Disabled'}\n"
            f"📞 Alternative Search: {'✅ Enabled' if alt_enabled else '❌ Disabled'}\n"
            f"🚗 Vehicle Search: {'✅ Enabled' if vehicle_enabled else '❌ Disabled'}\n\n"
            f"*Note: Global settings must also be enabled*"
        )
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton(
                    f"📱 {'Disable' if mobile_enabled else 'Enable'} Mobile",
                    callback_data="toggle_lea_mobile"
                ),
                InlineKeyboardButton(
                    f"🆔 {'Disable' if aadhar_enabled else 'Enable'} ID",
                    callback_data="toggle_lea_aadhar"
                )
            ],
            [
                InlineKeyboardButton(
                    f"📞 {'Disable' if alt_enabled else 'Enable'} Alt",
                    callback_data="toggle_lea_alternate"
                ),
                InlineKeyboardButton(
                    f"🚗 {'Disable' if vehicle_enabled else 'Enable'} Vehicle",
                    callback_data="toggle_lea_vehicle"
                )
            ],
            [
                # IMPROVEMENT 1: Single toggle button instead of two separate buttons
                InlineKeyboardButton(
                    f"🔄 {'Disable' if self.settings_model.are_all_lea_features_enabled() else 'Enable'} All LEA",
                    callback_data="toggle_all_lea"
                )
            ],
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_features"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_PANEL
    
    async def toggle_normal_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show normal user feature toggles"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()

        # CRITICAL: Set conversation state to ensure callbacks are routed to admin panel
        context.user_data["conversation_state"] = "ADMIN_PANEL"
        
        # Get current normal user settings
        mobile_enabled = self.settings_model.get_setting("normal_mobile_search_enabled", True)
        aadhar_enabled = self.settings_model.get_setting("normal_aadhar_search_enabled", True)
        alt_enabled = self.settings_model.get_setting("normal_alternate_search_enabled", True)
        vehicle_enabled = self.settings_model.get_setting("normal_vehicle_search_enabled", True)
        
        text = (
            f"👤 **Normal User Features**\n\n"
            f"Control features available to normal users:\n\n"
            f"📱 Mobile Search: {'✅ Enabled' if mobile_enabled else '❌ Disabled'}\n"
            f"🆔 ID Search: {'✅ Enabled' if aadhar_enabled else '❌ Disabled'}\n"
            f"📞 Alternative Search: {'✅ Enabled' if alt_enabled else '❌ Disabled'}\n"
            f"🚗 Vehicle Search: {'✅ Enabled' if vehicle_enabled else '❌ Disabled'}\n\n"
            f"*Note: Global settings must also be enabled*"
        )
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton(
                    f"📱 {'Disable' if mobile_enabled else 'Enable'} Mobile",
                    callback_data="toggle_normal_mobile"
                ),
                InlineKeyboardButton(
                    f"🆔 {'Disable' if aadhar_enabled else 'Enable'} ID",
                    callback_data="toggle_normal_aadhar"
                )
            ],
            [
                InlineKeyboardButton(
                    f"📞 {'Disable' if alt_enabled else 'Enable'} Alt",
                    callback_data="toggle_normal_alternate"
                ),
                InlineKeyboardButton(
                    f"🚗 {'Disable' if vehicle_enabled else 'Enable'} Vehicle",
                    callback_data="toggle_normal_vehicle"
                )
            ],
            [
                # IMPROVEMENT 1: Single toggle button instead of two separate buttons
                InlineKeyboardButton(
                    f"🔄 {'Enable' if self.settings_model.are_all_normal_features_disabled() else 'Disable'} All Normal",
                    callback_data="toggle_all_normal"
                )
            ],
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_features"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_PANEL
    
    async def handle_feature_toggle(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle feature toggle callbacks"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        callback_data = query.data
        logger.info(f"FEATURE_TOGGLE: Processing callback {callback_data}")
        
        # Map callback data to settings
        toggle_map = {
            # Global toggles
            "toggle_global_mobile": "mobile_search_enabled",
            "toggle_global_aadhar": "aadhar_search_enabled",
            "toggle_global_alternate": "alternate_search_enabled",
            "toggle_global_vehicle": "vehicle_search_enabled",
            
            # LEA toggles
            "toggle_lea_mobile": "lea_mobile_search_enabled",
            "toggle_lea_aadhar": "lea_aadhar_search_enabled",
            "toggle_lea_alternate": "lea_alternate_search_enabled",
            "toggle_lea_vehicle": "lea_vehicle_search_enabled",
            
            # Normal user toggles
            "toggle_normal_mobile": "normal_mobile_search_enabled",
            "toggle_normal_aadhar": "normal_aadhar_search_enabled",
            "toggle_normal_alternate": "normal_alternate_search_enabled",
            "toggle_normal_vehicle": "normal_vehicle_search_enabled"
        }
        
        setting_key = toggle_map.get(callback_data)
        if setting_key:
            current_value = self.settings_model.get_setting(setting_key, True)
            new_value = not current_value
            self.settings_model.set_setting(setting_key, new_value)
            
            feature_name = setting_key.replace("_enabled", "").replace("_", " ").title()
            status = "enabled" if new_value else "disabled"
            
            await query.answer(f"{feature_name} {status}")
            self.log_admin_action(user["user_id"], "toggle_feature", f"{feature_name} -> {status}")
            
            # Refresh the current view
            if "global" in callback_data:
                return await self.toggle_global_features(update, context)
            elif "lea" in callback_data:
                return await self.toggle_lea_features(update, context)
            elif "normal" in callback_data:
                return await self.toggle_normal_features(update, context)

        # Handle bulk operations
        if callback_data in ["enable_all_global", "disable_all_global"]:
            enable = callback_data == "enable_all_global"
            features = ["mobile_search_enabled", "aadhar_search_enabled", "alternate_search_enabled", "vehicle_search_enabled"]
            
            for feature in features:
                self.settings_model.set_setting(feature, enable)
            
            await query.answer(f"All global features {'enabled' if enable else 'disabled'}")
            self.log_admin_action(user["user_id"], "bulk_toggle_global", f"All -> {'enabled' if enable else 'disabled'}")
            return await self.toggle_global_features(update, context)
        
        if callback_data in ["enable_all_lea", "disable_all_lea"]:
            enable = callback_data == "enable_all_lea"
            features = ["lea_mobile_search_enabled", "lea_aadhar_search_enabled", "lea_alternate_search_enabled", "lea_vehicle_search_enabled"]
            
            for feature in features:
                self.settings_model.set_setting(feature, enable)
            
            await query.answer(f"All LEA features {'enabled' if enable else 'disabled'}")
            self.log_admin_action(user["user_id"], "bulk_toggle_lea", f"All -> {'enabled' if enable else 'disabled'}")
            return await self.toggle_lea_features(update, context)
        
        if callback_data in ["enable_all_normal", "disable_all_normal"]:
            enable = callback_data == "enable_all_normal"
            features = ["normal_mobile_search_enabled", "normal_aadhar_search_enabled", "normal_alternate_search_enabled", "normal_vehicle_search_enabled"]
            
            for feature in features:
                self.settings_model.set_setting(feature, enable)
            
            await query.answer(f"All normal user features {'enabled' if enable else 'disabled'}")
            self.log_admin_action(user["user_id"], "bulk_toggle_normal", f"All -> {'enabled' if enable else 'disabled'}")
            return await self.toggle_normal_features(update, context)

        # If no callback matched, show error and return to admin panel
        await query.answer(f"❌ Unknown feature action: {callback_data}")
        return ADMIN_PANEL

    async def handle_feature_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle feature management callbacks"""
        query = update.callback_query
        data = query.data
        logger.info(f"FEATURE_CALLBACK: Processing callback {data}")

        if data == "admin_global_features":
            return await self.toggle_global_features(update, context)
        elif data == "admin_lea_features":
            return await self.toggle_lea_features(update, context)
        elif data == "admin_normal_features":
            return await self.toggle_normal_features(update, context)
        elif data == "admin_enable_all_lea":
            return await self.handle_admin_enable_all_lea(update, context)
        elif data == "admin_disable_all_normal":
            return await self.handle_admin_disable_all_normal(update, context)
        elif data == "admin_reset_features":
            return await self.handle_admin_reset_features(update, context)
        elif data == "admin_individual_features":
            return await self.show_individual_features(update, context)
        # IMPROVEMENT 2: Add new toggle callback handlers
        elif data == "admin_toggle_all_lea":
            return await self.handle_admin_toggle_all_lea(update, context)
        elif data == "admin_toggle_all_normal":
            return await self.handle_admin_toggle_all_normal(update, context)
        elif data == "toggle_all_lea":
            return await self.handle_toggle_all_lea(update, context)
        elif data == "toggle_all_normal":
            return await self.handle_toggle_all_normal(update, context)
        elif data == "toggle_all_global":
            return await self.handle_toggle_all_global(update, context)
        elif data.startswith("toggle_"):
            return await self.handle_feature_toggle(update, context)
        elif data.startswith("enable_all_"):
            return await self.handle_enable_all(update, context)
        elif data.startswith("disable_all_"):
            return await self.handle_disable_all(update, context)
        else:
            await query.answer("Unknown feature action")
            return ADMIN_PANEL

    async def handle_admin_enable_all_lea(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Enable all LEA features from main menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        # Enable all LEA features
        features = [
            "lea_mobile_search_enabled",
            "lea_aadhar_search_enabled",
            "lea_alternate_search_enabled",
            "lea_vehicle_search_enabled"
        ]

        for feature in features:
            self.settings_model.set_setting(feature, True)

        await query.answer("✅ All LEA features enabled")
        self.log_admin_action(user["user_id"], "bulk_enable_lea", "All LEA features enabled")
        return await self.show_admin_features(update, context)

    async def handle_admin_disable_all_normal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Disable all normal user features from main menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        # Disable all normal user features
        features = [
            "normal_mobile_search_enabled",
            "normal_aadhar_search_enabled",
            "normal_alternate_search_enabled",
            "normal_vehicle_search_enabled"
        ]

        for feature in features:
            self.settings_model.set_setting(feature, False)

        await query.answer("❌ All normal user features disabled")
        self.log_admin_action(user["user_id"], "bulk_disable_normal", "All normal features disabled")
        return await self.show_admin_features(update, context)

    async def handle_admin_reset_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Reset all features to defaults"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        # Reset all features to default (enabled)
        all_features = [
            # Global features
            "mobile_search_enabled", "aadhar_search_enabled",
            "alternate_search_enabled", "vehicle_search_enabled",
            # LEA features
            "lea_mobile_search_enabled", "lea_aadhar_search_enabled",
            "lea_alternate_search_enabled", "lea_vehicle_search_enabled",
            # Normal user features
            "normal_mobile_search_enabled", "normal_aadhar_search_enabled",
            "normal_alternate_search_enabled", "normal_vehicle_search_enabled"
        ]

        for feature in all_features:
            self.settings_model.set_setting(feature, True)

        await query.answer("🔄 All features reset to defaults (enabled)")
        self.log_admin_action(user["user_id"], "reset_features", "All features reset to defaults")
        return await self.show_admin_features(update, context)

    async def show_individual_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show individual feature management"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        # CRITICAL: Set conversation state to ensure callbacks are routed to admin panel
        context.user_data["conversation_state"] = "ADMIN_PANEL"

        # Get current feature states
        mobile_enabled = self.settings_model.get_setting("mobile_search_enabled", True)
        aadhar_enabled = self.settings_model.get_setting("aadhar_search_enabled", True)
        alt_enabled = self.settings_model.get_setting("alternate_search_enabled", True)
        vehicle_enabled = self.settings_model.get_setting("vehicle_search_enabled", True)

        text = "⚙️ **Individual Feature Control**\n\n"
        text += "Toggle individual features on/off:\n\n"
        text += f"📱 Mobile Search: {'✅ Enabled' if mobile_enabled else '❌ Disabled'}\n"
        text += f"🆔 AADHAR Search: {'✅ Enabled' if aadhar_enabled else '❌ Disabled'}\n"
        text += f"📞 Alternate Search: {'✅ Enabled' if alt_enabled else '❌ Disabled'}\n"
        text += f"🚗 Vehicle Search: {'✅ Enabled' if vehicle_enabled else '❌ Disabled'}\n"

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton(
                    f"📱 {'Disable' if mobile_enabled else 'Enable'} Mobile",
                    callback_data="toggle_global_mobile"
                ),
                InlineKeyboardButton(
                    f"🆔 {'Disable' if aadhar_enabled else 'Enable'} ID",
                    callback_data="toggle_global_aadhar"
                )
            ],
            [
                InlineKeyboardButton(
                    f"📞 {'Disable' if alt_enabled else 'Enable'} Alt",
                    callback_data="toggle_global_alternate"
                ),
                InlineKeyboardButton(
                    f"🚗 {'Disable' if vehicle_enabled else 'Enable'} Vehicle",
                    callback_data="toggle_global_vehicle"
                )
            ],
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_features"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])

        await query.edit_message_text(text, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL

    async def show_admin_features(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main admin features menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        # CRITICAL: Set conversation state to ensure callbacks are routed to admin panel
        context.user_data["conversation_state"] = "ADMIN_PANEL"

        # IMPROVEMENT: Include feature matrix in main menu for better UX
        matrix = self.settings_model.get_feature_visibility_matrix()

        text = "🎯 **Feature Settings**\n\n"

        # Add compact feature matrix
        text += "📊 **Current Status:**\n"
        text += "```\n"
        text += "Feature    │ Global │ LEA │ Normal\n"
        text += "───────────┼────────┼─────┼───────\n"

        for feature, data in matrix.items():
            feature_name = feature.capitalize()[:9].ljust(9)
            global_icon = "✅" if data["global"] else "❌"
            lea_icon = "✅" if data["lea_final"] else "❌"
            normal_icon = "✅" if data["normal_final"] else "❌"
            text += f"{feature_name} │   {global_icon}   │ {lea_icon}  │  {normal_icon}\n"

        text += "```\n\n"
        text += "Choose an option below:"

        keyboard = self.keyboard_builder.admin_features_keyboard()

        await query.edit_message_text(text, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL

    # IMPROVEMENT 2: Add missing toggle handler methods
    async def handle_admin_toggle_all_lea(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Toggle all LEA features from main menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query

        # Check current state and toggle
        if self.settings_model.are_all_lea_features_enabled():
            # Disable all LEA features
            features = ["lea_mobile_search_enabled", "lea_aadhar_search_enabled",
                       "lea_alternate_search_enabled", "lea_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, False)
            await query.answer("❌ All LEA features disabled")
            self.log_admin_action(user["user_id"], "bulk_disable_lea", "All LEA features disabled")
        else:
            # Enable all LEA features
            features = ["lea_mobile_search_enabled", "lea_aadhar_search_enabled",
                       "lea_alternate_search_enabled", "lea_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, True)
            await query.answer("✅ All LEA features enabled")
            self.log_admin_action(user["user_id"], "bulk_enable_lea", "All LEA features enabled")

        return await self.show_admin_features(update, context)

    async def handle_admin_toggle_all_normal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Toggle all normal user features from main menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query

        # Check current state and toggle
        if self.settings_model.are_all_normal_features_disabled():
            # Enable all normal features
            features = ["normal_mobile_search_enabled", "normal_aadhar_search_enabled",
                       "normal_alternate_search_enabled", "normal_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, True)
            await query.answer("✅ All normal user features enabled")
            self.log_admin_action(user["user_id"], "bulk_enable_normal", "All normal features enabled")
        else:
            # Disable all normal features
            features = ["normal_mobile_search_enabled", "normal_aadhar_search_enabled",
                       "normal_alternate_search_enabled", "normal_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, False)
            await query.answer("❌ All normal user features disabled")
            self.log_admin_action(user["user_id"], "bulk_disable_normal", "All normal features disabled")

        return await self.show_admin_features(update, context)

    async def handle_toggle_all_lea(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Toggle all LEA features from LEA features menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query

        # Check current state and toggle
        if self.settings_model.are_all_lea_features_enabled():
            # Disable all LEA features
            features = ["lea_mobile_search_enabled", "lea_aadhar_search_enabled",
                       "lea_alternate_search_enabled", "lea_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, False)
            await query.answer("❌ All LEA features disabled")
            self.log_admin_action(user["user_id"], "bulk_disable_lea", "All LEA features disabled")
        else:
            # Enable all LEA features
            features = ["lea_mobile_search_enabled", "lea_aadhar_search_enabled",
                       "lea_alternate_search_enabled", "lea_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, True)
            await query.answer("✅ All LEA features enabled")
            self.log_admin_action(user["user_id"], "bulk_enable_lea", "All LEA features enabled")

        return await self.toggle_lea_features(update, context)

    async def handle_toggle_all_normal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Toggle all normal user features from normal features menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query

        # Check current state and toggle
        if self.settings_model.are_all_normal_features_disabled():
            # Enable all normal features
            features = ["normal_mobile_search_enabled", "normal_aadhar_search_enabled",
                       "normal_alternate_search_enabled", "normal_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, True)
            await query.answer("✅ All normal user features enabled")
            self.log_admin_action(user["user_id"], "bulk_enable_normal", "All normal features enabled")
        else:
            # Disable all normal features
            features = ["normal_mobile_search_enabled", "normal_aadhar_search_enabled",
                       "normal_alternate_search_enabled", "normal_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, False)
            await query.answer("❌ All normal user features disabled")
            self.log_admin_action(user["user_id"], "bulk_disable_normal", "All normal features disabled")

        return await self.toggle_normal_features(update, context)

    async def handle_toggle_all_global(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Toggle all global features from global features menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query

        # Check current state and toggle
        if self.settings_model.are_all_global_features_enabled():
            # Disable all global features
            features = ["mobile_search_enabled", "aadhar_search_enabled",
                       "alternate_search_enabled", "vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, False)
            await query.answer("❌ All global features disabled")
            self.log_admin_action(user["user_id"], "bulk_disable_global", "All global features disabled")
        else:
            # Enable all global features
            features = ["mobile_search_enabled", "aadhar_search_enabled",
                       "alternate_search_enabled", "vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, True)
            await query.answer("✅ All global features enabled")
            self.log_admin_action(user["user_id"], "bulk_enable_global", "All global features enabled")

        return await self.toggle_global_features(update, context)

    # IMPROVEMENT 2: Add missing bulk toggle methods
    async def handle_enable_all(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle enable_all_* callbacks"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        data = query.data

        if data == "enable_all_global":
            # Enable all global features
            features = ["mobile_search_enabled", "aadhar_search_enabled",
                       "alternate_search_enabled", "vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, True)
            await query.answer("✅ All global features enabled")
            self.log_admin_action(user["user_id"], "bulk_enable_global", "All global features enabled")
            return await self.toggle_global_features(update, context)

        elif data == "enable_all_lea":
            # Enable all LEA features
            features = ["lea_mobile_search_enabled", "lea_aadhar_search_enabled",
                       "lea_alternate_search_enabled", "lea_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, True)
            await query.answer("✅ All LEA features enabled")
            self.log_admin_action(user["user_id"], "bulk_enable_lea", "All LEA features enabled")
            return await self.toggle_lea_features(update, context)

        elif data == "enable_all_normal":
            # Enable all normal features
            features = ["normal_mobile_search_enabled", "normal_aadhar_search_enabled",
                       "normal_alternate_search_enabled", "normal_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, True)
            await query.answer("✅ All normal features enabled")
            self.log_admin_action(user["user_id"], "bulk_enable_normal", "All normal features enabled")
            return await self.toggle_normal_features(update, context)

        return ADMIN_PANEL

    async def handle_disable_all(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle disable_all_* callbacks"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        data = query.data

        if data == "disable_all_global":
            # Disable all global features
            features = ["mobile_search_enabled", "aadhar_search_enabled",
                       "alternate_search_enabled", "vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, False)
            await query.answer("❌ All global features disabled")
            self.log_admin_action(user["user_id"], "bulk_disable_global", "All global features disabled")
            return await self.toggle_global_features(update, context)

        elif data == "disable_all_lea":
            # Disable all LEA features
            features = ["lea_mobile_search_enabled", "lea_aadhar_search_enabled",
                       "lea_alternate_search_enabled", "lea_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, False)
            await query.answer("❌ All LEA features disabled")
            self.log_admin_action(user["user_id"], "bulk_disable_lea", "All LEA features disabled")
            return await self.toggle_lea_features(update, context)

        elif data == "disable_all_normal":
            # Disable all normal features
            features = ["normal_mobile_search_enabled", "normal_aadhar_search_enabled",
                       "normal_alternate_search_enabled", "normal_vehicle_search_enabled"]
            for feature in features:
                self.settings_model.set_setting(feature, False)
            await query.answer("❌ All normal features disabled")
            self.log_admin_action(user["user_id"], "bulk_disable_normal", "All normal features disabled")
            return await self.toggle_normal_features(update, context)

        return ADMIN_PANEL
