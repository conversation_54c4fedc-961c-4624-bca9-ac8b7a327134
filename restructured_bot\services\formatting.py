# services/formatting.py
import logging
from config.constants import DISPLAY_FIELDS

logger = logging.getLogger(__name__)

class FormattingService:
    """Service for formatting search results and messages"""
    
    @staticmethod
    def escape_markdown(text):
        """Escape special Markdown characters"""
        if not text:
            return text
        # Escape Markdown special characters
        special_chars = ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
        for char in special_chars:
            text = str(text).replace(char, f'\\{char}')
        return text
    
    @staticmethod
    def format_search_result(item):
        """Format search result with standardized display format"""
        formatted = []

        for field_name, emoji, display_name in DISPLAY_FIELDS:
            value = item.get(field_name)
            if value:
                # Handle special field mappings
                if field_name == "careof" and not value and item.get("fname"):
                    value = item.get("fname")
                elif field_name == "alt" and not value and item.get("AlternateNo"):
                    value = item.get("AlternateNo")
                
                if value:
                    escaped_value = FormattingService.escape_markdown(str(value))
                    formatted.append(f"{emoji} **{display_name}:** {escaped_value}")

        return "\n".join(formatted) if formatted else "No data available"

    @staticmethod
    def format_search_results_page(results, search_type, query, current_page, total_pages, total_results):
        """Enhanced format search results with page-based navigation"""
        if not results:
            return f"❌ No results found for {search_type}: {query}"

        formatted = f"📄 **Page {current_page} of {total_pages}**\n\n"

        for i, result in enumerate(results, 1):
            formatted += f"📋 **Result #{i}**\n"
            formatted += FormattingService.format_search_result(result)

            if i < len(results):  # Don't add separator after last result
                formatted += "\n" + "─" * 30 + "\n\n"

        return formatted

    @staticmethod
    def format_search_results(results, search_type, query):
        """Legacy format method for backward compatibility"""
        return FormattingService.format_search_results_page(results, search_type, query, 1, 1, len(results))

    @staticmethod
    def format_vehicle_results(vehicle_data, vehicle_number):
        """Format comprehensive vehicle information from multiple APIs"""
        rc_data = vehicle_data.get("rc_data")
        gtplay_data = vehicle_data.get("gtplay_data", {}).get("data", {}) if vehicle_data.get("gtplay_data") else {}
        challan_data = vehicle_data.get("challan_data")
        fastag_data = vehicle_data.get("fastag_data")

        # Helper function to get value with fallback
        def get_value(primary, secondary, key, default="N/A"):
            if primary and primary.get(key):
                return primary.get(key, default)
            elif secondary and secondary.get(key):
                return secondary.get(key, default)
            return default

        def escape_value(value):
            return FormattingService.escape_markdown(str(value)) if value != "N/A" else "N/A"

        # Build formatted result
        result_parts = [f"🚗 **Vehicle Information: {FormattingService.escape_markdown(vehicle_number)}**\n"]

        # Owner Information
        owner_name = get_value(rc_data, gtplay_data, 'owner_name')
        if owner_name != "N/A":
            result_parts.append(f"👤 **Owner Name:** {escape_value(owner_name)}")

        father_name = rc_data.get('father_name') if rc_data else "N/A"
        if father_name != "N/A":
            result_parts.append(f"👥 **Father's Name:** {escape_value(father_name)}")

        # Address Information
        present_address = rc_data.get('present_address') if rc_data else "N/A"
        if present_address != "N/A":
            result_parts.append(f"📍 **Present Address:** {escape_value(present_address)}")

        permanent_address = rc_data.get('permanent_address') if rc_data else "N/A"
        if permanent_address != "N/A":
            result_parts.append(f"🏠 **Permanent Address:** {escape_value(permanent_address)}")

        # Vehicle Details
        brand_name = get_value(rc_data, gtplay_data, 'brand_name', get_value(rc_data, gtplay_data, 'maker_model'))
        if brand_name != "N/A":
            result_parts.append(f"🏭 **Brand/Make:** {escape_value(brand_name)}")

        model = rc_data.get('brand_model', gtplay_data.get('body_type_desc', 'N/A'))
        if model != "N/A":
            result_parts.append(f"🚙 **Model:** {escape_value(model)}")

        color = get_value(rc_data, gtplay_data, 'color', get_value(rc_data, gtplay_data, 'vehicle_color'))
        if color != "N/A":
            result_parts.append(f"🎨 **Color:** {escape_value(color)}")

        fuel_type = get_value(rc_data, gtplay_data, 'fuel_type')
        if fuel_type != "N/A":
            result_parts.append(f"⛽ **Fuel Type:** {escape_value(fuel_type)}")

        # Registration Details
        reg_date = get_value(rc_data, gtplay_data, 'registration_date')
        if reg_date != "N/A":
            result_parts.append(f"📅 **Registration Date:** {escape_value(reg_date)}")

        rc_status = get_value(rc_data, gtplay_data, 'rc_status')
        if rc_status != "N/A":
            result_parts.append(f"📋 **RC Status:** {escape_value(rc_status)}")

        # Technical Details
        chassis_no = get_value(rc_data, gtplay_data, 'chassis_number', get_value(rc_data, gtplay_data, 'chassis_no'))
        if chassis_no != "N/A":
            result_parts.append(f"🔧 **Chassis No:** {escape_value(chassis_no)}")

        engine_no = get_value(rc_data, gtplay_data, 'engine_number', get_value(rc_data, gtplay_data, 'engine_no'))
        if engine_no != "N/A":
            result_parts.append(f"⚙️ **Engine No:** {escape_value(engine_no)}")

        # Insurance & PUC
        insurance_company = get_value(rc_data, gtplay_data, 'insurance_company')
        if insurance_company != "N/A":
            result_parts.append(f"🛡️ **Insurance Company:** {escape_value(insurance_company)}")

        insurance_expiry = get_value(rc_data, gtplay_data, 'insurance_expiry', get_value(rc_data, gtplay_data, 'insurance_upto'))
        if insurance_expiry != "N/A":
            result_parts.append(f"📅 **Insurance Expiry:** {escape_value(insurance_expiry)}")

        puc_expiry = get_value(rc_data, gtplay_data, 'pucc_upto', get_value(rc_data, gtplay_data, 'puc_upto'))
        if puc_expiry != "N/A":
            result_parts.append(f"🌿 **PUC Expiry:** {escape_value(puc_expiry)}")

        # Challan Information
        if challan_data and isinstance(challan_data, dict):
            challan_count = challan_data.get('total_challans', 0)
            if challan_count > 0:
                result_parts.append(f"🚨 **Total Challans:** {challan_count}")
                total_amount = challan_data.get('total_amount', 0)
                if total_amount > 0:
                    result_parts.append(f"💰 **Total Challan Amount:** ₹{total_amount}")

        # FASTag Information
        if fastag_data and isinstance(fastag_data, dict):
            fastag_status = fastag_data.get('status', 'N/A')
            if fastag_status != "N/A":
                result_parts.append(f"🏷️ **FASTag Status:** {escape_value(fastag_status)}")

        return "\n".join(result_parts)
    
    @staticmethod
    def format_user_info(user_info):
        """Format user information for display"""
        if not user_info:
            return "User information not available"
        
        lines = [
            f"👤 **User ID:** {user_info['user_id']}",
            f"📝 **Username:** {FormattingService.escape_markdown(user_info['username'])}",
            f"💳 **Credits:** {user_info['credits']}",
            f"📋 **Plan:** {user_info['plan']}",
            f"📅 **Expiry:** {user_info['expiry']}",
            f"🔍 **Searches Today:** {user_info['search_count_today']}"
        ]
        
        if user_info['is_admin']:
            lines.append("⚙️ **Role:** Administrator")
        
        if user_info['is_suspended']:
            lines.append("🚫 **Status:** Suspended")
        
        return "\n".join(lines)
    
    @staticmethod
    def format_search_stats(stats):
        """Format search statistics"""
        if not stats:
            return "Statistics not available"
        
        lines = [
            f"📊 **Database Statistics**\n",
            f"📱 **Userdata Records:** {stats.get('userdata_count', 0):,}",
            f"📋 **Raj Data Records:** {stats.get('raj_data_count', 0):,}",
            f"📈 **Total Records:** {stats.get('total_records', 0):,}",
            f"🙈 **Hidden Records:** {stats.get('hidden_records_count', 0):,}"
        ]
        
        return "\n".join(lines)
    
    @staticmethod
    def format_vehicle_stats(stats):
        """Format vehicle database statistics"""
        if not stats:
            return "Vehicle statistics not available"
        
        lines = [
            f"🚗 **Vehicle Database Statistics**\n",
            f"📊 **Total Vehicles:** {stats.get('total_vehicles', 0):,}",
            f"📋 **RC Data:** {stats.get('rc_data_count', 0):,}",
            f"🎮 **GTPlay Data:** {stats.get('gtplay_data_count', 0):,}",
            f"🚨 **Challan Data:** {stats.get('challan_data_count', 0):,}",
            f"🏷️ **FASTag Data:** {stats.get('fastag_data_count', 0):,}"
        ]
        
        top_vehicles = stats.get('top_vehicles', [])
        if top_vehicles:
            lines.append("\n🔥 **Most Searched:**")
            for i, vehicle in enumerate(top_vehicles[:3], 1):
                vehicle_num = FormattingService.escape_markdown(vehicle.get('vehicle_number', 'N/A'))
                search_count = vehicle.get('search_count', 0)
                lines.append(f"{i}. {vehicle_num} ({search_count} searches)")
        
        return "\n".join(lines)
    
    @staticmethod
    def format_log_entry(log_entry):
        """Format a single log entry"""
        timestamp = log_entry.get('timestamp', '')
        if hasattr(timestamp, 'strftime'):
            time_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        else:
            time_str = str(timestamp)
        
        query_type = log_entry.get('query_type', 'unknown').upper()
        query = FormattingService.escape_markdown(str(log_entry.get('query', 'N/A')))
        results_count = log_entry.get('results_count', 0)
        
        return f"🕐 {time_str} | {query_type}: {query} ({results_count} results)"
    
    @staticmethod
    def truncate_text(text, max_length=100):
        """Truncate text to specified length"""
        if not text or len(text) <= max_length:
            return text
        
        return text[:max_length-3] + "..."
