# database/models/settings.py
import logging
from datetime import datetime, timezone
from database.connection import get_collection
from config.constants import COLLECTIONS

logger = logging.getLogger(__name__)

class SettingsModel:
    """Enhanced settings model for all bot configurations"""
    
    def __init__(self):
        self.collection = get_collection(COLLECTIONS['SETTINGS'])
        self._ensure_default_settings()
    
    def _ensure_default_settings(self):
        """Ensure default settings exist"""
        if not self.collection.find_one({"_id": "config"}):
            default_settings = {
                "_id": "config",
                "created_at": datetime.now(timezone.utc),
                
                # Basic settings
                "search_credit_cost": 1,
                "trial_credits": 5,
                "open_access_enabled": False,
                
                # Feature visibility settings
                "mobile_search_enabled": True,
                "aadhar_search_enabled": True,
                "alternate_search_enabled": True,
                "vehicle_search_enabled": True,
                
                # LEA feature visibility
                "lea_mobile_search_enabled": True,
                "lea_aadhar_search_enabled": True,
                "lea_alternate_search_enabled": True,
                "lea_vehicle_search_enabled": True,
                
                # Normal user feature visibility
                "normal_mobile_search_enabled": True,
                "normal_aadhar_search_enabled": True,
                "normal_alternate_search_enabled": True,
                "normal_vehicle_search_enabled": True,
                
                # Smart pricing system
                "global_pricing_enabled": True,  # True = single price, False = individual prices
                "mobile_search_cost": 1,
                "aadhar_search_cost": 2,
                "alternate_search_cost": 1,
                "vehicle_search_cost": 3,
                
                # LEA Plan Management
                "lea_30d_cost": 500,
                "lea_6m_cost": 2500,
                "lea_1y_cost": 5000,
                "lea_vehicle_search_limit": None,  # None = unlimited
                
                # Rate limiting
                "rate_limiting_enabled": True,
                "daily_search_limit": 50,
                "per_minute_search_limit": 5,
                
                # Notifications
                "notifications_enabled": True,
                "credit_recharge_notifications": True,
                "lea_expiry_notifications": True,
                "expiry_warning_days": 3,
                
                # Messages (all editable by admin)
                "start_message": "🎉 Welcome to the Search Bot!\n\nYour gateway to comprehensive data search.",
                "insufficient_credits_message": "❌ **Insufficient Credits**\n\nYou don't have enough credits to perform this search.\nContact admin to recharge your account.",
                "access_denied_message": "🚫 **Access Denied**\n\nYou don't have permission to use this bot.\nContact admin for access.",
                "plan_expired_message": "⚠️ **Your plan has expired**\n\nContact admin for renewal to continue using search services.",
                "lea_plan_expired_message": "⚠️ **Your LEA plan has expired**\n\nContact admin for renewal to continue using search services.",
                "credit_deduction_notice": "Credits will be deducted only if results are found",
                "successful_search_message": "✅ Search Successful ({credits} credits deducted)",
                "no_results_message": "⚠️ No results found (no credits deducted)",
                "rate_limited_message": "⚡ Search blocked by rate limit (no credits deducted)",
                "search_error_message": "❌ Search error occurred (no credits deducted)",
                "lea_welcome_message": "👮 **Welcome LEA Officer!**\n\nYou now have unlimited search access until your plan expires.",
                "vehicle_limit_message": "🚗 Vehicle search limit reached. Contact admin.",
                "pricing_suffix": "if results found",
                "lea_unlimited_message": "Search anything unlimited",
                
                # Export settings
                "results_per_page": 3,
                
                # Updated timestamp
                "updated_at": datetime.now(timezone.utc)
            }
            
            self.collection.insert_one(default_settings)
            logger.info("Created default settings")
    
    def get_setting(self, key, default=None):
        """Get a setting value"""
        try:
            doc = self.collection.find_one({"_id": "config"})
            if not doc:
                return default
            return doc.get(key, default)
        except Exception as e:
            logger.error(f"Error getting setting {key}: {e}")
            return default
    
    def set_setting(self, key, value):
        """Set a setting value with timeout protection"""
        try:
            # Add timeout protection for database operations
            import pymongo
            result = self.collection.with_options(
                write_concern=pymongo.WriteConcern(w=1, wtimeout=5000)  # 5 second timeout
            ).update_one(
                {"_id": "config"},
                {
                    "$set": {
                        key: value,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            if result.modified_count > 0:
                logger.info(f"Updated setting {key} = {value}")
                return True
            return False
        except pymongo.errors.WTimeoutError:
            logger.error(f"Timeout setting {key}: Database write timeout")
            return False
        except Exception as e:
            logger.error(f"Error setting {key}: {e}")
            return False
    
    def get_all_settings(self):
        """Get all settings"""
        try:
            doc = self.collection.find_one({"_id": "config"})
            return doc if doc else {}
        except Exception as e:
            logger.error(f"Error getting all settings: {e}")
            return {}
    
    def update_multiple_settings(self, settings_dict):
        """Update multiple settings at once"""
        try:
            settings_dict["updated_at"] = datetime.now(timezone.utc)
            result = self.collection.update_one(
                {"_id": "config"},
                {"$set": settings_dict}
            )
            if result.modified_count > 0:
                logger.info(f"Updated {len(settings_dict)} settings")
                return True
            return False
        except Exception as e:
            logger.error(f"Error updating multiple settings: {e}")
            return False
    
    def get_feature_visibility_matrix(self):
        """Get feature visibility matrix for admin panel"""
        settings = self.get_all_settings()
        
        features = ["mobile", "aadhar", "alternate", "vehicle"]
        matrix = {}
        
        for feature in features:
            global_key = f"{feature}_search_enabled"
            lea_key = f"lea_{feature}_search_enabled"
            normal_key = f"normal_{feature}_search_enabled"
            
            global_enabled = settings.get(global_key, True)
            lea_enabled = settings.get(lea_key, True)
            normal_enabled = settings.get(normal_key, True)
            
            # Final result is global AND user-type specific
            matrix[feature] = {
                "global": global_enabled,
                "lea": lea_enabled,
                "normal": normal_enabled,
                "lea_final": global_enabled and lea_enabled,
                "normal_final": global_enabled and normal_enabled
            }
        
        return matrix
    
    def get_pricing_display(self, user_type="normal_user"):
        """Get pricing display based on user type and settings"""
        if user_type == "lea_officer":
            return self.get_setting("lea_unlimited_message", "Search anything unlimited")
        
        global_pricing = self.get_setting("global_pricing_enabled", True)
        suffix = self.get_setting("pricing_suffix", "if results found")
        
        if global_pricing:
            cost = self.get_setting("search_credit_cost", 1)
            return f"Per search: {cost} credits {suffix}"
        else:
            mobile_cost = self.get_setting("mobile_search_cost", 1)
            aadhar_cost = self.get_setting("aadhar_search_cost", 2)
            alt_cost = self.get_setting("alternate_search_cost", 1)
            vehicle_cost = self.get_setting("vehicle_search_cost", 3)
            
            return f"Mobile: {mobile_cost}, ID: {aadhar_cost}, Alt: {alt_cost}, Vehicle: {vehicle_cost} credits {suffix}"
    
    def get_search_cost(self, search_type="mobile"):
        """Get cost for specific search type"""
        global_pricing = self.get_setting("global_pricing_enabled", True)
        
        if global_pricing:
            return self.get_setting("search_credit_cost", 1)
        else:
            cost_map = {
                "mobile": self.get_setting("mobile_search_cost", 1),
                "aadhar": self.get_setting("aadhar_search_cost", 2),
                "alternate": self.get_setting("alternate_search_cost", 1),
                "vehicle": self.get_setting("vehicle_search_cost", 3)
            }
            return cost_map.get(search_type, 1)
    
    def is_feature_enabled(self, feature, user_type="normal_user"):
        """Check if feature is enabled for specific user type"""
        global_key = f"{feature}_search_enabled"
        user_key = f"{user_type.split('_')[0]}_{feature}_search_enabled"
        
        global_enabled = self.get_setting(global_key, True)
        user_enabled = self.get_setting(user_key, True)
        
        return global_enabled and user_enabled
    
    def get_lea_plan_cost(self, plan_duration):
        """Get LEA plan cost"""
        cost_map = {
            "30D": self.get_setting("lea_30d_cost", 500),
            "6M": self.get_setting("lea_6m_cost", 2500),
            "1Y": self.get_setting("lea_1y_cost", 5000)
        }
        return cost_map.get(plan_duration, 500)
    
    def get_editable_messages(self):
        """Get all editable messages for admin panel"""
        settings = self.get_all_settings()
        
        message_keys = [
            "start_message", "insufficient_credits_message", "access_denied_message",
            "plan_expired_message", "lea_plan_expired_message", "credit_deduction_notice",
            "successful_search_message", "no_results_message", "rate_limited_message",
            "search_error_message", "lea_welcome_message", "vehicle_limit_message",
            "pricing_suffix", "lea_unlimited_message"
        ]
        
        return {key: settings.get(key, "") for key in message_keys}

    def are_all_lea_features_enabled(self):
        """Check if all LEA features are enabled"""
        settings = self.get_all_settings()
        lea_features = [
            "lea_mobile_search_enabled", "lea_aadhar_search_enabled",
            "lea_alternate_search_enabled", "lea_vehicle_search_enabled"
        ]
        return all(settings.get(feature, True) for feature in lea_features)

    def are_all_normal_features_disabled(self):
        """Check if all normal user features are disabled"""
        settings = self.get_all_settings()
        normal_features = [
            "normal_mobile_search_enabled", "normal_aadhar_search_enabled",
            "normal_alternate_search_enabled", "normal_vehicle_search_enabled"
        ]
        return not any(settings.get(feature, True) for feature in normal_features)

    def are_all_global_features_enabled(self):
        """Check if all global features are enabled"""
        settings = self.get_all_settings()
        global_features = [
            "mobile_search_enabled", "aadhar_search_enabled",
            "alternate_search_enabled", "vehicle_search_enabled"
        ]
        return all(settings.get(feature, True) for feature in global_features)

    def are_all_global_features_disabled(self):
        """Check if all global features are disabled"""
        settings = self.get_all_settings()
        global_features = [
            "mobile_search_enabled", "aadhar_search_enabled",
            "alternate_search_enabled", "vehicle_search_enabled"
        ]
        return not any(settings.get(feature, True) for feature in global_features)
