# ui/keyboards.py
from telegram import InlineKeyboardMarkup, InlineKeyboardButton
from services.auth import AuthService
from config.constants import EMOJIS

class KeyboardBuilder:
    """Builder class for creating inline keyboards"""
    
    def __init__(self):
        self.auth_service = AuthService()
    
    def main_menu_keyboard(self, user=None):
        """Create main menu keyboard with improved layout"""
        buttons = []

        # First line: Search and My Info
        first_line = [InlineKeyboardButton(f"{EMOJIS['SEARCH']} Search", callback_data="search_menu")]
        if user:
            first_line.append(InlineKeyboardButton("👤 My Info", callback_data="user_info"))
        buttons.append(first_line)

        # Second line: Pricing and Join Channel
        second_line = []
        if self.auth_service.get_setting("pricing_button_enabled", True):
            pricing_text = self.auth_service.get_setting("pricing_button_text", "💲 Pricing")
            second_line.append(InlineKeyboardButton(pricing_text, callback_data="pricing"))

        channel_text = self.auth_service.get_setting("join_channel_text", "📢 Join Channel")
        channel_link = self.auth_service.get_setting("join_channel_link", "")
        if channel_link and channel_link != "https://t.me/your_channel_here":
            second_line.append(InlineKeyboardButton(channel_text, url=channel_link))

        if second_line:
            buttons.append(second_line)

        # Third line: My History
        if user:
            buttons.append([InlineKeyboardButton("📑 My History", callback_data="user_logs")])

        # Admin panel (if admin)
        if user and self.auth_service.is_admin(user):
            buttons.append([InlineKeyboardButton(f"{EMOJIS['ADMIN']} Admin Panel", callback_data="admin_panel")])

        return InlineKeyboardMarkup(buttons)
    
    def search_menu_keyboard(self, user=None):
        """Enhanced search menu keyboard with user-specific features and pricing"""
        buttons = []
        search_buttons = []

        # Get user-specific settings
        if user:
            is_lea = self.auth_service.is_lea_officer(user)
            suffix = "" if is_lea else self.auth_service.get_setting("pricing_suffix", "if results found")

            # Mobile search
            if self.auth_service.is_feature_enabled("mobile", user):
                search_buttons.append(InlineKeyboardButton(
                    f"{EMOJIS['MOBILE']} Mobile",
                    callback_data="do_search_mobile"
                ))

            # Aadhar search
            if self.auth_service.is_feature_enabled("aadhar", user):
                search_buttons.append(InlineKeyboardButton(
                    f"{EMOJIS['AADHAR']} AADHAR",
                    callback_data="do_search_aadhar"
                ))
        else:
            # Fallback for when user is not available
            if self.auth_service.is_feature_enabled("mobile_search"):
                search_buttons.append(InlineKeyboardButton(f"{EMOJIS['MOBILE']} Mobile", callback_data="do_search_mobile"))

            if self.auth_service.is_feature_enabled("aadhar_search"):
                search_buttons.append(InlineKeyboardButton(f"{EMOJIS['AADHAR']} AADHAR", callback_data="do_search_aadhar"))

        # Split into rows of 2
        for i in range(0, len(search_buttons), 2):
            buttons.append(search_buttons[i:i+2])

        # Second row of search options
        search_buttons_2 = []

        if user:
            # Alternative search
            if self.auth_service.is_feature_enabled("alternate", user):
                search_buttons_2.append(InlineKeyboardButton(
                    f"{EMOJIS['ALTERNATE']} Alternate",
                    callback_data="do_search_alt"
                ))

            # Vehicle search
            if self.auth_service.is_feature_enabled("vehicle", user):
                search_buttons_2.append(InlineKeyboardButton(
                    f"{EMOJIS['VEHICLE']} Vehicle",
                    callback_data="do_search_vehicle"
                ))
        else:
            # Fallback
            if self.auth_service.is_feature_enabled("alternate_search"):
                search_buttons_2.append(InlineKeyboardButton(f"{EMOJIS['ALTERNATE']} Alternate", callback_data="do_search_alt"))

            if self.auth_service.is_feature_enabled("vehicle_search"):
                search_buttons_2.append(InlineKeyboardButton(f"{EMOJIS['VEHICLE']} Vehicle", callback_data="do_search_vehicle"))

        if search_buttons_2:
            for i in range(0, len(search_buttons_2), 2):
                buttons.append(search_buttons_2[i:i+2])

        # Back button
        buttons.append([InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="back_main")])

        return InlineKeyboardMarkup(buttons)
    
    def search_input_keyboard(self):
        """Create keyboard for search input screens"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="search_menu"),
                InlineKeyboardButton(f"{EMOJIS['HOME']} Main Menu", callback_data="back_main")
            ]
        ])
    
    def admin_panel_keyboard(self):
        """Enhanced admin panel main keyboard with LEA management"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{EMOJIS['USERS']} User Management", callback_data="admin_user_mgmt"),
                InlineKeyboardButton("🏛️ LEA Officer Plans", callback_data="admin_lea_plans")
            ],
            [
                InlineKeyboardButton("💰 Pricing Control", callback_data="admin_pricing"),
                InlineKeyboardButton("🎯 Feature Settings", callback_data="admin_features")
            ],
            [
                InlineKeyboardButton("✏️ Message Templates", callback_data="admin_messages"),
                InlineKeyboardButton("🔔 Notifications", callback_data="admin_notifications")
            ],
            [
                InlineKeyboardButton("📢 Broadcast Message", callback_data="admin_broadcast"),
                InlineKeyboardButton(f"{EMOJIS['HIDDEN']} Hidden Records", callback_data="admin_hidden_records")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['STATS']} Statistics", callback_data="admin_stats"),
                InlineKeyboardButton(f"{EMOJIS['LOGS']} System Logs", callback_data="admin_logs_settings")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['EXPORT']} Data Export", callback_data="admin_export"),
                InlineKeyboardButton("⚡ Rate Limits", callback_data="admin_rate_limiting")
            ],
            [
                InlineKeyboardButton("🔐 Access", callback_data="admin_access_control"),
                InlineKeyboardButton(f"{EMOJIS['VEHICLE']} Vehicles", callback_data="admin_vehicle_data")
            ],
            [InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="back_main")]
        ])
    
    def admin_user_management_keyboard(self):
        """Enhanced user management keyboard with LEA support"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("👮 Add LEA", callback_data="admin_add_lea"),
                InlineKeyboardButton("👤 Add User", callback_data="admin_add_user")
            ],
            [
                InlineKeyboardButton("📋 Bulk LEA", callback_data="admin_bulk_lea"),
                InlineKeyboardButton("🔄 Add/Convert to LEA", callback_data="admin_convert_user")
            ],
            [
                InlineKeyboardButton("👥 View All", callback_data="admin_view_users"),
                InlineKeyboardButton("💳 Add Credits", callback_data="admin_add_credits")
            ],
            [
                InlineKeyboardButton("✏️ Modify", callback_data="admin_modify_credits"),
                InlineKeyboardButton("🚫 Suspend", callback_data="admin_suspend")
            ],
            [
                InlineKeyboardButton("✅ Unsuspend", callback_data="admin_unsuspend"),
                InlineKeyboardButton("❌ Remove", callback_data="admin_remove_user")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="admin_panel"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
    
    def admin_hidden_records_keyboard(self):
        """Create hidden records management keyboard"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("➕ Hide Record", callback_data="admin_hide_record"),
                InlineKeyboardButton("👁 View Hidden", callback_data="admin_view_hidden")
            ],
            [
                InlineKeyboardButton("🔍 Search Hidden", callback_data="admin_search_hidden"),
                InlineKeyboardButton("👁‍🗨 Unhide Record", callback_data="admin_unhide_record")
            ],
            [InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="admin_panel")]
        ])
    
    def admin_vehicle_data_keyboard(self):
        """Create vehicle data management keyboard"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{EMOJIS['STATS']} Vehicle Statistics", callback_data="admin_vehicle_stats"),
                InlineKeyboardButton("🗑️ Remove Vehicle", callback_data="admin_remove_vehicle")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['EXPORT']} Export Vehicle Data", callback_data="admin_export_vehicles")
            ],
            [InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="admin_panel")]
        ])
    
    def admin_search_settings_keyboard(self):
        """Create search settings keyboard"""
        mobile_status = "✅" if self.auth_service.is_feature_enabled("mobile_search") else "❌"
        aadhar_status = "✅" if self.auth_service.is_feature_enabled("aadhar_search") else "❌"
        alt_status = "✅" if self.auth_service.is_feature_enabled("alternate_search") else "❌"
        vehicle_status = "✅" if self.auth_service.is_feature_enabled("vehicle_search") else "❌"
        
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{mobile_status} Mobile Search", callback_data="admin_toggle_mobile_search"),
                InlineKeyboardButton(f"{aadhar_status} Aadhar Search", callback_data="admin_toggle_aadhar_search")
            ],
            [
                InlineKeyboardButton(f"{alt_status} Alternate Search", callback_data="admin_toggle_alternate_search"),
                InlineKeyboardButton(f"{vehicle_status} Vehicle Search", callback_data="admin_toggle_vehicle_search")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="admin_panel"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
    
    def confirmation_keyboard(self, confirm_callback, cancel_callback="admin_panel"):
        """Create confirmation keyboard"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("✅ Confirm", callback_data=confirm_callback),
                InlineKeyboardButton("❌ Cancel", callback_data=cancel_callback)
            ]
        ])
    
    def simple_back_keyboard(self, back_callback="back_main"):
        """Create simple back button keyboard"""
        return InlineKeyboardMarkup([
            [InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data=back_callback)]
        ])
    
    def admin_back_keyboard(self, back_callback="admin_panel"):
        """Create admin back button keyboard"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data=back_callback),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
    
    def no_results_keyboard(self):
        """Create keyboard for no results screen"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🔍 Search Again", callback_data="search_again"),
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="search_menu")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['HOME']} Main Menu", callback_data="back_main")
            ]
        ])
    
    def user_info_keyboard(self):
        """Create user info keyboard"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🔄 Refresh", callback_data="user_info"),
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="back_main")
            ]
        ])

    # Enhanced LEA Management Keyboards
    def admin_lea_plans_keyboard(self):
        """Create LEA plan management keyboard"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📅 30-Day Plan", callback_data="admin_lea_30d"),
                InlineKeyboardButton("📅 6-Month Plan", callback_data="admin_lea_6m")
            ],
            [
                InlineKeyboardButton("📅 1-Year Plan", callback_data="admin_lea_1y"),
                InlineKeyboardButton("📊 Plan Stats", callback_data="admin_lea_stats")
            ],
            [
                InlineKeyboardButton("⚙️ Plan Settings", callback_data="admin_lea_settings"),
                InlineKeyboardButton("⏰ Expiring Soon", callback_data="admin_lea_expiring")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="admin_panel"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])

    def plan_duration_keyboard(self):
        """Create LEA plan duration selection keyboard"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📅 30 Days", callback_data="plan_30D"),
                InlineKeyboardButton("📅 6 Months", callback_data="plan_6M")
            ],
            [
                InlineKeyboardButton("📅 1 Year", callback_data="plan_1Y"),
                InlineKeyboardButton("❌ Cancel", callback_data="admin_user_mgmt")
            ]
        ])

    def bulk_lea_options_keyboard(self):
        """Create bulk LEA options keyboard for welcome messages"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("✅ Send Welcome", callback_data="bulk_welcome_yes"),
                InlineKeyboardButton("❌ Skip Welcome", callback_data="bulk_welcome_no")
            ]
        ])

    def admin_features_keyboard(self):
        """Create feature visibility keyboard with toggle buttons"""
        from database.models.settings import SettingsModel
        settings = SettingsModel()

        # Check current states for toggle buttons
        lea_enabled = settings.are_all_lea_features_enabled()
        normal_disabled = settings.are_all_normal_features_disabled()

        # Create toggle button texts
        lea_toggle_text = f"👮 {'✅ Disable' if lea_enabled else '❌ Enable'} All LEA"
        normal_toggle_text = f"👤 {'❌ Enable' if normal_disabled else '✅ Disable'} All Normal"

        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📊 Feature Matrix", callback_data="admin_feature_matrix"),
                InlineKeyboardButton("🌐 Global Control", callback_data="admin_global_features")
            ],
            [
                InlineKeyboardButton("👮 LEA Features", callback_data="admin_lea_features"),
                InlineKeyboardButton("👤 User Features", callback_data="admin_normal_features")
            ],
            [
                InlineKeyboardButton(lea_toggle_text, callback_data="admin_toggle_all_lea"),
                InlineKeyboardButton(normal_toggle_text, callback_data="admin_toggle_all_normal")
            ],
            [
                InlineKeyboardButton("🔄 Reset Defaults", callback_data="admin_reset_features"),
                InlineKeyboardButton("⚙️ Individual", callback_data="admin_individual_features")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="admin_panel"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])

    def admin_pricing_keyboard(self):
        """Create smart pricing keyboard"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🔄 Global Toggle", callback_data="admin_toggle_global_pricing"),
                InlineKeyboardButton("💰 Set Costs", callback_data="admin_set_costs")
            ],
            [
                InlineKeyboardButton("👁️ Preview", callback_data="admin_pricing_preview"),
                InlineKeyboardButton("🏛️ LEA Costs", callback_data="admin_lea_costs")
            ],
            [
                InlineKeyboardButton(f"{EMOJIS['BACK']} Back", callback_data="admin_panel"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ])
