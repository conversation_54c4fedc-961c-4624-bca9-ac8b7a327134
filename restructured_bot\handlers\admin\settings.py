# handlers/admin/settings.py
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.base import BaseHandler
from config.constants import *
from database.models import UserModel, SettingsModel
from utils.error_logger import log_callback_error, log_callback_interaction

logger = logging.getLogger(__name__)

class AdminSettingsHandler(BaseHandler):
    """Handler for admin settings functionality"""

    def __init__(self):
        super().__init__()
        self.settings_model = SettingsModel()
    
    async def admin_pricing_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show pricing settings"""
        query = update.callback_query
        await query.answer()
        
        current_cost = self.auth_service.get_search_cost()
        
        text = (
            f"💰 **Pricing Settings**\n\n"
            f"Current search cost: {current_cost} credits\n\n"
            f"Use /setprice <amount> to change pricing"
        )
        
        keyboard = self.keyboard_builder.admin_back_keyboard()
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_PANEL
    
    async def admin_message_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show comprehensive message settings"""
        query = update.callback_query
        await query.answer()

        text = (
            f"✏️ **Message Templates Control**\n\n"
            f"Configure all bot messages and responses:\n\n"
            f"📝 **Available Message Types:**\n"
            f"• Welcome & Start Messages\n"
            f"• Search Prompts & Results\n"
            f"• Error & Success Messages\n"
            f"• Pricing & Credit Messages\n"
            f"• Notification Messages\n"
            f"• Admin & System Messages"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📝 Welcome Messages", callback_data="msg_welcome"),
                InlineKeyboardButton("🔍 Search Messages", callback_data="msg_search")
            ],
            [
                InlineKeyboardButton("💰 Pricing Messages", callback_data="msg_pricing"),
                InlineKeyboardButton("❌ Error Messages", callback_data="msg_error")
            ],
            [
                InlineKeyboardButton("✅ Success Messages", callback_data="msg_success"),
                InlineKeyboardButton("🔔 Notification Messages", callback_data="msg_notification")
            ],
            [
                InlineKeyboardButton("⚙️ System Messages", callback_data="msg_system"),
                InlineKeyboardButton("🔄 Reset All to Default", callback_data="msg_reset_all")
            ],
            [InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin_panel")]
        ])

        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

        return ADMIN_PANEL
    
    async def admin_notification_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show comprehensive notification settings"""
        query = update.callback_query
        await query.answer()

        # Get current notification settings
        welcome_enabled = self.settings_model.get_setting("welcome_notifications", True)
        credit_threshold = self.settings_model.get_setting("low_credit_threshold", 10)
        expiry_days = self.settings_model.get_setting("plan_expiry_warning_days", 3)

        text = (
            f"🔔 **Notification Control Center**\n\n"
            f"📊 **Current Settings:**\n"
            f"• Welcome Notifications: {'✅ Enabled' if welcome_enabled else '❌ Disabled'}\n"
            f"• Low Credit Warning: {credit_threshold} credits\n"
            f"• Plan Expiry Warning: {expiry_days} days before\n\n"
            f"🎯 **Configure All Notification Types:**"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("👋 Welcome Notifications", callback_data="notif_welcome"),
                InlineKeyboardButton("💳 Credit Warnings", callback_data="notif_credits")
            ],
            [
                InlineKeyboardButton("⏰ Plan Expiry Alerts", callback_data="notif_expiry"),
                InlineKeyboardButton("📊 Usage Reports", callback_data="notif_usage")
            ],
            [
                InlineKeyboardButton("🏛️ LEA Notifications", callback_data="notif_lea"),
                InlineKeyboardButton("👤 User Notifications", callback_data="notif_users")
            ],
            [
                InlineKeyboardButton("🔧 System Alerts", callback_data="notif_system"),
                InlineKeyboardButton("📱 Message Customization", callback_data="notif_customize")
            ],
            [InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin_panel")]
        ])

        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

        return ADMIN_PANEL
    
    async def admin_logs_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show logs settings"""
        query = update.callback_query
        await query.answer()
        
        from telegram import InlineKeyboardMarkup, InlineKeyboardButton
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📑 View All Logs", callback_data="admin_all_logs"),
                InlineKeyboardButton("👤 User Logs", callback_data="admin_user_logs")
            ],
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ])
        
        await query.edit_message_text(
            "📑 **Logs Settings**\n\nChoose log type to view:",
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_PANEL
    
    async def admin_view_all_logs(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page=0):
        """View all search logs"""
        query = update.callback_query
        await query.answer()
        
        from database.models import SearchModel
        search_model = SearchModel()
        
        logs = search_model.get_all_logs(100)
        message, total_pages = self.pagination_service.format_log_list(logs, page)
        
        keyboard = self.pagination_service.create_admin_list_keyboard(
            page, total_pages, "admin_logs", "admin_logs_settings"
        )
        
        await query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_PANEL
    
    async def admin_rate_limiting_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show comprehensive rate limiting settings"""
        query = update.callback_query
        await query.answer()

        # Get current rate limiting settings
        rate_limiting_enabled = self.settings_model.get_setting("rate_limiting_enabled", True)
        daily_limit = self.settings_model.get_setting("daily_search_limit", 100)
        cooldown_enabled = self.settings_model.get_setting("cooldown_enabled", False)
        cooldown_seconds = self.settings_model.get_setting("cooldown_seconds", 30)

        text = (
            f"⚡ **Rate Limiting Control Center**\n\n"
            f"📊 **Current Settings:**\n"
            f"• Rate Limiting: {'✅ Enabled' if rate_limiting_enabled else '❌ Disabled'}\n"
            f"• Daily Search Limit: {daily_limit} searches\n"
            f"• Cooldown Period: {'✅ Enabled' if cooldown_enabled else '❌ Disabled'} ({cooldown_seconds}s)\n\n"
            f"🎯 **Configure Rate Controls:**"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🔄 Toggle Rate Limiting", callback_data="rate_toggle_main"),
                InlineKeyboardButton("📊 Daily Limits", callback_data="rate_daily_limits")
            ],
            [
                InlineKeyboardButton("📱 Per Search Type Limits", callback_data="rate_search_limits"),
                InlineKeyboardButton("👥 User Type Limits", callback_data="rate_user_limits")
            ],
            [
                InlineKeyboardButton("⏱️ Cooldown Settings", callback_data="rate_cooldown"),
                InlineKeyboardButton("🏛️ LEA Exemptions", callback_data="rate_lea_exempt")
            ],
            [
                InlineKeyboardButton("📈 Usage Statistics", callback_data="rate_stats"),
                InlineKeyboardButton("🔄 Reset All Limits", callback_data="rate_reset_all")
            ],
            [InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin_panel")]
        ])

        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

        return ADMIN_PANEL
    
    async def admin_access_control_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show comprehensive access control settings"""
        query = update.callback_query
        await query.answer()

        # Get current access control settings
        trial_enabled = self.settings_model.get_setting("trial_enabled", True)
        trial_days = self.settings_model.get_setting("trial_days", 7)
        trial_credits = self.settings_model.get_setting("trial_credits", 50)
        auto_convert = self.settings_model.get_setting("auto_convert_trial", False)

        text = (
            f"🔐 **Access Control & Trial Management**\n\n"
            f"📊 **Current Settings:**\n"
            f"• Trial System: {'✅ Enabled' if trial_enabled else '❌ Disabled'}\n"
            f"• Trial Duration: {trial_days} days\n"
            f"• Trial Credits: {trial_credits} credits\n"
            f"• Auto Convert: {'✅ Enabled' if auto_convert else '❌ Disabled'}\n\n"
            f"🎯 **Configure Access Controls:**"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🆓 Trial Settings", callback_data="access_trial"),
                InlineKeyboardButton("👥 User Types", callback_data="access_user_types")
            ],
            [
                InlineKeyboardButton("🏛️ LEA Management", callback_data="access_lea"),
                InlineKeyboardButton("💎 Premium Settings", callback_data="access_premium")
            ],
            [
                InlineKeyboardButton("🔄 Auto Conversions", callback_data="access_auto_convert"),
                InlineKeyboardButton("📊 Access Statistics", callback_data="access_stats")
            ],
            [
                InlineKeyboardButton("🚫 Banned Users", callback_data="access_banned"),
                InlineKeyboardButton("⚙️ Default Settings", callback_data="access_defaults")
            ],
            [InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin_panel")]
        ])

        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

        return ADMIN_PANEL

    # New callback handlers for enhanced functionality
    async def handle_message_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle message settings callbacks"""
        query = update.callback_query
        data = query.data

        if data == "msg_reset_all":
            # Reset all messages to default with confirmation
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("✅ Yes, Reset All", callback_data="msg_confirm_reset"),
                    InlineKeyboardButton("❌ Cancel", callback_data="admin_messages")
                ]
            ])

            await query.edit_message_text(
                "⚠️ **Confirm Reset All Messages**\n\n"
                "This will reset ALL bot messages to default values.\n"
                "This action cannot be undone.\n\n"
                "Are you sure you want to continue?",
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_PANEL

        elif data == "msg_confirm_reset":
            # Execute reset
            self.settings_model.reset_all_messages()
            await query.answer("✅ All messages reset to default!")
            return await self.admin_message_settings(update, context)

        else:
            # Handle specific message type settings
            message_type = data.replace("msg_", "")
            context.user_data["admin_state"] = f"edit_message_{message_type}"

            await query.edit_message_text(
                f"✏️ **Edit {message_type.title()} Messages**\n\n"
                f"Send the new message text for {message_type} messages.\n\n"
                f"💡 You can use markdown formatting.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Cancel", callback_data="admin_messages")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_ADD_USER

    async def handle_notification_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle notification settings callbacks"""
        query = update.callback_query
        data = query.data

        # Handle notification type settings
        notification_type = data.replace("notif_", "")

        if notification_type == "credits":
            # Credit threshold setting
            context.user_data["admin_state"] = "set_credit_threshold"
            await query.edit_message_text(
                "💳 **Low Credit Warning Threshold**\n\n"
                "Enter the credit threshold for low credit warnings:\n\n"
                "Example: `10` (warn when credits ≤ 10)",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Cancel", callback_data="admin_notifications")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_ADD_USER

        elif notification_type == "expiry":
            # Expiry days setting
            context.user_data["admin_state"] = "set_expiry_days"
            await query.edit_message_text(
                "⏰ **Plan Expiry Warning Days**\n\n"
                "Enter how many days before expiry to send warnings:\n\n"
                "Example: `3` (warn 3 days before expiry)",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Cancel", callback_data="admin_notifications")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_ADD_USER

        else:
            # Toggle notification type
            current_status = self.settings_model.get_setting(f"{notification_type}_notifications", True)
            new_status = not current_status
            self.settings_model.set_setting(f"{notification_type}_notifications", new_status)

            await query.answer(f"{'✅ Enabled' if new_status else '❌ Disabled'} {notification_type} notifications")
            return await self.admin_notification_settings(update, context)

    async def handle_rate_limiting_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle rate limiting settings callbacks"""
        query = update.callback_query
        data = query.data

        if data == "rate_toggle_main":
            # Toggle main rate limiting
            current = self.settings_model.get_setting("rate_limiting_enabled", True)
            new_status = not current
            self.settings_model.set_setting("rate_limiting_enabled", new_status)
            await query.answer(f"Rate limiting {'✅ Enabled' if new_status else '❌ Disabled'}")
            return await self.admin_rate_limiting_settings(update, context)

        elif data == "rate_cooldown":
            # Toggle cooldown
            current = self.settings_model.get_setting("cooldown_enabled", False)
            new_status = not current
            self.settings_model.set_setting("cooldown_enabled", new_status)
            await query.answer(f"Cooldown {'✅ Enabled' if new_status else '❌ Disabled'}")
            return await self.admin_rate_limiting_settings(update, context)

        elif data == "rate_daily_limits":
            # Set daily limits
            context.user_data["admin_state"] = "set_daily_limit"
            await query.edit_message_text(
                "📊 **Daily Search Limit**\n\n"
                "Enter the daily search limit per user:\n\n"
                "Example: `100` (100 searches per day)",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Cancel", callback_data="admin_rate_limiting")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_ADD_USER

        else:
            await query.answer("⚙️ Feature coming soon!")
            return await self.admin_rate_limiting_settings(update, context)

    async def handle_access_control_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle access control settings callbacks"""
        query = update.callback_query
        data = query.data

        if data == "access_trial":
            # Trial settings
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("🔄 Toggle Trial System", callback_data="toggle_trial_system"),
                    InlineKeyboardButton("📅 Set Trial Days", callback_data="set_trial_days")
                ],
                [
                    InlineKeyboardButton("💳 Set Trial Credits", callback_data="set_trial_credits"),
                    InlineKeyboardButton("🔄 Auto Convert", callback_data="toggle_auto_convert")
                ],
                [InlineKeyboardButton("🔙 Back", callback_data="admin_access_control")]
            ])

            trial_enabled = self.settings_model.get_setting("trial_enabled", True)
            trial_days = self.settings_model.get_setting("trial_days", 7)
            trial_credits = self.settings_model.get_setting("trial_credits", 50)

            await query.edit_message_text(
                f"🆓 **Trial System Settings**\n\n"
                f"Status: {'✅ Enabled' if trial_enabled else '❌ Disabled'}\n"
                f"Duration: {trial_days} days\n"
                f"Credits: {trial_credits}\n\n"
                f"Configure trial system:",
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_PANEL

        else:
            await query.answer("⚙️ Feature coming soon!")
            return await self.admin_access_control_settings(update, context)
