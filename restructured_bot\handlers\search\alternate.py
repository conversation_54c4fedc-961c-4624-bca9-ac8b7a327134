# handlers/search/alternate.py
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.search.base_search import <PERSON><PERSON>earch<PERSON>andler
from config.constants import MENU, ALT_SEARCH
from services.validation import ValidationService
from database.models import SearchModel, UserModel

logger = logging.getLogger(__name__)

class AlternateSearchHandler(BaseSearchHandler):
    """Handler for alternate number searches"""
    
    def __init__(self):
        super().__init__()
        self.search_model = SearchModel()
        self.user_model = UserModel()  # Fix: Add missing user_model
    
    async def handle_alternate_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle alternate number input"""
        user = await self.check_user_authorization(update, context)
        if not user:
            return MENU
        
        input_text = update.message.text.strip()
        
        # Validate input
        is_valid, result = ValidationService.validate_alternate_number(input_text)
        if not is_valid:
            await self.send_validation_error(update, context, result)
            return ALT_SEARCH
        
        query_text = result
        
        # Check credits
        cost = self.auth_service.get_search_cost("alternate")
        has_credits, credit_msg = self.auth_service.check_credits(user, cost)
        if not has_credits:
            await self.send_insufficient_credits(update, context)
            return MENU
        
        # Check rate limits
        rate_ok, rate_msg = self.auth_service.check_rate_limit(user)
        if not rate_ok:
            limit_type = rate_msg.split()[-1] if rate_msg else "rate"
            await self.send_rate_limited(update, context, limit_type)
            return MENU
        
        # Show searching message
        wait_msg = await update.message.reply_text(
            self.message_templates.searching_message(query_text),
            parse_mode=ParseMode.MARKDOWN
        )
        
        try:
            # Perform search
            results = self.search_model.search_data("alt", query_text)
            
            if not results:
                await wait_msg.edit_text(
                    self.message_templates.no_results_message("Alternate Number", query_text),
                    reply_markup=self.keyboard_builder.no_results_keyboard(),
                    parse_mode=ParseMode.MARKDOWN
                )
                
                # Log search but don't deduct credits for no results
                self.search_model.log_search(user["user_id"], "alternate", query_text, 0)
                self.log_user_action(user["user_id"], "alternate_search_no_results", query_text)
                return MENU
            
            # Enhanced: Results found - now deduct credits
            credits_charged = 0
            if not self.auth_service.is_admin(user) and not self.auth_service.is_lea_officer(user):
                if self.auth_service.deduct_credits(user["user_id"], cost):
                    credits_charged = cost
                    self.auth_service.update_search_activity(user)

            # Enhanced: Log successful search with credit info
            self.search_model.log_search(user["user_id"], "alternate", query_text, len(results), success=True)
            self.auth_service.update_search_stats(user["user_id"], success=True, credits_charged=credits_charged)

            # Store results in context for pagination
            context.user_data["alt_results"] = results
            context.user_data["current_search_field"] = "alt"

            # Show results first (separate message)
            await self.show_paginated_results_universal(context, update, results, 0, "alt", "results")

            # Then show action buttons (separate message)
            await self.show_search_action_buttons(update, context, "alt", len(results))

            # Finally show success message (separate message)
            await self.show_search_success_message(update, user, credits_charged, context)

            self.log_user_action(user["user_id"], "alternate_search_success", f"{query_text} ({len(results)} results)")
            return MENU
            
        except Exception as e:
            logger.error(f"Error in alternate search: {e}")
            await wait_msg.edit_text(
                "❌ **Search Error**\n\nAn error occurred while searching. Please try again.",
                reply_markup=self.keyboard_builder.search_input_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )
            return MENU
    
    async def handle_pagination(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page: int):
        """Handle pagination using universal method"""
        return await self.handle_pagination_universal(update, context, page, "alt", "alt_results")
    
    async def show_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main menu (imported from start handler)"""
        from handlers.start import StartHandler
        start_handler = StartHandler()
        return await start_handler.show_main_menu(update, context)
