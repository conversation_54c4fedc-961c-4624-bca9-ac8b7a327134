# config/settings.py
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Bot Configuration
BOT_TOKEN = os.getenv("BOT_TOKEN", "8458824131:AAERhwBjzQC2MlJFqRiiGZVKJpgFPtey4R0")
OWNER_ID = int(os.getenv("OWNER_ID", "1639782650"))

# Database Configuration
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")
DB_NAME = os.getenv("DB_NAME", "lawenforcement")

# Pagination Settings
RESULTS_PER_PAGE = int(os.getenv("RESULTS_PER_PAGE", "4"))
USERS_PER_PAGE = int(os.getenv("USERS_PER_PAGE", "5"))
LOGS_PER_PAGE = int(os.getenv("LOGS_PER_PAGE", "10"))

# Rate Limiting
DEFAULT_DAILY_SEARCH_LIMIT = int(os.getenv("DEFAULT_DAILY_SEARCH_LIMIT", "10"))
DEFAULT_PER_MINUTE_LIMIT = int(os.getenv("DEFAULT_PER_MINUTE_LIMIT", "5"))

# Default Settings
DEFAULT_SEARCH_COST = int(os.getenv("DEFAULT_SEARCH_COST", "1"))
DEFAULT_TRIAL_CREDITS = int(os.getenv("DEFAULT_TRIAL_CREDITS", "5"))

# Logging Configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Vehicle API Configuration (if needed)
VEHICLE_API_TIMEOUT = int(os.getenv("VEHICLE_API_TIMEOUT", "30"))

# Feature Flags
ENABLE_VEHICLE_SEARCH = os.getenv("ENABLE_VEHICLE_SEARCH", "true").lower() == "true"
ENABLE_HIDDEN_RECORDS = os.getenv("ENABLE_HIDDEN_RECORDS", "true").lower() == "true"
ENABLE_RATE_LIMITING = os.getenv("ENABLE_RATE_LIMITING", "true").lower() == "true"

# Enhanced Features Settings
DEFAULT_RESULTS_PER_PAGE = int(os.getenv("DEFAULT_RESULTS_PER_PAGE", "3"))
DEFAULT_LEA_VEHICLE_LIMIT = None  # None = unlimited
DEFAULT_EXPIRY_WARNING_DAYS = int(os.getenv("DEFAULT_EXPIRY_WARNING_DAYS", "3"))

# LEA Plan Costs (in INR)
DEFAULT_LEA_30D_COST = int(os.getenv("DEFAULT_LEA_30D_COST", "500"))
DEFAULT_LEA_6M_COST = int(os.getenv("DEFAULT_LEA_6M_COST", "2500"))
DEFAULT_LEA_1Y_COST = int(os.getenv("DEFAULT_LEA_1Y_COST", "5000"))

# Enhanced Credit System
SUCCESS_BASED_CREDITS = os.getenv("SUCCESS_BASED_CREDITS", "true").lower() == "true"
ENHANCED_LOGGING = os.getenv("ENHANCED_LOGGING", "true").lower() == "true"

# Feature Visibility Defaults
DEFAULT_GLOBAL_FEATURES = {
    "mobile_search_enabled": True,
    "aadhar_search_enabled": True,
    "alternate_search_enabled": True,
    "vehicle_search_enabled": True
}

DEFAULT_LEA_FEATURES = {
    "lea_mobile_search_enabled": True,
    "lea_aadhar_search_enabled": True,
    "lea_alternate_search_enabled": True,
    "lea_vehicle_search_enabled": True
}

DEFAULT_NORMAL_FEATURES = {
    "normal_mobile_search_enabled": True,
    "normal_aadhar_search_enabled": True,
    "normal_alternate_search_enabled": True,
    "normal_vehicle_search_enabled": True
}

# Smart Pricing Defaults
DEFAULT_GLOBAL_PRICING = os.getenv("DEFAULT_GLOBAL_PRICING", "true").lower() == "true"
DEFAULT_SEARCH_COSTS = {
    "mobile_search_cost": int(os.getenv("MOBILE_SEARCH_COST", "1")),
    "aadhar_search_cost": int(os.getenv("AADHAR_SEARCH_COST", "2")),
    "alternate_search_cost": int(os.getenv("ALTERNATE_SEARCH_COST", "1")),
    "vehicle_search_cost": int(os.getenv("VEHICLE_SEARCH_COST", "3"))
}

# Enhanced Messages
DEFAULT_MESSAGES = {
    "lea_plan_expired_message": "🏛️ **LEA Plan Expired**\n\nYour LEA officer plan has expired. Please contact admin for renewal.",
    "successful_search_message": "✅ **Search Successful** ({credits} credits charged)",
    "no_results_message": "❌ **No Results Found**\n\nNo matching records found. Credits not charged.",
    "rate_limited_message": "⚡ **Rate Limited**\n\nPlease wait before searching again.",
    "lea_welcome_message": "🏛️ **Welcome LEA Officer**\n\nYour account has been activated with unlimited search access."
}
