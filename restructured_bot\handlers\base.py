# handlers/base.py
import logging
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from services.auth import AuthService
from ui.keyboards import KeyboardBuilder
from ui.messages import MessageTemplates
from services.pagination import PaginationService

logger = logging.getLogger(__name__)

class BaseHandler:
    """Base handler class with common functionality"""
    
    def __init__(self):
        self.auth_service = AuthService()
        self.keyboard_builder = KeyboardBuilder()
        self.message_templates = MessageTemplates()
        self.pagination_service = PaginationService()
    
    async def get_user_from_update(self, update: Update):
        """Get user from update with authentication"""
        user_id = update.effective_user.id
        username = update.effective_user.username
        first_name = update.effective_user.first_name
        last_name = update.effective_user.last_name
        
        return self.auth_service.get_or_create_user(user_id, username, first_name, last_name)
    
    async def check_user_authorization(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Check if user is authorized and return user object"""
        user = await self.get_user_from_update(update)

        if not user:
            await self.send_access_denied_with_user_id(update, context)
            return None

        if not self.auth_service.is_user_authorized(user):
            await self.send_access_denied_with_user_id(update, context, user)
            return None

        return user
    
    async def check_admin_authorization(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Check if user is admin and return user object"""
        user = await self.get_user_from_update(update)

        if not user or not self.auth_service.is_admin(user):
            await self.send_admin_access_denied_with_user_id(update, context, user)
            return None

        return user
    
    async def send_access_denied(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send access denied message"""
        message = self.message_templates.access_denied_message()
        keyboard = self.keyboard_builder.simple_back_keyboard()

        if update.callback_query:
            await update.callback_query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

    async def send_access_denied_with_user_id(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user=None):
        """Send access denied message WITH user ID for easy copying"""
        user_id = update.effective_user.id
        username = update.effective_user.username

        # Determine status based on user data
        if user is None:
            status = "not_registered"
        elif user.get("suspended", False):
            status = "suspended"
        elif user.get("credits", 0) <= 0 and not self.auth_service.is_admin(user) and not self.auth_service.is_lea_officer(user):
            status = "expired"
        else:
            status = "unauthorized"

        message = self.message_templates.access_denied_with_user_id_message(user_id, username, status)

        # CRITICAL FIX: For unauthorized users, don't show interactive buttons
        # This prevents conversation state issues and follows old bot approach
        if user is None or status in ["not_registered", "suspended", "expired"]:
            # No keyboard for truly unauthorized users - conversation will end
            keyboard = None
        else:
            # Only show back button for users who might regain access
            keyboard = self.keyboard_builder.simple_back_keyboard()

        if update.callback_query:
            await update.callback_query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def send_admin_access_denied(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send admin access denied message"""
        message = self.message_templates.admin_access_denied_message()
        keyboard = self.keyboard_builder.simple_back_keyboard()

        if update.callback_query:
            await update.callback_query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

    async def send_admin_access_denied_with_user_id(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user=None):
        """Send admin access denied message WITH user ID for easy copying"""
        user_id = update.effective_user.id
        username = update.effective_user.username

        user_display = f"**User ID:** `{user_id}`"
        if username:
            user_display += f"\n**Username:** @{username}"

        message = (
            f"🔒 **Admin Access Required**\n\n"
            f"You need administrator privileges to access this feature.\n\n"
            f"📋 **Your Information:**\n"
            f"{user_display}\n\n"
            f"💡 **Contact the main administrator if you believe you should have admin access.**"
        )

        keyboard = self.keyboard_builder.simple_back_keyboard()

        if update.callback_query:
            await update.callback_query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def send_insufficient_credits(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send insufficient credits message with User ID"""
        user_id = update.effective_user.id
        username = update.effective_user.username

        user_display = f"**User ID:** `{user_id}`"
        if username:
            user_display += f"\n**Username:** @{username}"

        message = (
            f"💳 **Insufficient Credits**\n\n"
            f"You don't have enough credits to perform this search.\n\n"
            f"📋 **Your Information:**\n"
            f"{user_display}\n\n"
            f"💡 **Send your User ID to the admin to recharge your account.**"
        )

        keyboard = self.keyboard_builder.search_input_keyboard()

        if update.callback_query:
            await update.callback_query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def send_rate_limited(self, update: Update, context: ContextTypes.DEFAULT_TYPE, limit_type):
        """Send rate limited message"""
        message = self.message_templates.rate_limited_message(limit_type)
        keyboard = self.keyboard_builder.search_input_keyboard()
        
        if update.callback_query:
            await update.callback_query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def send_feature_disabled(self, update: Update, context: ContextTypes.DEFAULT_TYPE, feature_name):
        """Send feature disabled message"""
        message = self.message_templates.feature_disabled_message(feature_name)
        keyboard = self.keyboard_builder.simple_back_keyboard()
        
        if update.callback_query:
            await update.callback_query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def send_validation_error(self, update: Update, context: ContextTypes.DEFAULT_TYPE, error_message, back_callback="search_menu"):
        """Send validation error message"""
        keyboard = self.keyboard_builder.search_input_keyboard()
        
        await update.message.reply_text(
            f"{error_message}\n\nPlease try again:",
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def send_success_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message, keyboard=None):
        """Send success message"""
        success_msg = self.message_templates.success_message(message)
        
        if not keyboard:
            keyboard = self.keyboard_builder.simple_back_keyboard()
        
        if update.callback_query:
            await update.callback_query.edit_message_text(
                success_msg,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                success_msg,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def send_error_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message, keyboard=None):
        """Send error message"""
        error_msg = self.message_templates.error_message(message)
        
        if not keyboard:
            keyboard = self.keyboard_builder.simple_back_keyboard()
        
        if update.callback_query:
            await update.callback_query.edit_message_text(
                error_msg,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                error_msg,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
    
    def log_user_action(self, user_id, action, details=""):
        """Log user action"""
        logger.info(f"User {user_id} performed action: {action} {details}")
    
    def log_admin_action(self, admin_id, action, target="", details=""):
        """Log admin action"""
        logger.info(f"Admin {admin_id} performed action: {action} on {target} {details}")
    
    async def answer_callback_query(self, update: Update, text="", show_alert=False):
        """Answer callback query if present"""
        if update.callback_query:
            await update.callback_query.answer(text, show_alert=show_alert)

    async def safe_edit_message(self, query, message, keyboard, parse_mode=ParseMode.MARKDOWN):
        """Safely edit message with error handling for 'Message is not modified' error"""
        try:
            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=parse_mode
            )
        except Exception as e:
            if "Message is not modified" in str(e) or "message is not modified" in str(e):
                # Message content is the same, just answer the callback
                await query.answer("Refreshed")
            else:
                # Re-raise other exceptions
                raise e
