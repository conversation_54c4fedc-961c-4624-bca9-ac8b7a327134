# database/models/search.py
import logging
from datetime import datetime, timezone
from database.connection import get_collection
from config.constants import COLLECTIONS, FIELD_MAPPINGS

logger = logging.getLogger(__name__)

class SearchModel:
    """Search model for handling database searches"""
    
    def __init__(self):
        self.userdata = get_collection(COLLECTIONS['USERDATA'])
        self.raj_data = get_collection(COLLECTIONS['RAJ_DATA'])
        self.search_logs = get_collection(COLLECTIONS['SEARCH_LOGS'])
        self.hidden_records = get_collection(COLLECTIONS['HIDDEN_RECORDS'])
    
    def search_data(self, field, query):
        """Smart search with person-level hiding"""
        results = []

        # Step 1: Check if the search query itself is directly hidden
        if self.is_record_hidden(field, query):
            return results  # Return empty if query is directly hidden

        # Step 2: Perform the actual search (match old bot exactly)
        userdata_results = list(self.userdata.find({field: query}))
        raj_results = list(self.raj_data.find({field: query}))

        all_results = userdata_results + raj_results

        # Step 3: Filter results based on smart hiding logic
        for record in all_results:
            if self.should_hide_record(record, field):
                continue  # Skip this record
            results.append(record)

        return results
    
    def should_hide_record(self, record, search_field):
        """Check if a record should be hidden based on smart hiding rules"""
        # Check if record contains any hidden mobile numbers
        if record.get("mobile") and self.is_record_hidden("mobile", record["mobile"]):
            return True

        # Check if record contains any hidden IDs
        if record.get("id") and self.is_record_hidden("id", record["id"]):
            return True

        # Special case for alternate search: more permissive
        if search_field == "alt":
            # For alternate search, only hide if the record itself contains hidden values
            # Don't hide just because the search query matches a hidden value
            return False

        return False
    
    def is_record_hidden(self, field, value):
        """Check if a record is hidden"""
        return self.hidden_records.find_one({"field": field, "value": value}) is not None
    
    def log_search(self, user_id, query_type, query, results_count=0, success=True, credits_charged=0):
        """Enhanced search logging with success status and credit info"""
        log_doc = {
            "user_id": user_id,
            "query_type": query_type,
            "query": query,
            "results_count": results_count,
            "success": success,
            "credits_charged": credits_charged,
            "timestamp": datetime.now(timezone.utc),
        }
        self.search_logs.insert_one(log_doc)
        status = "success" if success else "failed"
        logger.info(f"Logged search: user={user_id}, type={query_type}, query={query}, results={results_count}, status={status}, credits={credits_charged}")
    
    def get_user_logs(self, user_id, limit=50):
        """Get user search logs"""
        return list(self.search_logs.find(
            {"user_id": user_id}
        ).sort("timestamp", -1).limit(limit))
    
    def get_all_logs(self, limit=100):
        """Get all search logs"""
        return list(self.search_logs.find().sort("timestamp", -1).limit(limit))

    def get_recent_logs(self, limit=1000):
        """Get recent search logs for analytics"""
        return list(self.search_logs.find().sort("timestamp", -1).limit(limit))

    def get_search_analytics(self):
        """Get enhanced search analytics"""
        try:
            # Get recent logs
            recent_logs = self.get_recent_logs(1000)

            total_searches = len(recent_logs)
            successful_searches = len([log for log in recent_logs if log.get("success", True)])
            failed_searches = total_searches - successful_searches

            # Calculate success rate by search type
            search_types = {}
            for log in recent_logs:
                search_type = log.get("query_type", "unknown")
                if search_type not in search_types:
                    search_types[search_type] = {"total": 0, "successful": 0}

                search_types[search_type]["total"] += 1
                if log.get("success", True):
                    search_types[search_type]["successful"] += 1

            # Calculate success rates
            for search_type in search_types:
                total = search_types[search_type]["total"]
                successful = search_types[search_type]["successful"]
                search_types[search_type]["success_rate"] = (successful / total * 100) if total > 0 else 0

            return {
                "total_searches": total_searches,
                "successful_searches": successful_searches,
                "failed_searches": failed_searches,
                "overall_success_rate": (successful_searches / total_searches * 100) if total_searches > 0 else 0,
                "search_types": search_types
            }

        except Exception as e:
            logger.error(f"Error getting search analytics: {e}")
            return {
                "total_searches": 0,
                "successful_searches": 0,
                "failed_searches": 0,
                "overall_success_rate": 0,
                "search_types": {}
            }
    
    def get_search_stats(self):
        """Get statistics from both collections"""
        userdata_count = self.userdata.count_documents({})
        raj_data_count = self.raj_data.count_documents({})
        hidden_count = self.hidden_records.count_documents({})

        return {
            "userdata_count": userdata_count,
            "raj_data_count": raj_data_count,
            "total_records": userdata_count + raj_data_count,
            "hidden_records_count": hidden_count
        }
    
    def get_recent_searches(self, limit=10):
        """Get recent searches across all users"""
        return list(self.search_logs.find().sort("timestamp", -1).limit(limit))
    
    def get_search_count_by_type(self):
        """Get search count grouped by type"""
        pipeline = [
            {"$group": {"_id": "$query_type", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        return list(self.search_logs.aggregate(pipeline))
    
    def get_top_searched_queries(self, query_type=None, limit=10):
        """Get most searched queries"""
        match_stage = {}
        if query_type:
            match_stage["query_type"] = query_type
        
        pipeline = [
            {"$match": match_stage},
            {"$group": {"_id": "$query", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": limit}
        ]
        return list(self.search_logs.aggregate(pipeline))
    
    def get_user_search_stats(self, user_id):
        """Get search statistics for a specific user"""
        total_searches = self.search_logs.count_documents({"user_id": user_id})
        
        # Get searches by type
        pipeline = [
            {"$match": {"user_id": user_id}},
            {"$group": {"_id": "$query_type", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        by_type = list(self.search_logs.aggregate(pipeline))
        
        # Get recent activity
        recent = list(self.search_logs.find(
            {"user_id": user_id}
        ).sort("timestamp", -1).limit(5))
        
        return {
            "total_searches": total_searches,
            "by_type": by_type,
            "recent_searches": recent
        }
    
    def cleanup_old_logs(self, days=30):
        """Clean up old search logs"""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        result = self.search_logs.delete_many({"timestamp": {"$lt": cutoff_date}})
        
        if result.deleted_count > 0:
            logger.info(f"Cleaned up {result.deleted_count} old search logs")
        
        return result.deleted_count

    def get_today_search_count(self):
        """Get today's search count (fast query)"""
        try:
            from datetime import datetime, timezone
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            return self.search_logs.count_documents({"timestamp": {"$gte": today_start}})
        except Exception as e:
            logger.error(f"Error getting today's search count: {e}")
            return 0
