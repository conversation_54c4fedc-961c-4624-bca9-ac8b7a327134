#!/usr/bin/env python3
# run_enhanced_bot.py
"""
Enhanced Bot Runner with Feature Validation
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Initialize error logging immediately
from utils.error_logger import error_logger
print("📋 Error logging initialized - all errors will be automatically logged")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Suppress verbose MongoDB heartbeat logs
logging.getLogger('pymongo').setLevel(logging.WARNING)
logging.getLogger('pymongo.topology').setLevel(logging.WARNING)
logging.getLogger('pymongo.serverSelection').setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

def validate_environment():
    """Validate environment and dependencies"""
    print("🔍 Validating Environment...")
    
    # Check required files
    required_files = [
        "config/settings.py",
        "config/constants.py", 
        "database/connection.py",
        "database/models/__init__.py",
        "handlers/__init__.py",
        "services/__init__.py",
        "ui/__init__.py",
        "main.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required files present")
    
    # Check Python packages
    try:
        import telegram
        import pymongo
        print("✅ Required packages available")
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        return False
    
    return True

def validate_enhanced_features():
    """Validate enhanced features are working"""
    print("\n🚀 Validating Enhanced Features...")
    
    try:
        # Test database models
        from database.models import UserModel, SettingsModel
        
        user_model = UserModel()
        settings_model = SettingsModel()
        
        print("✅ Enhanced database models loaded")
        
        # Test services
        from services.auth import AuthService
        
        auth_service = AuthService()
        print("✅ Enhanced auth service loaded")
        
        # Test admin handlers
        from handlers.admin.lea_management import LEAManagementHandler
        from handlers.admin.feature_management import FeatureManagementHandler
        from handlers.admin.pricing_management import PricingManagementHandler
        
        print("✅ Enhanced admin handlers loaded")
        
        # Test settings
        test_setting = settings_model.get_setting("global_pricing_enabled", True)
        print(f"✅ Settings system working: global_pricing_enabled = {test_setting}")
        
        # Test feature matrix
        matrix = settings_model.get_feature_visibility_matrix()
        print(f"✅ Feature visibility matrix: {len(matrix)} features")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced features validation failed: {e}")
        return False

def show_recent_logs():
    """Show recent error logs and interactions"""
    print("\n🚨 RECENT ERROR LOGS:")
    print("=" * 60)
    try:
        recent_errors = error_logger.get_recent_errors(30)
        if recent_errors.strip():
            print(recent_errors)
        else:
            print("✅ No recent errors found")
    except Exception as e:
        print(f"❌ Could not read error logs: {e}")

    print("\n📋 RECENT INTERACTIONS:")
    print("=" * 60)
    try:
        recent_interactions = error_logger.get_recent_interactions(50)
        if recent_interactions.strip():
            print(recent_interactions)
        else:
            print("ℹ️ No recent interactions found")
    except Exception as e:
        print(f"❌ Could not read interaction logs: {e}")

    print("\n📁 Log files location:")
    print("   - logs/bot_errors.log")
    print("   - logs/bot_interactions.log")

def show_feature_summary():
    """Show summary of enhanced features"""
    print("\n" + "="*60)
    print("🎉 ENHANCED BOT FEATURES READY!")
    print("="*60)
    
    features = [
        "🏛️ LEA Officer Management (30D/6M/1Y Plans)",
        "💰 Smart Pricing System (Global/Individual)",
        "🎯 Feature Visibility Matrix (Global/LEA/Normal)",
        "🔍 Success-Based Credit Deduction",
        "📊 Enhanced Statistics & Analytics",
        "📱 Page-Based Navigation",
        "🔔 User-Type Specific Notifications",
        "⚙️ Advanced Admin Panel",
        "📤 Enhanced Export System",
        "🛡️ Robust Error Handling",
        "📋 Comprehensive Error Logging System"
    ]
    
    for feature in features:
        print(f"   ✅ {feature}")
    
    print("\n📊 ERROR LOGGING SYSTEM:")
    print("   📁 Error logs: logs/bot_errors.log")
    print("   📁 Interaction logs: logs/bot_interactions.log")
    print("   🔍 Logs are automatically captured - check files above for details")

    print("\n" + "="*60)
    print("🚀 Your bot is ready with ALL enhanced features!")
    print("   • 100% backward compatible")
    print("   • All existing features preserved")
    print("   • Professional LEA management")
    print("   • Comprehensive error tracking")
    print("   • Smart dual-user system")
    print("="*60)

async def main():
    """Main function to run enhanced bot"""
    # Check for command line arguments first
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "logs":
            show_recent_logs()
            return
        elif command == "errors":
            print("\n🚨 RECENT ERROR LOGS:")
            print("=" * 60)
            print(error_logger.get_recent_errors(50))
            return
        elif command == "interactions":
            print("\n📋 RECENT INTERACTIONS:")
            print("=" * 60)
            print(error_logger.get_recent_interactions(100))
            return
        elif command == "clear":
            error_logger.clear_logs()
            print("✅ All logs cleared")
            return
        elif command in ["help", "-h", "--help"]:
            print("\n📖 ENHANCED BOT USAGE:")
            print("=" * 50)
            print("python run_enhanced_bot.py              - Start the bot")
            print("python run_enhanced_bot.py logs         - Show recent logs")
            print("python run_enhanced_bot.py errors       - Show error logs only")
            print("python run_enhanced_bot.py interactions - Show interaction logs only")
            print("python run_enhanced_bot.py clear        - Clear all logs")
            return

    print("🤖 Enhanced Search Bot - Starting...")
    print("="*50)

    # Validate environment
    if not validate_environment():
        print("\n❌ Environment validation failed. Please check requirements.")
        return

    # Validate enhanced features
    if not validate_enhanced_features():
        print("\n❌ Enhanced features validation failed. Please check implementation.")
        return

    # Show feature summary
    show_feature_summary()
    
    # Import and run the main bot
    try:
        print("\n🚀 Starting Enhanced Bot...")
        from main import TelegramBot

        bot = TelegramBot()
        await bot.run()

    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        # Log the error using our error logging system
        from utils.error_logger import log_error
        log_error(e, context="Bot startup/runtime error")

        logger.error(f"Bot error: {e}")
        print(f"\n❌ Bot error: {e}")
        print("💡 If you see connection errors, make sure MongoDB is running:")
        print("   sudo systemctl start mongod")

        # Show recent errors automatically
        print("\n🚨 RECENT ERROR LOGS:")
        print("=" * 50)
        try:
            recent_errors = error_logger.get_recent_errors(20)
            print(recent_errors)
        except:
            print("Could not retrieve error logs")

        print("\n📁 Full logs available at:")
        print("   - logs/bot_errors.log")
        print("   - logs/bot_interactions.log")

if __name__ == "__main__":
    # Check if this is a test run
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        print("🧪 Running in test mode...")
        
        # Validate environment and features
        env_ok = validate_environment()
        features_ok = validate_enhanced_features()
        
        if env_ok and features_ok:
            show_feature_summary()
            print("\n✅ All tests passed! Bot is ready to run.")
            sys.exit(0)
        else:
            print("\n❌ Tests failed! Please check the issues above.")
            sys.exit(1)
    else:
        # Run the bot
        try:
            asyncio.run(main())
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
        except Exception as e:
            print(f"\n❌ Fatal error: {e}")
            sys.exit(1)
