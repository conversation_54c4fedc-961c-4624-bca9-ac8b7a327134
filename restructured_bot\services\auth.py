# services/auth.py
import logging
from datetime import datetime, timezone
from database.models import UserModel, SettingsModel
from database.connection import get_collection
from config.constants import COLLECTIONS, ERROR_MESSAGES
from config.settings import DEFAULT_DAILY_SEARCH_LIMIT, DEFAULT_PER_MINUTE_LIMIT

logger = logging.getLogger(__name__)

class AuthService:
    """Enhanced authentication and authorization service with LEA support"""

    def __init__(self):
        self.user_model = UserModel()
        self.settings_model = SettingsModel()
        self.settings = get_collection(COLLECTIONS['SETTINGS'])
    
    def get_or_create_user(self, user_id, username=None, first_name=None, last_name=None):
        """Get existing user or create if authorized"""
        # Build full name from first and last name
        full_name = ""
        if first_name:
            full_name = first_name
            if last_name:
                full_name += f" {last_name}"
        
        final_username = username or full_name or f"user_{user_id}"
        
        return self.user_model.create_user_if_missing(user_id, final_username)
    
    def is_user_authorized(self, user):
        """Check if user is authorized to use the bot"""
        return self.user_model.is_user_authorized(user)
    
    def is_admin(self, user):
        """Check if user is admin"""
        return user and user.get("is_admin", False)

    def is_lea_officer(self, user):
        """Check if user is LEA officer"""
        return user and user.get("user_type") == "lea_officer"

    def can_search(self, user):
        """Check if user can perform searches"""
        return self.user_model.can_user_search(user)

    def check_credits(self, user, cost=1):
        """Enhanced credit check with LEA officer support"""
        if self.is_admin(user) or self.is_lea_officer(user):
            return True, None

        if user.get("credits", 0) < cost:
            return False, self.get_setting("insufficient_credits_message", ERROR_MESSAGES['INSUFFICIENT_CREDITS'])

        return True, None

    def deduct_credits(self, user_id, cost=1):
        """Deduct credits from user (LEA officers exempt)"""
        user = self.user_model.get_user(user_id)
        if not user:
            return False

        # LEA officers have unlimited credits
        if self.is_lea_officer(user):
            return True

        return self.user_model.update_user_credits(user_id, -cost, "add")
    
    def check_rate_limit(self, user):
        """Enhanced rate limiting with LEA officer exemption"""
        if self.is_admin(user) or self.is_lea_officer(user):
            return True, None

        if not self.get_setting("rate_limiting_enabled", True):
            return True, None
        
        # Check daily limit
        daily_limit = self.get_setting("daily_search_limit", DEFAULT_DAILY_SEARCH_LIMIT)
        today = datetime.now(timezone.utc).date()
        
        if user.get("last_search_day") != today.isoformat():
            # Reset daily counter
            self.user_model.collection.update_one(
                {"user_id": user["user_id"]}, 
                {"$set": {"search_count_today": 0, "last_search_day": today.isoformat()}}
            )
            user["search_count_today"] = 0
        
        if user.get("search_count_today", 0) >= daily_limit:
            return False, ERROR_MESSAGES['RATE_LIMITED'].format(limit_type="daily")
        
        # Check per-minute limit
        minute_limit = self.get_setting("per_minute_search_limit", DEFAULT_PER_MINUTE_LIMIT)
        now = datetime.now(timezone.utc)
        last_search = self.normalize_datetime(user.get("last_search_time"))
        
        if last_search and (now - last_search).total_seconds() < 60:
            minute_count = user.get("searches_this_minute", 0)
            if minute_count >= minute_limit:
                return False, ERROR_MESSAGES['RATE_LIMITED'].format(limit_type="per-minute")
        else:
            # Reset minute counter
            self.user_model.collection.update_one(
                {"user_id": user["user_id"]}, 
                {"$set": {"searches_this_minute": 0, "last_search_time": now}}
            )
        
        return True, None
    
    def update_search_activity(self, user):
        """Update user's search activity counters"""
        self.user_model.increment_search_count(user)
        self.user_model.update_search_time(user["user_id"])
    
    def get_user_plan(self, user):
        """Get user's plan status"""
        return self.user_model.get_plan(user)
    
    def get_user_info(self, user):
        """Enhanced user information with LEA officer support"""
        if not user:
            return None

        plan = self.get_user_plan(user)
        expiry = user.get("expiry")
        expiry_str = "Never" if self.is_admin(user) else (
            expiry.strftime("%Y-%m-%d") if expiry else "N/A"
        )

        # Enhanced info for LEA officers
        info = {
            "user_id": user["user_id"],
            "username": user.get("username", "Unknown"),
            "user_type": user.get("user_type", "normal_user"),
            "credits": user.get("credits", 0),
            "plan": plan,
            "expiry": expiry_str,
            "is_admin": self.is_admin(user),
            "is_lea_officer": self.is_lea_officer(user),
            "is_suspended": user.get("is_suspended", False),
            "search_count_today": user.get("search_count_today", 0),
            "total_searches": user.get("total_searches", 0),
            "successful_searches": user.get("successful_searches", 0),
            "credits_saved": user.get("credits_saved", 0)
        }

        # Add LEA specific info if applicable
        if self.is_lea_officer(user) and user.get("lea_info"):
            lea_info = user["lea_info"]
            info.update({
                "lea_name": lea_info.get("name", "Unknown"),
                "lea_department": lea_info.get("department", "Unknown"),
                "lea_badge": lea_info.get("badge_number", "Unknown"),
                "lea_rank": lea_info.get("rank", "Unknown"),
                "lea_contact": lea_info.get("contact", "Unknown"),
                "plan_duration": lea_info.get("plan_duration", "Unknown")
            })

        return info
    
    def normalize_datetime(self, dt):
        """Normalize datetime to UTC timezone"""
        if dt is None:
            return None
        if dt.tzinfo is None:
            return dt.replace(tzinfo=timezone.utc)
        return dt
    
    def get_setting(self, key, default=None):
        """Get a setting from database"""
        doc = self.settings.find_one({"_id": "config"})
        if not doc:
            return default
        return doc.get(key, default)
    
    def set_setting(self, key, value):
        """Set a setting in database"""
        self.settings.update_one(
            {"_id": "config"}, 
            {"$set": {key: value}}, 
            upsert=True
        )
        logger.info(f"Updated setting {key} = {value}")
    
    def is_feature_enabled(self, feature_name, user=None):
        """Enhanced feature check with user-type specific settings"""
        if not user:
            # Global check only
            return self.get_setting(f"{feature_name}_search_enabled", True)

        return self.settings_model.is_feature_enabled(feature_name, user.get("user_type", "normal_user"))

    def get_search_cost(self, search_type="mobile"):
        """Get search cost based on type and pricing settings"""
        return self.settings_model.get_search_cost(search_type)

    def get_pricing_display(self, user):
        """Get pricing display for user"""
        if not user:
            return ""

        return self.settings_model.get_pricing_display(user.get("user_type", "normal_user"))

    def update_search_stats(self, user_id, success=True, credits_charged=0):
        """Update search statistics"""
        self.user_model.update_search_stats(user_id, success, credits_charged)

    def get_expired_message(self, user):
        """Get appropriate expired message based on user type"""
        if not user:
            return self.get_setting("access_denied_message", "Access denied")

        if self.is_lea_officer(user):
            return self.get_setting("lea_plan_expired_message", "Your LEA plan has expired")
        else:
            return self.get_setting("plan_expired_message", "Your plan has expired")
    
    def validate_admin_access(self, user):
        """Validate admin access and return error message if not authorized"""
        if not user:
            return False, "User not found"
        
        if not self.is_admin(user):
            return False, "❌ **Access Denied**\n\nYou don't have admin privileges."
        
        return True, None
