# main.py
import logging
import asyncio
from telegram import Update
from telegram.constants import ParseMode
from telegram.ext import (
    Application<PERSON><PERSON>er,
    CommandHandler,
    MessageHandler,
    filters,
    ConversationHandler,
    CallbackQueryHandler,
    Defaults,
    ContextTypes,
)
import nest_asyncio

import config
import db_utils
import handlers
import admin

nest_asyncio.apply()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def fallback_callback_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle orphaned callback queries after bot restart"""
    query = update.callback_query
    await query.answer("Bot was restarted. Redirecting to main menu...")
    
    try:
        # Try to show main menu
        await handlers.show_main_menu(update, context)
    except:
        # If that fails, send a simple restart message
        restart_msg = db_utils.get_setting("bot_restart_message", "🔄 **CyberNetra is Updated.**\n\nPlease use /start to continue.")
        await query.edit_message_text(
            restart_msg,
            parse_mode=ParseMode.MARKDOWN
        )

async def main():
    """Main function to start the bot"""
    try:
        # Initialize database
        db_utils.init_defaults()
        logger.info("Database initialized successfully")

        # Create application
        app = ApplicationBuilder().token(config.BOT_TOKEN).defaults(
            Defaults(parse_mode="Markdown")
        ).build()

        # Main conversation handler
        conv_handler = ConversationHandler(
            entry_points=[
                CommandHandler("start", handlers.start_handler),
                CallbackQueryHandler(handlers.menu_handler, pattern="^start_bot$")
            ],
            states={
                config.MENU: [
                    CallbackQueryHandler(handlers.menu_handler),
                    CallbackQueryHandler(admin.admin_callback_handler, pattern="^admin_"),
                ],
                config.SEARCH_MENU: [
                    CallbackQueryHandler(handlers.menu_handler),
                ],
                config.MOBILE_SEARCH: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, handlers.handle_mobile_search),
                    CallbackQueryHandler(handlers.menu_handler),
                ],
                config.AADHAR_SEARCH: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, handlers.handle_aadhar_search),
                    CallbackQueryHandler(handlers.menu_handler),
                ],
                config.ALT_SEARCH: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, handlers.handle_alt_search),
                    CallbackQueryHandler(handlers.menu_handler),
                ],
                config.VEHICLE_SEARCH: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, handlers.handle_vehicle_search),
                    CallbackQueryHandler(handlers.menu_handler),
                ],
                config.ADMIN_PANEL: [
                    CallbackQueryHandler(admin.admin_callback_handler, pattern="^admin_"),
                    CallbackQueryHandler(handlers.menu_handler)
                ],
                config.ADMIN_ADD_USER: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, admin.handle_admin_input),
                    CallbackQueryHandler(admin.admin_callback_handler, pattern="^admin_"),
                ],
                config.ADMIN_SET_PRICE: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, admin.handle_admin_input),
                    CallbackQueryHandler(admin.admin_callback_handler, pattern="^admin_"),
                ],
                config.ADMIN_EDIT_MESSAGES: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, admin.handle_admin_input),
                    CallbackQueryHandler(admin.admin_callback_handler, pattern="^admin_"),
                ],
                config.ADMIN_VIEW_USER_LOGS: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, admin.handle_admin_input),
                    CallbackQueryHandler(admin.admin_callback_handler, pattern="^admin_"),
                ],
                config.ADMIN_REMOVE_VEHICLE: [
                    MessageHandler(filters.TEXT & ~filters.COMMAND, admin.admin_remove_vehicle_handler),
                    CallbackQueryHandler(admin.admin_callback_handler, pattern="^admin_"),
                ],
                config.VIEW_LOGS: [CallbackQueryHandler(handlers.menu_handler)],
                config.VIEW_USER_LOGS: [CallbackQueryHandler(handlers.menu_handler)],
            },
            fallbacks=[CommandHandler("cancel", handlers.cancel_handler)],
            allow_reentry=True,
            per_message=False,
        )

        app.add_handler(conv_handler)
        
        # Add fallback handler for orphaned callbacks (must be after conv_handler)
        app.add_handler(CallbackQueryHandler(fallback_callback_handler))
        
        # Admin command handlers
        app.add_handler(CommandHandler("adduser", admin.adduser_cmd))
        app.add_handler(CommandHandler("addcredits", admin.addcredits_cmd))
        app.add_handler(CommandHandler("setcredits", admin.setcredits_cmd))
        app.add_handler(CommandHandler("suspenduser", admin.suspenduser_cmd))
        app.add_handler(CommandHandler("unsuspenduser", admin.unsuspenduser_cmd))
        app.add_handler(CommandHandler("removeuser", admin.removeuser_cmd))
        app.add_handler(CommandHandler("setprice", admin.setprice_cmd))
        app.add_handler(CommandHandler("setprestart", admin.setprestart_cmd))
        app.add_handler(CommandHandler("setstart", admin.setstart_cmd))
        app.add_handler(CommandHandler("setpricingtext", admin.setpricingtext_cmd))
        app.add_handler(CommandHandler("setchanneltext", admin.setchanneltext_cmd))
        app.add_handler(CommandHandler("setchannellink", admin.setchannellink_cmd))
        app.add_handler(CommandHandler("togglenotifications", admin.toggle_notifications_cmd))
        app.add_handler(CommandHandler("hiderecord", admin.hiderecord_cmd))
        app.add_handler(CommandHandler("unhiderecord", admin.unhiderecord_cmd))
        app.add_handler(CommandHandler("listhidden", admin.listhidden_cmd))
        app.add_handler(CommandHandler("help", handlers.help_cmd))

        logger.info("Bot started and running...")
        await app.run_polling()

    except Exception as e:
        logger.error(f"An error occurred: {e}")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except RuntimeError as e:
        if "running event loop" in str(e):
            import nest_asyncio
            nest_asyncio.apply()
            asyncio.get_event_loop().run_until_complete(main())
        else:
            raise
