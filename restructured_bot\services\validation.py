# services/validation.py
import re
import logging
from config.constants import VALIDATION_PATTERNS, ERROR_MESSAGES

logger = logging.getLogger(__name__)

class ValidationService:
    """Input validation service"""
    
    @staticmethod
    def validate_mobile_number(input_text):
        """Validate mobile number input (10 digits, handle +91 prefix and spaces)"""
        if not input_text:
            return False, "❌ Please enter a mobile number"

        # Step 1: Remove +91 prefix if present
        cleaned_input = input_text.strip()
        if cleaned_input.startswith('+91'):
            cleaned_input = cleaned_input[3:]
        elif cleaned_input.startswith('91') and len(cleaned_input) == 12:
            # Handle case like "919876543210"
            cleaned_input = cleaned_input[2:]

        # Step 2: Remove all spaces
        cleaned_input = cleaned_input.replace(' ', '')

        # Step 3: Check if only digits
        if not cleaned_input.isdigit():
            return False, "❌ Please enter only numbers (no letters or special characters)"

        # Step 4: Check length (exactly 10 digits)
        if len(cleaned_input) != 10:
            return False, ERROR_MESSAGES['INVALID_MOBILE'].format(length=len(cleaned_input))

        return True, cleaned_input
    
    @staticmethod
    def validate_aadhar_number(input_text):
        """Validate ID/Aadhar number input (exactly 12 digits)"""
        if not input_text:
            return False, "❌ Please enter an ID/Aadhar number"

        # Step 1: Remove all spaces and dashes
        cleaned_input = input_text.strip().replace(' ', '').replace('-', '')

        # Step 2: Check if only digits
        if not cleaned_input.isdigit():
            return False, "❌ Please enter only numbers (no letters or special characters)"

        # Step 3: Check length (exactly 12 digits)
        if len(cleaned_input) != 12:
            return False, ERROR_MESSAGES['INVALID_AADHAR'].format(length=len(cleaned_input))

        return True, cleaned_input
    
    @staticmethod
    def validate_vehicle_number(input_text):
        """Validate vehicle number input (7-10 characters, no special characters)"""
        if not input_text:
            return False, "❌ Please enter a vehicle number"

        # Step 1: Remove spaces and dashes, convert to uppercase
        cleaned_input = input_text.strip().replace(' ', '').replace('-', '').upper()

        # Step 2: Check length (7-10 characters)
        if len(cleaned_input) < 7:
            return False, f"❌ Please enter at least 7 characters (you entered {len(cleaned_input)} characters)"
        elif len(cleaned_input) > 10:
            return False, f"❌ Please enter maximum 10 characters (you entered {len(cleaned_input)} characters)"

        # Step 3: Check if only letters and numbers (no special characters)
        if not cleaned_input.isalnum():
            return False, ERROR_MESSAGES['INVALID_VEHICLE']

        return True, cleaned_input
    
    @staticmethod
    def validate_alternate_number(input_text):
        """Validate alternate number (same as mobile number)"""
        return ValidationService.validate_mobile_number(input_text)
    
    @staticmethod
    def validate_user_id(input_text):
        """Validate Telegram user ID"""
        if not input_text:
            return False, "❌ Please enter a user ID"
        
        cleaned_input = input_text.strip()
        
        if not cleaned_input.isdigit():
            return False, "❌ User ID must be numeric"
        
        user_id = int(cleaned_input)
        if user_id <= 0:
            return False, "❌ User ID must be positive"
        
        return True, user_id
    
    @staticmethod
    def validate_credits(input_text):
        """Validate credits amount"""
        if not input_text:
            return False, "❌ Please enter credits amount"
        
        cleaned_input = input_text.strip()
        
        try:
            credits = int(cleaned_input)
            if credits < 0:
                return False, "❌ Credits cannot be negative"
            if credits > 999999:
                return False, "❌ Credits amount too large"
            return True, credits
        except ValueError:
            return False, "❌ Credits must be a number"
    
    @staticmethod
    def validate_expiry_days(input_text):
        """Validate expiry days"""
        if not input_text:
            return False, "❌ Please enter expiry days"
        
        cleaned_input = input_text.strip().lower()
        
        if cleaned_input == "lifetime":
            return True, 365 * 100  # 100 years
        
        try:
            days = int(cleaned_input)
            if days <= 0:
                return False, "❌ Expiry days must be positive"
            if days > 36500:  # 100 years
                return False, "❌ Expiry days too large"
            return True, days
        except ValueError:
            return False, "❌ Expiry days must be a number or 'lifetime'"
    
    @staticmethod
    def validate_admin_command_format(text, expected_parts):
        """Validate admin command format"""
        if not text:
            return False, "❌ Please provide the required information"
        
        parts = text.strip().split()
        if len(parts) < expected_parts:
            return False, f"❌ Invalid format. Expected {expected_parts} parameters"
        
        return True, parts
    
    @staticmethod
    def validate_hide_record_format(text):
        """Validate hide record command format"""
        if not text:
            return False, "❌ Please provide field:value:reason format"
        
        parts = text.split(':', 2)  # Split into max 3 parts
        if len(parts) < 2:
            return False, "❌ Invalid format. Use: field:value:reason"
        
        field = parts[0].strip().lower()
        value = parts[1].strip()
        reason = parts[2].strip() if len(parts) > 2 else ""
        
        # Validate field
        valid_fields = ['mobile', 'id', 'alt']
        if field not in valid_fields:
            return False, f"❌ Invalid field. Use: {', '.join(valid_fields)}"
        
        # Validate value based on field
        if field == 'mobile' or field == 'alt':
            is_valid, result = ValidationService.validate_mobile_number(value)
            if not is_valid:
                return False, result
            value = result
        elif field == 'id':
            is_valid, result = ValidationService.validate_aadhar_number(value)
            if not is_valid:
                return False, result
            value = result
        
        return True, (field, value, reason)
    
    @staticmethod
    def validate_search_input(search_type, input_text):
        """Validate search input based on search type"""
        validators = {
            'mobile': ValidationService.validate_mobile_number,
            'aadhar': ValidationService.validate_aadhar_number,
            'alt': ValidationService.validate_alternate_number,
            'vehicle': ValidationService.validate_vehicle_number
        }
        
        validator = validators.get(search_type)
        if not validator:
            return False, f"❌ Unknown search type: {search_type}"
        
        return validator(input_text)
    
    @staticmethod
    def sanitize_input(input_text, max_length=100):
        """Sanitize user input"""
        if not input_text:
            return ""
        
        # Remove excessive whitespace
        sanitized = ' '.join(input_text.strip().split())
        
        # Limit length
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
        
        return sanitized
    
    @staticmethod
    def validate_reason(reason_text, max_length=200):
        """Validate reason text"""
        if not reason_text:
            return True, ""
        
        sanitized = ValidationService.sanitize_input(reason_text, max_length)
        return True, sanitized
