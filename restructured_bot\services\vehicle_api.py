# services/vehicle_api.py
import logging
import asyncio
import requests
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from config.settings import VEHICLE_API_TIMEOUT

logger = logging.getLogger(__name__)

class VehicleAPIService:
    """Service for calling vehicle APIs"""
    
    @staticmethod
    async def call_vehicle_apis(vehicle_number):
        """Call all 4 vehicle APIs with timeout"""
        
        def call_rc_api(vehicle_number):
            """Call RC Information API"""
            try:
                url = "https://glacier.on-track.in/api/external/v2/rc_info"
                headers = {
                    "user-agent": "Dart/3.4 (dart:io)",
                    "content-type": "application/json",
                    "accept-encoding": "gzip",
                    "host": "glacier.on-track.in"
                }
                data = {"vehicleId": vehicle_number}
                response = requests.post(url, headers=headers, json=data, timeout=VEHICLE_API_TIMEOUT)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"RC API error: {e}")
                return None

        def call_gtplay_api(vehicle_number):
            """Call GTPlay Vehicle Info API"""
            try:
                url = "https://gtplay.in/API/vehicle_challan_info/vehicle_info.php"
                headers = {
                    "Host": "gtplay.in",
                    "content-type": "application/x-www-form-urlencoded",
                    "accept-encoding": "gzip",
                    "user-agent": "okhttp/4.10.0"
                }
                data = {"vehicle_no": vehicle_number}
                response = requests.post(url, headers=headers, data=data, timeout=VEHICLE_API_TIMEOUT)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"GTPlay API error: {e}")
                return None

        def call_challan_api(vehicle_number):
            """Call Challan Details API"""
            try:
                url = "https://core.cuvora.com/car/velocity/armor/v1/challan/scrape"
                headers = {
                    "searchcount": "3",
                    "masktype": "NORMAL",
                    "deviceheight": "2306",
                    "appflavour": "car",
                    "appversion": "492",
                    "model": "23049PCD8I",
                    "accept-encoding": "gzip",
                    "segment": "5",
                    "locale": "en",
                    "authorization": "",
                    "insurancebannertype": "default",
                    "os_version": "33",
                    "clientid": "android_car-info",
                    "appopencount": "7",
                    "salt": "255",
                    "ghacfk": "234b2a89a7231524bc1fd88ed6c70fd2bb2b0a9945b5e398f532343f7efd7c38ed5a943cef041df6460f51ee7ccec4da713a03d4515889eba0124cc26da50095",
                    "ts": "1745396259079",
                    "src": "android_car-info",
                    "analyticsappinstanceid": "a71f70ddb739dcc25239ce50d7efba02",
                    "content-type": "application/json; charset=UTF-8",
                    "user-agent": "okhttp/4.12.0"
                }
                data = {"vehicleNumber": vehicle_number}
                response = requests.post(url, headers=headers, json=data, timeout=VEHICLE_API_TIMEOUT)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Challan API error: {e}")
                return None

        def call_fastag_api(vehicle_number):
            """Call FASTag Details API"""
            try:
                url = f'https://b2c-gateway-india.c24.tech/api/v3/bill-payment/fastag/register?inputParamValue={vehicle_number}'
                headers = {
                    'Host': 'b2c-gateway-india.c24.tech',
                    'accept': 'application/json, text/plain, */*',
                    'x_vehicle_type': 'CAR',
                    'x_country': 'IN',
                    'source': 'MobileApp',
                    'pincode': 'undefined',
                    'usercityid': '',
                    'appversion': '',
                    'osname': 'android',
                    'mediasource': 'HELLO_AR',
                    'useragent': 'cars24CustomerApp/',
                    'userid': 'b6cc0c21-e235-48c0-a429-5ab1ce3b5bbb',
                    'x-user-city-id': '',
                    'user-agent': 'okhttp/4.12.0'
                }
                response = requests.get(url, headers=headers, timeout=VEHICLE_API_TIMEOUT)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"FASTag API error: {e}")
                return None

        # Execute all APIs in parallel with ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=4) as executor:
            tasks = [
                loop.run_in_executor(executor, call_rc_api, vehicle_number),
                loop.run_in_executor(executor, call_gtplay_api, vehicle_number),
                loop.run_in_executor(executor, call_challan_api, vehicle_number),
                loop.run_in_executor(executor, call_fastag_api, vehicle_number)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

        return {
            "rc_data": results[0] if not isinstance(results[0], Exception) else None,
            "gtplay_data": results[1] if not isinstance(results[1], Exception) else None,
            "challan_data": results[2] if not isinstance(results[2], Exception) else None,
            "fastag_data": results[3] if not isinstance(results[3], Exception) else None
        }
