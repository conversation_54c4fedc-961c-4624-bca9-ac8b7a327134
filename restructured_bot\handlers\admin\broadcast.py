# handlers/admin/broadcast.py
import logging
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.base import <PERSON>Handler
from config.constants import ADMIN_PANEL, ADMIN_ADD_USER

logger = logging.getLogger(__name__)

class AdminBroadcastHandler(BaseHandler):
    """Handler for admin broadcast functionality"""
    
    def __init__(self):
        super().__init__()
    
    async def admin_broadcast_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show comprehensive broadcast settings"""
        query = update.callback_query
        await query.answer()

        text = (
            f"📢 **Broadcast Message Center**\n\n"
            f"🎯 **Target Audiences:**\n"
            f"• All Users - Everyone in database\n"
            f"• LEA Officers - Law enforcement only\n"
            f"• Expired Users - Plans have expired\n"
            f"• Expiring Soon - Plans expire in 3 days\n"
            f"• Active/Premium/Trial Users\n\n"
            f"⏰ **Features:**\n"
            f"• Instant sending or scheduled\n"
            f"• Message preview before sending\n"
            f"• Delivery confirmation"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("📢 All Users", callback_data="broadcast_all"),
                InlineKeyboardButton("🏛️ LEA Officers", callback_data="broadcast_lea")
            ],
            [
                InlineKeyboardButton("⏰ Expired Users", callback_data="broadcast_expired"),
                InlineKeyboardButton("🔔 Expiring Soon", callback_data="broadcast_expiring")
            ],
            [
                InlineKeyboardButton("👥 Active Users", callback_data="broadcast_active"),
                InlineKeyboardButton("💎 Premium Users", callback_data="broadcast_premium")
            ],
            [
                InlineKeyboardButton("🆓 Trial Users", callback_data="broadcast_trial"),
                InlineKeyboardButton("⏰ Schedule Broadcast", callback_data="broadcast_schedule")
            ],
            [InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin_panel")]
        ])

        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

        return ADMIN_PANEL
    
    async def admin_broadcast_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE, broadcast_type: str):
        """Show broadcast message prompt"""
        query = update.callback_query
        await query.answer()
        
        context.user_data["admin_state"] = f"broadcast_{broadcast_type}"
        
        type_text = {
            "broadcast": "All Users",
            "all": "All Users",
            "active": "Active Users Only",
            "premium": "Premium Users Only",
            "trial": "Trial Users Only",
            "lea": "LEA Officers Only",
            "expired": "Expired Users Only",
            "expiring": "Users Expiring Soon",
            "schedule": "Scheduled Broadcast"
        }.get(broadcast_type, "All Users")

        # Get user counts for each type
        from database.models import UserModel
        from datetime import datetime, timedelta
        user_model = UserModel()
        all_users = user_model.get_all_users()

        if broadcast_type in ["broadcast", "all"]:
            target_count = len(all_users)
        elif broadcast_type == "active":
            target_count = len([u for u in all_users if user_model.is_user_authorized(u)])
        elif broadcast_type == "premium":
            target_count = len([u for u in all_users if user_model.get_plan(u) == "Premium"])
        elif broadcast_type == "trial":
            target_count = len([u for u in all_users if user_model.get_plan(u) == "Trial"])
        elif broadcast_type == "lea":
            target_count = len([u for u in all_users if u.get("user_type") == "lea_officer"])
        elif broadcast_type == "expired":
            target_count = len([u for u in all_users if not user_model.is_user_authorized(u)])
        elif broadcast_type == "expiring":
            # Users expiring in next 3 days
            from datetime import timezone
            expiring_users = []
            for user in all_users:
                expiry = user_model.normalize_datetime(user.get("expiry"))
                if expiry:
                    days_left = (expiry - datetime.now(timezone.utc)).days
                    if 0 <= days_left <= 3:
                        expiring_users.append(user)
            target_count = len(expiring_users)
        else:
            target_count = 0
        
        await query.edit_message_text(
            f"📢 **Send Broadcast to {type_text}**\n\n"
            f"Target Users: {target_count:,}\n\n"
            f"Please send the message you want to broadcast:",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_broadcast")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_ADD_USER
    
    async def admin_export_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show export options menu"""
        query = update.callback_query
        await query.answer()
        
        # Get statistics
        from database.models import VehicleModel, UserModel, SearchModel
        vehicle_model = VehicleModel()
        user_model = UserModel()
        search_model = SearchModel()
        
        vehicle_stats = vehicle_model.get_vehicle_database_stats()
        vehicle_count = vehicle_stats.get("total_vehicles", 0) if vehicle_stats else 0
        
        user_count = len(user_model.get_all_users())
        
        # Get log count (approximate)
        try:
            log_count = len(search_model.get_all_logs(1000))  # Sample count
        except:
            log_count = 0
        
        text = (
            f"📤 **Export Data**\n\n"
            f"Choose what data to export:\n\n"
            f"👥 **All Users**: Export user data ({user_count:,} users)\n"
            f"📊 **Search Logs**: Export search history ({log_count:,}+ logs)\n"
            f"🚗 **Vehicle Database**: Export vehicle database ({vehicle_count:,} vehicles)"
        )
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("👥 All Users", callback_data="admin_export_users"),
                InlineKeyboardButton("📊 Search Logs", callback_data="admin_export_logs")
            ],
            [
                InlineKeyboardButton("🚗 Vehicle Database", callback_data="admin_export_vehicles")
            ],
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ])
        
        await query.edit_message_text(text, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL
    
    async def admin_export_users(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Export users to CSV"""
        query = update.callback_query
        await query.answer()
        
        try:
            from database.models import UserModel
            import csv
            import io
            from datetime import datetime
            
            user_model = UserModel()
            users = user_model.get_all_users()
            
            # Create CSV
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                'User ID', 'Username', 'Credits', 'Plan', 'Expiry', 
                'Is Admin', 'Is Suspended', 'Created At', 'Search Count Today'
            ])
            
            # Write user data
            for user in users:
                expiry = user.get('expiry')
                expiry_str = expiry.strftime('%Y-%m-%d') if expiry else 'N/A'
                
                created_at = user.get('created_at')
                created_str = created_at.strftime('%Y-%m-%d') if created_at else 'N/A'
                
                writer.writerow([
                    user.get('user_id', ''),
                    user.get('username', ''),
                    user.get('credits', 0),
                    user_model.get_plan(user),
                    expiry_str,
                    user.get('is_admin', False),
                    user.get('is_suspended', False),
                    created_str,
                    user.get('search_count_today', 0)
                ])
            
            # Send file
            csv_data = output.getvalue().encode('utf-8')
            filename = f"users_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            await context.bot.send_document(
                chat_id=update.effective_chat.id,
                document=io.BytesIO(csv_data),
                filename=filename,
                caption=f"📊 **Users Export**\n\nTotal Users: {len(users):,}\nGenerated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            await query.edit_message_text(
                f"✅ **Export Complete**\n\nUsers exported successfully!\nTotal: {len(users):,} users",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error exporting users: {e}")
            await query.edit_message_text(
                "❌ **Export Failed**\n\nAn error occurred while exporting users.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
        
        return ADMIN_PANEL
    
    async def admin_export_logs(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Export search logs to CSV"""
        query = update.callback_query
        await query.answer()
        
        try:
            from database.models import SearchModel
            import csv
            import io
            from datetime import datetime
            
            search_model = SearchModel()
            logs = search_model.get_all_logs(1000)  # Limit to prevent memory issues
            
            # Create CSV
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow(['User ID', 'Query Type', 'Query', 'Results Count', 'Timestamp'])
            
            # Write log data
            for log in logs:
                timestamp = log.get('timestamp')
                timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S') if timestamp else 'N/A'
                
                writer.writerow([
                    log.get('user_id', ''),
                    log.get('query_type', ''),
                    log.get('query', ''),
                    log.get('results_count', 0),
                    timestamp_str
                ])
            
            # Send file
            csv_data = output.getvalue().encode('utf-8')
            filename = f"search_logs_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            await context.bot.send_document(
                chat_id=update.effective_chat.id,
                document=io.BytesIO(csv_data),
                filename=filename,
                caption=f"📊 **Search Logs Export**\n\nTotal Logs: {len(logs):,}\nGenerated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            await query.edit_message_text(
                f"✅ **Export Complete**\n\nSearch logs exported successfully!\nTotal: {len(logs):,} logs",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error exporting logs: {e}")
            await query.edit_message_text(
                "❌ **Export Failed**\n\nAn error occurred while exporting logs.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
        
        return ADMIN_PANEL
    
    async def admin_export_vehicles(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Export vehicle database to CSV"""
        query = update.callback_query
        await query.answer()
        
        try:
            from database.models import VehicleModel
            import csv
            import io
            from datetime import datetime
            
            vehicle_model = VehicleModel()
            vehicles = vehicle_model.get_all_vehicles(1000)  # Limit to prevent memory issues
            
            # Create CSV
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                'Vehicle Number', 'Search Count', 'First Searched', 'Last Searched', 
                'Created By User', 'Data Sources'
            ])
            
            # Write vehicle data
            for vehicle in vehicles:
                first_searched = vehicle.get('first_searched')
                first_str = first_searched.strftime('%Y-%m-%d %H:%M:%S') if first_searched else 'N/A'
                
                last_searched = vehicle.get('last_searched')
                last_str = last_searched.strftime('%Y-%m-%d %H:%M:%S') if last_searched else 'N/A'
                
                data_sources = ', '.join(vehicle.get('data_sources', []))
                
                writer.writerow([
                    vehicle.get('vehicle_number', ''),
                    vehicle.get('search_count', 0),
                    first_str,
                    last_str,
                    vehicle.get('created_by_user', ''),
                    data_sources
                ])
            
            # Send file
            csv_data = output.getvalue().encode('utf-8')
            filename = f"vehicles_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            await context.bot.send_document(
                chat_id=update.effective_chat.id,
                document=io.BytesIO(csv_data),
                filename=filename,
                caption=f"📊 **Vehicle Database Export**\n\nTotal Vehicles: {len(vehicles):,}\nGenerated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            await query.edit_message_text(
                f"✅ **Export Complete**\n\nVehicle database exported successfully!\nTotal: {len(vehicles):,} vehicles",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error exporting vehicles: {e}")
            await query.edit_message_text(
                "❌ **Export Failed**\n\nAn error occurred while exporting vehicles.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
        
        return ADMIN_PANEL

    async def handle_broadcast_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast callbacks"""
        query = update.callback_query
        data = query.data

        broadcast_type = data.replace("broadcast_", "")

        if broadcast_type == "lea":
            return await self.admin_broadcast_prompt(update, context, "lea")
        elif broadcast_type == "expired":
            return await self.admin_broadcast_prompt(update, context, "expired")
        elif broadcast_type == "expiring":
            return await self.admin_broadcast_prompt(update, context, "expiring")
        elif broadcast_type == "all":
            return await self.admin_broadcast_prompt(update, context, "all")
        elif broadcast_type == "schedule":
            return await self.admin_broadcast_prompt(update, context, "schedule")
        else:
            return await self.admin_broadcast_prompt(update, context, broadcast_type)
