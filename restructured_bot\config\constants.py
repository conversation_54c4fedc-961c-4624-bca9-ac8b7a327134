# config/constants.py

# Conversation States
MENU, SEARCH_MENU, <PERSON><PERSON><PERSON><PERSON>_SEARCH, <PERSON><PERSON><PERSON><PERSON>_SEARCH, ALT_SEARCH, VEHICLE_SEARCH = range(6)
ADMIN_PANEL, ADMIN_ADD_USER, ADMIN_EDIT_USER, ADMIN_VIEW_USERS = range(6, 10)
ADMIN_VIEW_LOGS, ADMIN_SET_PRICE, ADMIN_EDIT_MESSAGES = range(10, 13)
VIEW_LOGS, VIEW_USER_LOGS, ADMIN_VIEW_USER_LOGS = range(13, 16)
ADMIN_REMOVE_VEHICLE = 16

# Search Types
SEARCH_TYPES = {
    'MOBILE': 'mobile',
    'AADHAR': 'id',
    'ALTERNATE': 'alt',
    'VEHICLE': 'vehicle'
}

# User Plans
USER_PLANS = {
    'ADMIN': 'Admin',
    'PREMIUM': 'Premium',
    'TRIAL': 'Trial',
    'FREE': 'Free',
    'EXPIRED': 'Expired'
}

# Database Collections
COLLECTIONS = {
    'USERDATA': 'userdata',
    'RAJ_DATA': 'raj_data',
    'USERS': 'users',
    'SETTINGS': 'settings',
    'SEARCH_LOGS': 'search_logs',
    'NOTIFICATIONS': 'notifications',
    'HIDDEN_RECORDS': 'hidden_records',
    'VEHICLE_DATA': 'vehicledata'
}

# Field Mappings
FIELD_MAPPINGS = {
    'userdata': {
        'mobile': 'mobile',
        'id': 'id',
        'alt': 'alt',
        'name': 'name',
        'address': 'address',
        'careof': 'careof',
        'fname': 'fname',
        'operator': 'operator',
        'circle': 'circle',
        'dob': 'dob'
    },
    'raj_data': {
        'mobile': 'mobile',
        'id': 'id',
        'alt': 'AlternateNo',  # Different field name in raj_data
        'name': 'name',
        'address': 'address',
        'careof': 'careof',
        'operator': 'operator',
        'dob': 'dob'
    }
}

# Display Field Order and Labels
DISPLAY_FIELDS = [
    ("mobile", "📱", "Mobile"),
    ("name", "👤", "Name"),
    ("careof", "👥", "Father's Name"),
    ("fname", "👥", "Father's Name"),
    ("address", "📍", "Address"),
    ("operator", "📡", "Operator"),
    ("circle", "📡", "Circle"),
    ("alt", "📞", "Alternative No."),
    ("id", "🆔", "Govt ID/Aadhar ID"),
    ("dob", "🎂", "DOB")
]

# Validation Patterns
VALIDATION_PATTERNS = {
    'MOBILE': r'^\d{10}$',
    'AADHAR': r'^\d{12}$',
    'VEHICLE': r'^[A-Z0-9]{7,10}$'
}

# Error Messages
ERROR_MESSAGES = {
    'INVALID_MOBILE': "❌ Please enter exactly 10 digits (you entered {length} digits)",
    'INVALID_AADHAR': "❌ Please enter exactly 12 digits (you entered {length} digits)",
    'INVALID_VEHICLE': "❌ Please enter 7-10 characters (letters and numbers only)",
    'INSUFFICIENT_CREDITS': "❌ **Insufficient Credits**\n\nYou don't have enough credits to perform this search.\nContact admin to recharge your account.",
    'ACCESS_DENIED': "❌ **Access Denied**\n\nYou are not authorized to use this bot. Please contact the administrator to get access.",
    'NO_RESULTS': "❌ **No Results Found**\n\nNo data found for {field}: `{query}`",
    'RATE_LIMITED': "⏰ **Rate Limited**\n\nYou have exceeded the {limit_type} limit. Please try again later."
}

# Success Messages
SUCCESS_MESSAGES = {
    'USER_ADDED': "✅ User added successfully",
    'CREDITS_ADDED': "✅ Credits added successfully",
    'USER_SUSPENDED': "✅ User suspended successfully",
    'USER_UNSUSPENDED': "✅ User unsuspended successfully",
    'RECORD_HIDDEN': "✅ Record hidden successfully",
    'RECORD_UNHIDDEN': "✅ Record unhidden successfully"
}

# Default Bot Messages
DEFAULT_MESSAGES = {
    'START_MESSAGE': "👮 Welcome to Law Enforcement Bot!",
    'PRICING_TEXT': "Each search costs 1 credit.\nContact admin to recharge.",
    'JOIN_CHANNEL_TEXT': "📢 Join Channel",
    'JOIN_CHANNEL_LINK': "https://t.me/your_channel_here",
    'BOT_RESTART_MESSAGE': "🔄 **CyberNetra is Updated.**\n\nPlease use /start to continue."
}

# Emojis
EMOJIS = {
    'MOBILE': '📱',
    'AADHAR': '🆔',
    'ALTERNATE': '📞',
    'VEHICLE': '🚗',
    'ADMIN': '⚙️',
    'SEARCH': '🔍',
    'SUCCESS': '✅',
    'ERROR': '❌',
    'WARNING': '⚠️',
    'INFO': 'ℹ️',
    'BACK': '⬅️',
    'HOME': '🏠',
    'STATS': '📊',
    'LOGS': '📑',
    'USERS': '👥',
    'SETTINGS': '⚙️',
    'HIDDEN': '🙈',
    'EXPORT': '📤'
}
