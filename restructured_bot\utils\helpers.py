# utils/helpers.py
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

def normalize_datetime(dt: Optional[datetime]) -> Optional[datetime]:
    """Normalize datetime to UTC timezone"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt

def format_datetime(dt: Optional[datetime], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """Format datetime to string"""
    if not dt:
        return "N/A"
    
    normalized_dt = normalize_datetime(dt)
    return normalized_dt.strftime(format_str)

def safe_get(dictionary: Dict[str, Any], key: str, default: Any = None) -> Any:
    """Safely get value from dictionary"""
    try:
        return dictionary.get(key, default)
    except (AttributeError, TypeError):
        return default

def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate string to specified length"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def clean_phone_number(phone: str) -> str:
    """Clean and normalize phone number"""
    if not phone:
        return ""
    
    # Remove common prefixes and formatting
    cleaned = phone.strip()
    if cleaned.startswith('+91'):
        cleaned = cleaned[3:]
    elif cleaned.startswith('91') and len(cleaned) == 12:
        cleaned = cleaned[2:]
    
    # Remove spaces, dashes, and other formatting
    cleaned = ''.join(char for char in cleaned if char.isdigit())
    
    return cleaned

def format_number(number: int) -> str:
    """Format number with commas"""
    try:
        return f"{number:,}"
    except (ValueError, TypeError):
        return str(number)

def get_user_display_name(user_data: Dict[str, Any]) -> str:
    """Get display name for user"""
    username = safe_get(user_data, 'username', '')
    user_id = safe_get(user_data, 'user_id', '')
    
    if username and not username.startswith('user_'):
        return username
    
    return f"User {user_id}"

def calculate_expiry_days(expiry_date: Optional[datetime]) -> int:
    """Calculate days until expiry"""
    if not expiry_date:
        return 0
    
    normalized_expiry = normalize_datetime(expiry_date)
    now = datetime.now(timezone.utc)
    
    if normalized_expiry <= now:
        return 0
    
    delta = normalized_expiry - now
    return delta.days

def is_expired(expiry_date: Optional[datetime]) -> bool:
    """Check if date has expired"""
    if not expiry_date:
        return True
    
    normalized_expiry = normalize_datetime(expiry_date)
    now = datetime.now(timezone.utc)
    
    return normalized_expiry <= now

def make_usernames_clickable(text):
    """Make @usernames clickable in text"""
    import re
    # Replace @username with clickable link
    pattern = r'@(\w+)'
    return re.sub(pattern, r'[@\1](tg://user?username=\1)', text)
