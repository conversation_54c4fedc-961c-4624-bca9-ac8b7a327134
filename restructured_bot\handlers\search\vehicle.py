# handlers/search/vehicle.py
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.search.base_search import <PERSON><PERSON>earchHandler
from config.constants import MENU, VEHICLE_SEARCH
from services.validation import ValidationService
from database.models import VehicleModel, SearchModel, UserModel
from services.formatting import FormattingService

logger = logging.getLogger(__name__)

class VehicleSearchHandler(BaseSearchHandler):
    """Handler for vehicle number searches"""
    
    def __init__(self):
        super().__init__()
        self.vehicle_model = VehicleModel()
        self.search_model = SearchModel()
        self.user_model = UserModel()  # Fix: Add missing user_model
    
    async def handle_vehicle_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle vehicle number input"""
        user = await self.check_user_authorization(update, context)
        if not user:
            return MENU
        
        input_text = update.message.text.strip()
        
        # Validate input
        is_valid, result = ValidationService.validate_vehicle_number(input_text)
        if not is_valid:
            await self.send_validation_error(update, context, result)
            return VEHICLE_SEARCH
        
        query_text = result
        
        # Check credits
        cost = self.auth_service.get_search_cost("vehicle")
        has_credits, credit_msg = self.auth_service.check_credits(user, cost)
        if not has_credits:
            await self.send_insufficient_credits(update, context)
            return MENU
        
        # Check rate limits
        rate_ok, rate_msg = self.auth_service.check_rate_limit(user)
        if not rate_ok:
            limit_type = rate_msg.split()[-1] if rate_msg else "rate"
            await self.send_rate_limited(update, context, limit_type)
            return MENU
        
        # Check our vehicle database first
        cached_vehicle = self.vehicle_model.get_cached_vehicle(query_text)
        
        if cached_vehicle:
            # Found in our database - instant response!
            self.vehicle_model.update_vehicle_search_stats(query_text)
            
            # Store results for search again functionality
            context.user_data["vehicle_results"] = [cached_vehicle["formatted_result"]]
            context.user_data["current_search_field"] = "vehicle"
            
            # Deduct credits and log search
            self.search_model.log_search(user["user_id"], "vehicle", query_text, 1)
            
            if not self.auth_service.is_admin(user):
                self.auth_service.deduct_credits(user["user_id"], cost)
                self.auth_service.update_search_activity(user)
            
            # Send results from our database
            result_text = cached_vehicle['formatted_result']
            if self.auth_service.is_admin(user):
                result_text = f"📊 **From Our Database**\n\n{result_text}"
            
            keyboard = self.pagination_service.create_search_result_keyboard(0, 1, "vehicle")
            
            await update.message.reply_text(
                result_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            
            self.log_user_action(user["user_id"], "vehicle_search_cached", f"{query_text}")
            return MENU
        
        # Not in cache - search APIs
        wait_msg = await update.message.reply_text(
            f"🔍 **Searching for {query_text}...**\n\nPlease wait while we gather comprehensive vehicle information.",
            parse_mode=ParseMode.MARKDOWN
        )

        try:
            # Call all 4 APIs with timeout
            from services.vehicle_api import VehicleAPIService
            vehicle_data = await VehicleAPIService.call_vehicle_apis(query_text)

            if not any(vehicle_data.values()):
                await wait_msg.edit_text(
                    f"❌ **Vehicle Not Found**\n\n"
                    f"No information found for vehicle number: `{query_text}`\n\n"
                    f"This could mean:\n"
                    f"• Vehicle number doesn't exist\n"
                    f"• Vehicle not registered in available databases\n"
                    f"• Temporary service unavailability",
                    reply_markup=self.keyboard_builder.no_results_keyboard(),
                    parse_mode=ParseMode.MARKDOWN
                )

                # Store empty results for search again functionality
                context.user_data["vehicle_results"] = []
                context.user_data["current_search_field"] = "vehicle"

                # Log search but don't deduct credits for no results
                self.search_model.log_search(user["user_id"], "vehicle", query_text, 0)
                self.log_user_action(user["user_id"], "vehicle_search_no_results", query_text)
                return MENU

            # Format and display results
            formatted_result = FormattingService.format_vehicle_results(vehicle_data, query_text)

            # Store in our permanent database
            self.vehicle_model.store_vehicle_data(query_text, vehicle_data, formatted_result, user["user_id"])

            # Store results for search again functionality
            context.user_data["vehicle_results"] = [formatted_result]
            context.user_data["current_search_field"] = "vehicle"

            # Enhanced: Results found - now deduct credits
            credits_charged = 0
            if not self.auth_service.is_admin(user) and not self.auth_service.is_lea_officer(user):
                if self.auth_service.deduct_credits(user["user_id"], cost):
                    credits_charged = cost
                    self.auth_service.update_search_activity(user)

            # Enhanced: Log successful search with credit info
            self.search_model.log_search(user["user_id"], "vehicle", query_text, 1, success=True)
            self.auth_service.update_search_stats(user["user_id"], success=True, credits_charged=credits_charged)

            # Show results first (separate message)
            await self.show_vehicle_results(update, formatted_result)

            # Then show action buttons (separate message)
            await self.show_search_action_buttons(update, context, "vehicle", 1)

            # Finally show success message (separate message)
            await self.show_search_success_message(update, user, credits_charged, context)

            self.log_user_action(user["user_id"], "vehicle_search_success", f"{query_text}")
            return MENU

        except Exception as e:
            logger.error(f"Error in vehicle search: {e}")
            await wait_msg.edit_text(
                "❌ **Search Error**\n\nAn error occurred while searching. Please try again.",
                reply_markup=self.keyboard_builder.search_input_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )
            return MENU
    
    async def show_vehicle_results(self, update: Update, formatted_result: str):
        """Show vehicle results separately from buttons and success message"""
        # Send results as separate message WITHOUT any buttons
        await update.message.reply_text(
            f"📊 **Vehicle Information Found**\n\n{formatted_result}",
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_pagination(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page: int):
        """Handle pagination for vehicle search results - Not needed for single results"""
        # Vehicle search typically returns single result, so just redirect to main menu
        await update.callback_query.answer("Vehicle search shows single result")
        return await self.show_main_menu(update, context)
