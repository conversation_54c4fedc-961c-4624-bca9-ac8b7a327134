# database/models/user.py
import logging
from datetime import datetime, timezone, timedelta
from database.connection import get_collection
from config.constants import COLLECTIONS, USER_PLANS
from config.settings import DEFAULT_TRIAL_CREDITS

logger = logging.getLogger(__name__)

class UserModel:
    """User model for managing bot users"""
    
    def __init__(self):
        self.collection = get_collection(COLLECTIONS['USERS'])
        self.settings = get_collection(COLLECTIONS['SETTINGS'])
    
    def get_user(self, user_id):
        """Get user by user_id"""
        return self.collection.find_one({"user_id": user_id})
    
    def get_user_by_username(self, username):
        """Get user by username"""
        return self.collection.find_one({"username": username})
    
    def get_all_users(self):
        """Get all users"""
        return list(self.collection.find({}))
    
    def create_user_if_missing(self, user_id, username):
        """Create user if missing, considering open access setting"""
        user = self.get_user(user_id)
        if user:
            # Update username if it has changed and is not generic
            current_username = user.get('username', '')
            if username and not username.startswith('user_') and current_username != username:
                self.collection.update_one(
                    {"user_id": user_id},
                    {"$set": {"username": username}}
                )
                user['username'] = username
            return user

        # Check if open access is enabled
        open_access = self.get_setting("open_access_enabled", False)
        if not open_access:
            return None  # User not authorized

        # Create new trial user
        trial_credits = self.get_setting("trial_credits", DEFAULT_TRIAL_CREDITS)

        doc = {
            "user_id": user_id,
            "username": username or f"user_{user_id}",
            "user_type": "normal_user",  # Enhanced: user type system
            "credits": trial_credits,
            "expiry": datetime.now(timezone.utc) + timedelta(days=7),  # 7 days trial
            "is_admin": False,
            "is_suspended": False,
            "suspended_until": None,
            "plan": USER_PLANS['TRIAL'],
            "plan_type": "NORMAL-TRIAL",  # Enhanced: detailed plan tracking
            "search_limit_daily": None,
            "search_count_today": 0,
            "last_search_day": None,
            "created_at": datetime.now(timezone.utc),

            # Enhanced: LEA Officer specific fields
            "lea_info": None,

            # Enhanced: Search limits and tracking
            "vehicle_searches_remaining": None,
            "total_searches": 0,
            "successful_searches": 0,
            "credits_saved": 0,  # Credits saved from failed searches
            "last_activity": datetime.now(timezone.utc)
        }
        self.collection.insert_one(doc)
        logger.info(f"Created trial user: {user_id}")
        return doc
    
    def add_user(self, user_id, username, credits=5, expiry_days=30, plan="Free"):
        """Add new normal user (admin function)"""
        if self.get_user(user_id):
            return False, "User already exists"

        doc = {
            "user_id": user_id,
            "username": username or "unknown",
            "user_type": "normal_user",
            "credits": credits,
            "expiry": datetime.now(timezone.utc) + timedelta(days=expiry_days),
            "is_admin": False,
            "is_suspended": False,
            "suspended_until": None,
            "plan": plan,
            "plan_type": f"NORMAL-{plan.upper()}",
            "search_limit_daily": None,
            "search_count_today": 0,
            "last_search_day": None,
            "created_at": datetime.now(timezone.utc),

            # Enhanced fields
            "lea_info": None,
            "vehicle_searches_remaining": None,
            "total_searches": 0,
            "successful_searches": 0,
            "credits_saved": 0,
            "last_activity": datetime.now(timezone.utc)
        }
        self.collection.insert_one(doc)
        logger.info(f"Added user: {user_id} with {credits} credits")
        return True, "User added successfully"

    def add_lea_officer(self, user_id, name, department, badge_number, rank, contact, plan_duration="30D", send_welcome=True):
        """Add new LEA officer (admin function)"""
        if self.get_user(user_id):
            return False, "User already exists"

        # Calculate expiry based on plan
        if plan_duration == "30D":
            expiry_days = 30
        elif plan_duration == "6M":
            expiry_days = 180
        elif plan_duration == "1Y":
            expiry_days = 365
        else:
            expiry_days = 30

        # LEA officers get unlimited credits
        doc = {
            "user_id": user_id,
            "username": name.replace(" ", "_").lower(),
            "user_type": "lea_officer",
            "credits": 999999,  # Unlimited
            "expiry": datetime.now(timezone.utc) + timedelta(days=expiry_days),
            "is_admin": False,
            "is_suspended": False,
            "suspended_until": None,
            "plan": "Premium",  # Always show as Premium to user
            "plan_type": f"LEA-{plan_duration}",
            "search_limit_daily": None,
            "search_count_today": 0,
            "last_search_day": None,
            "created_at": datetime.now(timezone.utc),

            # LEA Officer specific information
            "lea_info": {
                "name": name,
                "department": department,
                "badge_number": badge_number,
                "rank": rank,
                "contact": contact,
                "plan_duration": plan_duration,
                "welcome_sent": send_welcome
            },

            # Search limits (configurable per LEA if needed)
            "vehicle_searches_remaining": self.get_setting("lea_vehicle_search_limit", None),
            "total_searches": 0,
            "successful_searches": 0,
            "credits_saved": 0,
            "last_activity": datetime.now(timezone.utc)
        }

        self.collection.insert_one(doc)
        logger.info(f"Added LEA officer: {user_id} ({name}) from {department}")
        return True, f"LEA Officer {name} added successfully"

    def bulk_add_lea_officers(self, user_ids, plan_duration="30D", send_welcome=True):
        """Bulk add LEA officers"""
        results = []
        for user_id in user_ids:
            try:
                user_id = int(user_id.strip())
                success, message = self.add_lea_officer(
                    user_id,
                    f"Officer_{user_id}",
                    "Department_TBD",
                    f"BADGE_{user_id}",
                    "Officer",
                    "Contact_TBD",
                    plan_duration,
                    send_welcome
                )
                results.append({"user_id": user_id, "success": success, "message": message})
            except Exception as e:
                results.append({"user_id": user_id, "success": False, "message": str(e)})

        return results

    def convert_user_type(self, user_id, new_type, **kwargs):
        """Convert user between normal_user and lea_officer"""
        user = self.get_user(user_id)
        if not user:
            return False, "User not found"

        current_type = user.get("user_type", "normal_user")

        if current_type == new_type:
            return False, f"User is already {new_type}"

        if new_type == "lea_officer":
            # Convert normal user to LEA officer
            update_data = {
                "user_type": "lea_officer",
                "credits": 999999,  # Unlimited
                "plan": "Premium",
                "plan_type": f"LEA-{kwargs.get('plan_duration', '30D')}",
                "lea_info": {
                    "name": kwargs.get('name', f"Officer_{user_id}"),
                    "department": kwargs.get('department', 'Department_TBD'),
                    "badge_number": kwargs.get('badge_number', f'BADGE_{user_id}'),
                    "rank": kwargs.get('rank', 'Officer'),
                    "contact": kwargs.get('contact', 'Contact_TBD'),
                    "plan_duration": kwargs.get('plan_duration', '30D'),
                    "welcome_sent": False
                },
                "vehicle_searches_remaining": self.get_setting("lea_vehicle_search_limit", None)
            }

            # Extend expiry if needed
            if kwargs.get('plan_duration') == '6M':
                update_data["expiry"] = datetime.now(timezone.utc) + timedelta(days=180)
            elif kwargs.get('plan_duration') == '1Y':
                update_data["expiry"] = datetime.now(timezone.utc) + timedelta(days=365)

        else:  # Convert LEA officer to normal user
            # Give trial or specified credits
            trial_credits = kwargs.get('credits', self.get_setting("trial_credits", 5))
            trial_days = kwargs.get('days', 7)

            update_data = {
                "user_type": "normal_user",
                "credits": trial_credits,
                "plan": "Trial" if trial_credits <= 10 else "Premium",
                "plan_type": f"NORMAL-{'TRIAL' if trial_credits <= 10 else 'PREMIUM'}",
                "lea_info": None,
                "vehicle_searches_remaining": None,
                "expiry": datetime.now(timezone.utc) + timedelta(days=trial_days)
            }

        result = self.collection.update_one({"user_id": user_id}, {"$set": update_data})
        if result.modified_count > 0:
            logger.info(f"Converted user {user_id} from {current_type} to {new_type}")
            return True, f"User converted to {new_type} successfully"

        return False, "Failed to convert user"

    def update_user_credits(self, user_id, credits, operation="set"):
        """Update user credits (set or add)"""
        if operation == "set":
            result = self.collection.update_one({"user_id": user_id}, {"$set": {"credits": credits}})
        else:  # add
            result = self.collection.update_one({"user_id": user_id}, {"$inc": {"credits": credits}})
        
        if result.modified_count > 0:
            logger.info(f"Updated credits for user {user_id}: {operation} {credits}")
        return result.modified_count > 0
    
    def add_credits(self, user_id, credits, expiry_days=None):
        """Add credits to existing user"""
        user = self.get_user(user_id)
        if not user:
            return False
        
        update_data = {"$inc": {"credits": credits}}
        
        if expiry_days:
            new_expiry = datetime.now(timezone.utc) + timedelta(days=expiry_days)
            update_data["$set"] = {"expiry": new_expiry}
        
        result = self.collection.update_one({"user_id": user_id}, update_data)
        if result.modified_count > 0:
            logger.info(f"Added {credits} credits to user {user_id}")
        return result.modified_count > 0
    
    def suspend_user(self, user_id, days=None):
        """Suspend a user"""
        update_data = {"is_suspended": True}
        if days:
            suspend_until = datetime.now(timezone.utc) + timedelta(days=days)
            update_data["suspended_until"] = suspend_until
        else:
            update_data["suspended_until"] = None
        
        result = self.collection.update_one({"user_id": user_id}, {"$set": update_data})
        if result.modified_count > 0:
            logger.info(f"Suspended user {user_id}")
        return result.modified_count > 0
    
    def unsuspend_user(self, user_id):
        """Unsuspend a user"""
        result = self.collection.update_one(
            {"user_id": user_id}, 
            {"$set": {"is_suspended": False, "suspended_until": None}}
        )
        if result.modified_count > 0:
            logger.info(f"Unsuspended user {user_id}")
        return result.modified_count > 0
    
    def remove_user(self, user_id):
        """Remove a user"""
        result = self.collection.delete_one({"user_id": user_id})
        if result.deleted_count > 0:
            logger.info(f"Removed user {user_id}")
        return result.deleted_count > 0
    
    def remove_user_by_username(self, username):
        """Remove user by username"""
        result = self.collection.delete_one({"username": username})
        if result.deleted_count > 0:
            logger.info(f"Removed user by username: {username}")
        return result.deleted_count > 0

    def get_total_user_count(self):
        """Get total user count (fast query)"""
        try:
            return self.collection.count_documents({})
        except Exception as e:
            logger.error(f"Error getting total user count: {e}")
            return 0

    def convert_to_lea_officer(self, user_id):
        """Convert a regular user to LEA officer"""
        try:
            # Check if user exists
            user = self.get_user_by_id(user_id)
            if not user:
                return False, "User not found"

            # Check if already LEA officer
            if user.get("user_type") == "lea_officer":
                return False, "User is already an LEA officer"

            # Update user to LEA officer
            update_data = {
                "user_type": "lea_officer",
                "credits": 999999,  # Unlimited credits
                "expiry_date": datetime.now(timezone.utc) + timedelta(days=365*10),  # 10 years
                "lea_info": {
                    "converted_date": datetime.now(timezone.utc),
                    "plan_duration": "converted",
                    "unlimited_access": True
                }
            }

            result = self.collection.update_one(
                {"user_id": user_id},
                {"$set": update_data}
            )

            if result.modified_count > 0:
                logger.info(f"Converted user {user_id} to LEA officer")
                return True, f"Successfully converted user {user_id} to LEA officer"
            else:
                return False, "Failed to update user"

        except Exception as e:
            logger.error(f"Error converting user to LEA officer: {e}")
            return False, f"Error: {str(e)}"

    def increment_search_count(self, user):
        """Increment daily search count"""
        today = datetime.now(timezone.utc).date()
        self.collection.update_one(
            {"user_id": user["user_id"]}, 
            {
                "$set": {"last_search_day": today.isoformat()},
                "$inc": {"search_count_today": 1}
            }
        )
    
    def update_search_time(self, user_id):
        """Update last search time and minute counter"""
        now = datetime.now(timezone.utc)
        self.collection.update_one(
            {"user_id": user_id}, 
            {
                "$set": {"last_search_time": now},
                "$inc": {"searches_this_minute": 1}
            }
        )
    
    def get_setting(self, key, default=None):
        """Get a setting from database"""
        doc = self.settings.find_one({"_id": "config"})
        if not doc:
            return default
        return doc.get(key, default)
    
    def normalize_datetime(self, dt):
        """Normalize datetime to UTC timezone"""
        if dt is None:
            return None
        if dt.tzinfo is None:
            return dt.replace(tzinfo=timezone.utc)
        return dt
    
    def is_user_authorized(self, user):
        """Enhanced authorization check with LEA officer support"""
        if not user:
            return False

        # Check suspension
        if user.get("is_suspended", False):
            suspend_until = self.normalize_datetime(user.get("suspended_until"))
            if suspend_until and suspend_until > datetime.now(timezone.utc):
                return False
            else:
                # Auto-unsuspend if time expired
                self.collection.update_one(
                    {"user_id": user["user_id"]},
                    {"$set": {"is_suspended": False, "suspended_until": None}}
                )

        # Admin always authorized
        if user.get("is_admin", False):
            return True

        # LEA officers have different authorization logic
        if user.get("user_type") == "lea_officer":
            # LEA officers can interact even if expired, but can't search
            return True

        # Normal users need credits and valid expiry
        if user.get("credits", 0) <= 0:
            return False

        expiry = self.normalize_datetime(user.get("expiry"))
        if expiry < datetime.now(timezone.utc):
            return False

        return True

    def can_user_search(self, user):
        """Check if user can perform searches (different from authorization)"""
        if not user or not self.is_user_authorized(user):
            return False

        # Admin can always search
        if user.get("is_admin", False):
            return True

        # LEA officers need valid expiry to search
        if user.get("user_type") == "lea_officer":
            expiry = self.normalize_datetime(user.get("expiry"))
            return expiry >= datetime.now(timezone.utc)

        # Normal users already checked in is_user_authorized
        return True
    
    def get_plan(self, user):
        """Enhanced plan detection with LEA officer support"""
        if user.get("is_admin"):
            return USER_PLANS['ADMIN']

        # LEA officers always show as Premium (even if expired)
        if user.get("user_type") == "lea_officer":
            expiry = self.normalize_datetime(user.get("expiry"))
            if expiry < datetime.now(timezone.utc):
                return "Expired"  # Special case for expired LEA
            return "Premium Plan"  # Always show as Premium to LEA officers

        # Normal users
        expiry = self.normalize_datetime(user.get("expiry"))
        if expiry < datetime.now(timezone.utc) or user.get("credits", 0) <= 0:
            return USER_PLANS['EXPIRED']

        if user.get("credits", 0) > self.get_setting("trial_credits", 5):
            return USER_PLANS['PREMIUM']

        return USER_PLANS['TRIAL']

    def get_lea_officers(self, active_only=False):
        """Get all LEA officers"""
        query = {"user_type": "lea_officer"}

        if active_only:
            query["expiry"] = {"$gte": datetime.now(timezone.utc)}

        return list(self.collection.find(query))

    def get_expiring_lea_officers(self, days=3):
        """Get LEA officers expiring within specified days"""
        expiry_threshold = datetime.now(timezone.utc) + timedelta(days=days)

        return list(self.collection.find({
            "user_type": "lea_officer",
            "expiry": {
                "$gte": datetime.now(timezone.utc),
                "$lte": expiry_threshold
            }
        }))

    def get_user_statistics(self):
        """Get enhanced user statistics"""
        total_users = self.collection.count_documents({})
        normal_users = self.collection.count_documents({"user_type": "normal_user"})
        lea_officers = self.collection.count_documents({"user_type": "lea_officer"})
        active_lea = self.collection.count_documents({
            "user_type": "lea_officer",
            "expiry": {"$gte": datetime.now(timezone.utc)}
        })

        return {
            "total_users": total_users,
            "normal_users": normal_users,
            "lea_officers": lea_officers,
            "active_lea_officers": active_lea,
            "expired_lea_officers": lea_officers - active_lea
        }

    def update_search_stats(self, user_id, success=True, credits_charged=0):
        """Update enhanced search statistics"""
        update_data = {
            "$inc": {
                "total_searches": 1,
                "search_count_today": 1
            },
            "$set": {
                "last_activity": datetime.now(timezone.utc)
            }
        }

        if success:
            update_data["$inc"]["successful_searches"] = 1
        else:
            # Credits saved from failed search
            update_data["$inc"]["credits_saved"] = credits_charged

        self.collection.update_one({"user_id": user_id}, update_data)
