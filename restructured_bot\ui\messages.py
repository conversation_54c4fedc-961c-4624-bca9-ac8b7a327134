# ui/messages.py
from services.auth import AuthService
from config.constants import DEFAULT_MESSAGES, EMOJIS

class MessageTemplates:
    """Templates for bot messages"""
    
    def __init__(self):
        self.auth_service = AuthService()
    
    def welcome_message(self, user=None):
        """Enhanced welcome message with LEA support"""
        start_message = self.auth_service.get_setting("start_message", DEFAULT_MESSAGES['START_MESSAGE'])

        if user:
            username = user.get('username', 'User')
            credits = user.get('credits', 0)
            plan = self.auth_service.get_user_plan(user)
            is_lea = self.auth_service.is_lea_officer(user)

            if is_lea:
                # Enhanced LEA Officer welcome with full details
                lea_info = user.get('lea_info', {})
                lea_name = lea_info.get('name', username)
                department = lea_info.get('department', 'Unknown Department')
                plan_duration = lea_info.get('plan_duration', '30D')
                user_id = user.get('user_id', 'Unknown')

                # Get actual expiry date from user data or calculate from creation
                from datetime import datetime, timedelta
                expiry_date = user.get('expiry_date')

                if expiry_date:
                    # Use actual expiry date from database
                    if isinstance(expiry_date, str):
                        expiry_str = expiry_date
                    else:
                        expiry_str = expiry_date.strftime("%d %B %Y")
                else:
                    # Fallback: calculate from plan duration (for display purposes)
                    if plan_duration == "30D":
                        expiry_date = datetime.now() + timedelta(days=30)
                        plan_text = "Premium Plan (30 Days)"
                    elif plan_duration == "6M":
                        expiry_date = datetime.now() + timedelta(days=180)
                        plan_text = "Premium Plan (6 Months)"
                    elif plan_duration == "1Y":
                        expiry_date = datetime.now() + timedelta(days=365)
                        plan_text = "Premium Plan (1 Year)"
                    else:
                        expiry_date = datetime.now() + timedelta(days=30)
                        plan_text = "Premium Plan"

                    expiry_str = expiry_date.strftime("%d %B %Y")

                # Set plan text based on duration
                if plan_duration == "30D":
                    plan_text = "Premium Plan (30 Days)"
                elif plan_duration == "6M":
                    plan_text = "Premium Plan (6 Months)"
                elif plan_duration == "1Y":
                    plan_text = "Premium Plan (1 Year)"
                else:
                    plan_text = "Premium Plan"

                return (f"{start_message}\n\n"
                       f"🆔 **Your Unique User ID:** {user_id}\n\n"
                       f"👮 **Welcome, Officer {lea_name}!**\n"
                       f"🏛️ **Department:** {department}\n"
                       f"📋 **Plan:** {plan_text}\n"
                       f"💎 **Credits:** Unlimited\n"
                       f"⏰ **Expiry:** {expiry_str}\n\n"
                       f"🔓 You have unlimited search access and special LEA privileges.\n"
                       f"Choose an option below:")
            else:
                # Normal user welcome
                return (f"{start_message}\n\n"
                       f"👤 **Welcome, {username}!**\n"
                       f"💳 **Credits:** {credits}\n"
                       f"📋 **Plan:** {plan}\n\n"
                       f"Choose an option below:")

        return f"{start_message}\n\nChoose an option below:"
    
    def search_prompt_message(self, search_type, cost=1, user=None):
        """Enhanced search prompt message with LEA support"""
        is_lea = user and self.auth_service.is_lea_officer(user)
        suffix = self.auth_service.get_setting("pricing_suffix", "if results found")

        # LEA officers don't see pricing
        cost_text = "" if is_lea else f"💰 **Per Search Cost:** {cost} credits {suffix}\n\n"

        prompts = {
            'mobile': (
                f"{EMOJIS['MOBILE']} **Enter Mobile Number:**\n\n"
                f"Please enter the mobile number you want to search for.\n"
                f"{cost_text}"
                f"*Accepts: 9876543210, +************, or 91 9876 543 210*"
            ),
            'aadhar': (
                f"{EMOJIS['AADHAR']} **Enter ID/Aadhar Number:**\n\n"
                f"Please enter the ID/Aadhar number you want to search for.\n"
                f"{cost_text}"
                f"*Accepts: 123456789012, 1234 5678 9012, or 1234-5678-9012*"
            ),
            'alt': (
                f"{EMOJIS['ALTERNATE']} **Enter Alternate Number:**\n\n"
                f"Please enter the alternate number you want to search for.\n"
                f"{cost_text}"
                f"*Accepts: 9876543210, +************, or 91 9876 543 210*"
            ),
            'vehicle': (
                f"{EMOJIS['VEHICLE']} **Enter Vehicle Number:**\n\n"
                f"Please enter the vehicle registration number you want to search for.\n"
                f"{cost_text}"
                f"*Examples: DL1AB123, MH12345, UP16BC7890*"
            )
        }

        return prompts.get(search_type, f"Please enter the {search_type} you want to search for.")
    
    def searching_message(self, query):
        """Enhanced searching message with credit notice"""
        credit_notice = self.auth_service.get_setting("credit_deduction_notice", "Credits will be deducted only if results are found")
        return f"🔍 **Searching for {query}...**\n\nPlease wait while we search the database.\n\n*{credit_notice}*"
    
    def no_results_message(self, field_label, query):
        """Get no results message"""
        return (f"❌ **No Results Found**\n\n"
               f"No data found for {field_label}: `{query}`")
    
    def insufficient_credits_message(self):
        """Get insufficient credits message"""
        return self.auth_service.get_setting(
            "insufficient_credits_message",
            "❌ **Insufficient Credits**\n\nYou don't have enough credits to perform this search.\nContact admin to recharge your account."
        )
    
    def access_denied_message(self):
        """Get access denied message"""
        return self.auth_service.get_setting(
            "access_denied_message",
            "❌ **Access Denied**\n\nYou are not authorized to use this bot. Please contact the administrator to get access."
        )

    def access_denied_with_user_id_message(self, user_id, username=None, status="expired"):
        """Get access denied message WITH user ID for easy copying"""
        if status == "expired":
            status_text = "Your account has expired"
        elif status == "suspended":
            status_text = "Your account has been suspended"
        elif status == "not_registered":
            status_text = "You are not registered"
        else:
            status_text = "You are not authorized"

        user_display = f"**User ID:** `{user_id}`"
        if username:
            user_display += f"\n**Username:** @{username}"

        return (
            f"❌ **Access Required**\n\n"
            f"{status_text}. Please contact the administrator for access.\n\n"
            f"📋 **Your Information:**\n"
            f"{user_display}\n\n"
            f"💡 **Copy your User ID above and send it to the admin to get access.**"
        )
    
    def rate_limited_message(self, limit_type):
        """Get rate limited message"""
        return f"⏰ **Rate Limited**\n\nYou have exceeded the {limit_type} limit. Please try again later."
    
    def pricing_message(self):
        """Get pricing information message"""
        pricing_text = self.auth_service.get_setting("pricing_text", DEFAULT_MESSAGES['PRICING_TEXT'])
        search_cost = self.auth_service.get_search_cost()
        
        return (f"💰 **Pricing Information**\n\n"
               f"{pricing_text}\n\n"
               f"💳 **Current Rate:** {search_cost} credit per search")
    
    def admin_panel_message(self):
        """Get admin panel message"""
        return f"{EMOJIS['ADMIN']} **Admin Panel**\n\nChoose an option:"
    
    def admin_access_denied_message(self):
        """Get admin access denied message"""
        return "❌ **Access Denied**\n\nYou don't have admin privileges."
    
    def user_management_message(self):
        """Get user management message"""
        return f"{EMOJIS['USERS']} **User Management**\n\nChoose an action:"
    
    def hidden_records_message(self, count=0):
        """Get hidden records message"""
        return (f"{EMOJIS['HIDDEN']} **Hidden Records Management**\n\n"
               f"📊 **Total Hidden Records:** {count}\n\n"
               f"Choose an action:")
    
    def vehicle_data_message(self):
        """Get vehicle data management message"""
        return f"{EMOJIS['VEHICLE']} **Vehicle Data Management**\n\nChoose an action:"
    
    def search_settings_message(self):
        """Get search settings message"""
        mobile_status = "Enabled" if self.auth_service.is_feature_enabled("mobile_search") else "Disabled"
        aadhar_status = "Enabled" if self.auth_service.is_feature_enabled("aadhar_search") else "Disabled"
        alt_status = "Enabled" if self.auth_service.is_feature_enabled("alternate_search") else "Disabled"
        vehicle_status = "Enabled" if self.auth_service.is_feature_enabled("vehicle_search") else "Disabled"
        
        return (f"🔍 **Search Settings**\n\n"
               f"📱 **Mobile Search:** {mobile_status}\n"
               f"🆔 **Aadhar Search:** {aadhar_status}\n"
               f"📞 **Alternate Search:** {alt_status}\n"
               f"🚗 **Vehicle Search:** {vehicle_status}\n\n"
               f"Click to toggle:")
    
    def add_user_prompt_message(self):
        """Get add user prompt message"""
        return ("➕ **Add New User**\n\n"
               "Please send user details in this format:\n"
               "`user_id credits expiry_days`\n\n"
               "Examples:\n"
               "`123456789 100 30`\n\n"
               "Expiry days: 1, 2, 7, 15, 30, or 'lifetime'")
    
    def add_credits_prompt_message(self):
        """Get add credits prompt message"""
        return ("💳 **Add Credits**\n\n"
               "Please send in this format:\n"
               "`user_id credits expiry_days`\n\n"
               "Examples:\n"
               "`123456789 50 30`\n\n"
               "Expiry days: 1, 2, 7, 15, 30, or 'lifetime'")
    
    def hide_record_prompt_message(self):
        """Get hide record prompt message"""
        return ("🙈 **Smart Hide Person**\n\n"
               "This will hide a complete person and all their associated records.\n\n"
               "**Format:** `field:value:reason`\n\n"
               "**Fields:** mobile, id, alt\n\n"
               "**Examples:**\n"
               "`mobile:9876543210:Privacy request`\n"
               "`id:123456789012:Admin friend`\n"
               "`alt:9123456789:Personal`\n\n"
               "**Smart Features:**\n"
               "• Finds all records with this identifier\n"
               "• Automatically hides associated IDs\n"
               "• Protects person's complete privacy\n"
               "• Preserves alternate search functionality\n\n"
               "**Note:** Reason is optional but recommended")
    
    def unhide_record_prompt_message(self):
        """Get unhide record prompt message"""
        return ("👁‍🗨 **Smart Unhide Person**\n\n"
               "This will unhide a person and all their associated records.\n\n"
               "**Format:** `field:value`\n\n"
               "**Fields:** mobile, id, alt\n\n"
               "**Examples:**\n"
               "`mobile:9876543210`\n"
               "`id:123456789012`\n"
               "`alt:9123456789`")
    
    def remove_vehicle_prompt_message(self):
        """Get remove vehicle prompt message"""
        return ("🗑️ **Remove Vehicle Data**\n\n"
               "Enter the vehicle number you want to remove from our database.\n\n"
               "⚠️ **Warning**: This action cannot be undone!\n\n"
               "Example: DL1AB123")
    
    def bot_restart_message(self):
        """Get bot restart message"""
        return self.auth_service.get_setting("bot_restart_message", DEFAULT_MESSAGES['BOT_RESTART_MESSAGE'])
    
    def feature_disabled_message(self, feature_name):
        """Get feature disabled message"""
        return f"❌ **Feature Disabled**\n\n{feature_name} is currently disabled by the administrator."
    
    def maintenance_message(self):
        """Get maintenance message"""
        return ("🔧 **Maintenance Mode**\n\n"
               "The bot is currently under maintenance. Please try again later.")
    
    def success_message(self, message):
        """Get success message"""
        return f"{EMOJIS['SUCCESS']} {message}"
    
    def error_message(self, message):
        """Get error message"""
        return f"{EMOJIS['ERROR']} {message}"
    
    def warning_message(self, message):
        """Get warning message"""
        return f"{EMOJIS['WARNING']} {message}"
    
    def info_message(self, message):
        """Get info message"""
        return f"{EMOJIS['INFO']} {message}"

    # Enhanced LEA Officer specific messages
    def lea_plan_management_message(self):
        """Get LEA plan management message"""
        return f"🏛️ **LEA Plan Management**\n\nManage law enforcement officer plans and pricing:"

    def add_lea_officer_prompt_message(self):
        """Get add LEA officer prompt message"""
        return ("👮 **Add LEA Officer**\n\n"
               "Please send LEA officer details in this format:\n"
               "`user_id|name|department|badge|contact|rank|plan`\n\n"
               "**Example:**\n"
               "`123456789|John Smith|Mumbai Police|MP001|+************|Inspector|30D`\n\n"
               "**Available Plans:** 30D, 6M, 1Y\n"
               "**Note:** All fields are required")

    def bulk_lea_assignment_prompt_message(self):
        """Get bulk LEA assignment prompt message"""
        return ("📋 **Bulk LEA Assignment**\n\n"
               "Please send multiple User IDs (one per line):\n\n"
               "**Example:**\n"
               "```\n123456789\n987654321\n456789123```\n\n"
               "**Note:** All will get the same plan duration you select next.")

    def convert_user_type_message(self):
        """Get convert user type message"""
        return f"🔄 **Convert User Type**\n\nConvert between LEA Officer and Normal User:"

    def feature_visibility_message(self):
        """Get feature visibility message"""
        return f"🎯 **Feature Visibility Matrix**\n\nControl which features are available to different user types:"

    def smart_pricing_message(self):
        """Get smart pricing message"""
        return f"💰 **Smart Pricing System**\n\nConfigure global or individual feature pricing:"

    def enhanced_statistics_message(self):
        """Get enhanced statistics message"""
        return f"📊 **Enhanced Statistics**\n\nView detailed analytics and user metrics:"

    def lea_expired_message(self):
        """Get LEA plan expired message"""
        return self.auth_service.get_setting(
            "lea_plan_expired_message",
            "⚠️ **Your LEA plan has expired**\n\nContact admin for renewal to continue using search services."
        )

    def search_result_success_message(self, credits_charged):
        """Get search result success message"""
        template = self.auth_service.get_setting("successful_search_message", "✅ Search Successful ({credits} credits deducted)")
        return template.format(credits=credits_charged)

    def search_result_no_results_message(self):
        """Get search result no results message"""
        return self.auth_service.get_setting("no_results_message", "⚠️ No results found (no credits deducted)")

    def search_result_rate_limited_message(self):
        """Get search result rate limited message"""
        return self.auth_service.get_setting("rate_limited_message", "⚡ Search blocked by rate limit (no credits deducted)")

    def search_result_error_message(self):
        """Get search result error message"""
        return self.auth_service.get_setting("search_error_message", "❌ Search error occurred (no credits deducted)")

    def lea_welcome_notification_message(self, lea_name, department, plan_duration, user_id):
        """Enhanced LEA welcome notification message with full details"""
        from datetime import datetime, timedelta

        # Calculate expiry date based on plan
        if plan_duration == "30D":
            expiry_date = datetime.now() + timedelta(days=30)
            plan_text = "30 Days"
        elif plan_duration == "6M":
            expiry_date = datetime.now() + timedelta(days=180)
            plan_text = "6 Months"
        elif plan_duration == "1Y":
            expiry_date = datetime.now() + timedelta(days=365)
            plan_text = "1 Year"
        else:
            expiry_date = datetime.now() + timedelta(days=30)
            plan_text = "30 Days"

        expiry_str = expiry_date.strftime("%d %B %Y")

        return (
            f"🎉 **Welcome {lea_name}!**\n\n"
            f"✅ You have been successfully promoted to LEA Officer status!\n\n"
            f"👮 **Officer Details:**\n"
            f"📛 Name: {lea_name}\n"
            f"🏛️ Department: {department}\n"
            f"💎 Credits: Unlimited\n"
            f"📅 Plan: {plan_text}\n"
            f"⏰ Expiry: {expiry_str}\n\n"
            f"🚀 **What's Next?**\n"
            f"Press /start or click /start to utilize this bot and access all your enhanced LEA features!\n\n"
            f"🔓 You now have unlimited search access and special LEA privileges!"
        )

    def vehicle_limit_reached_message(self):
        """Get vehicle limit reached message"""
        return self.auth_service.get_setting("vehicle_limit_message", "🚗 Vehicle search limit reached. Contact admin.")

    def pricing_display_message(self, user):
        """Get pricing display based on user type"""
        return self.auth_service.get_pricing_display(user)
