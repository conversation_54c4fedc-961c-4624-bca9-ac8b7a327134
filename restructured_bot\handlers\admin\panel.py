# handlers/admin/panel.py
import logging
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.base import <PERSON><PERSON>and<PERSON>
from config.constants import MENU, ADMIN_PANEL, ADMIN_ADD_USER, ADMIN_REMOVE_VEHICLE
from database.models import UserModel, SearchModel, VehicleModel, HiddenRecordsModel
from services.formatting import FormattingService
from services.validation import ValidationService
from utils.error_logger import log_callback_error, log_callback_interaction

logger = logging.getLogger(__name__)

class AdminPanelHandler(BaseHandler):
    """Handler for admin panel functionality"""
    
    def __init__(self):
        super().__init__()
        self.user_model = UserModel()
        self.search_model = SearchModel()
        self.vehicle_model = VehicleModel()
        self.hidden_model = HiddenRecordsModel()

    async def safe_edit_message(self, query, message, keyboard, parse_mode=ParseMode.MARKDOWN):
        """Safely edit message with error handling for 'Message is not modified' error"""
        try:
            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=parse_mode
            )
        except Exception as e:
            if "Message is not modified" in str(e) or "message is not modified" in str(e):
                # Message content is the same, just answer the callback
                await query.answer("Panel refreshed")
            else:
                # Re-raise other exceptions
                raise e

    async def safe_edit_message_simple(self, query, message, keyboard=None, parse_mode=ParseMode.MARKDOWN):
        """Safely edit message with simple parameters"""
        try:
            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode=parse_mode
            )
        except Exception as e:
            if "Message is not modified" in str(e) or "message is not modified" in str(e):
                await query.answer("Panel updated")
            else:
                raise e
    
    async def admin_callback_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle admin callback queries with comprehensive error logging"""
        query = update.callback_query
        data = query.data

        try:
            # Log the callback attempt
            log_callback_interaction(update, "ADMIN_CALLBACK_ATTEMPT", success=True,
                                   details=f"Callback: {data}")

            await query.answer()

            # CRITICAL FIX: Clear search states when entering admin panel
            # This prevents mobile search validation from interfering with admin inputs
            context.user_data.pop("current_search_field", None)
            context.user_data.pop("conversation_state", None)
            context.user_data["conversation_state"] = "ADMIN_PANEL"

            # Check admin authorization
            user = await self.check_admin_authorization(update, context)
            if not user:
                log_callback_interaction(update, "ADMIN_ACCESS_DENIED", success=False,
                                       details=f"User not authorized for: {data}")
                await query.answer("❌ Admin access required", show_alert=True)
                return MENU

        except Exception as e:
            log_callback_error(update, context, e, "admin_callback_handler_init")
            await query.answer("❌ Error processing admin request")
            return MENU

        # Main admin panel
        if data == "admin_panel":
            try:
                log_callback_interaction(update, "ADMIN_PANEL_ACCESS", success=True)
                return await self.show_admin_panel(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "show_admin_panel")
                await query.answer("❌ Error loading admin panel")
                return MENU

        # User management with error handling
        elif data == "admin_user_mgmt":
            try:
                log_callback_interaction(update, "ADMIN_USER_MGMT_ACCESS", success=True)
                return await self.show_user_management(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "show_user_management")
                await query.answer("❌ Error loading user management")
                return MENU

        elif data == "admin_add_user":
            try:
                return await self.admin_add_user_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_add_user_prompt")
                await query.answer("❌ Error loading add user form")
                return MENU

        elif data == "admin_view_users":
            try:
                return await self.show_all_users(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "show_all_users")
                await query.answer("❌ Error loading users list")
                return MENU

        elif data == "admin_add_credits":
            try:
                return await self.admin_add_credits_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_add_credits_prompt")
                await query.answer("❌ Error loading add credits form")
                return MENU

        elif data == "admin_modify_credits":
            try:
                return await self.admin_modify_credits_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_modify_credits_prompt")
                await query.answer("❌ Error loading modify credits form")
                return MENU

        elif data == "admin_suspend":
            try:
                return await self.admin_suspend_user_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_suspend_user_prompt")
                await query.answer("❌ Error loading suspend user form")
                return MENU

        elif data == "admin_unsuspend":
            try:
                return await self.admin_unsuspend_user_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_unsuspend_user_prompt")
                await query.answer("❌ Error loading unsuspend user form")
                return MENU

        elif data == "admin_remove_user":
            try:
                return await self.admin_remove_user_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_remove_user_prompt")
                await query.answer("❌ Error loading remove user form")
                return MENU

        elif data == "admin_convert_user":
            try:
                # Redirect to LEA wizard instead of problematic convert functionality
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.lea_add_wizard_start(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_convert_user_to_wizard")
                await query.answer("❌ Error loading LEA wizard")
                return MENU

        # Enhanced: LEA Management with error handling
        elif data == "admin_lea_plans":
            try:
                log_callback_interaction(update, "ADMIN_LEA_PLANS_ACCESS", success=True)
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.show_lea_plans_panel(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_lea_plans")
                await query.answer("❌ Error loading LEA plans")
                return MENU

        elif data == "admin_add_lea":
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.add_lea_officer_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_add_lea")
                await query.answer("❌ Error loading add LEA form")
                return MENU

        elif data == "admin_bulk_lea":
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.bulk_lea_assignment_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_bulk_lea")
                await query.answer("❌ Error loading bulk LEA form")
                return MENU

        elif data.startswith("plan_"):
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.handle_bulk_lea_plan_selection(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "lea_plan_selection")
                await query.answer("❌ Error processing plan selection")
                return MENU

        elif data.startswith("bulk_welcome_"):
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.execute_bulk_lea_assignment(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "bulk_lea_assignment")
                await query.answer("❌ Error executing bulk assignment")
                return MENU

        elif data == "lea_wizard_confirm":
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.lea_wizard_confirm(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "lea_wizard_confirm")
                await query.answer("❌ Error confirming LEA creation")
                return MENU

        # LEA management callbacks
        elif data in ["lea_add_quick", "lea_add_wizard"]:
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.handle_lea_callback(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"lea_callback_{data}")
                await query.answer("❌ Error processing LEA action")
                return MENU

        # LEA plan toggle callbacks
        elif data in ["toggle_lea_30d", "toggle_lea_6m", "toggle_lea_1y"]:
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.handle_lea_toggle(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"lea_toggle_{data}")
                await query.answer("❌ Error toggling LEA plan")
                return MENU

        # LEA cost setting callbacks
        elif data in ["set_lea_30d_cost", "set_lea_6m_cost", "set_lea_1y_cost"]:
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.handle_lea_cost_setting(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"lea_cost_{data}")
                await query.answer("❌ Error setting LEA cost")
                return MENU

        # Enhanced: Feature Management with error handling
        elif data == "admin_features":
            try:
                log_callback_interaction(update, "ADMIN_FEATURES_ACCESS", success=True)
                # CRITICAL: Set conversation state before routing to feature management
                context.user_data["conversation_state"] = "ADMIN_PANEL"
                from .feature_management import FeatureManagementHandler
                handler = FeatureManagementHandler()
                return await handler.show_admin_features(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_features")
                await query.answer("❌ Error loading feature settings")
                return ADMIN_PANEL

        elif data == "admin_feature_matrix":
            try:
                # CRITICAL: Set conversation state before routing to feature management
                context.user_data["conversation_state"] = "ADMIN_PANEL"
                from .feature_management import FeatureManagementHandler
                handler = FeatureManagementHandler()
                return await handler.show_feature_matrix(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_feature_matrix")
                await query.answer("❌ Error loading feature matrix")
                return ADMIN_PANEL

        elif data == "admin_global_features":
            try:
                # CRITICAL: Set conversation state before routing to feature management
                context.user_data["conversation_state"] = "ADMIN_PANEL"
                from .feature_management import FeatureManagementHandler
                handler = FeatureManagementHandler()
                return await handler.toggle_global_features(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_global_features")
                await query.answer("❌ Error loading global features")
                return ADMIN_PANEL

        elif data == "admin_lea_features":
            try:
                # CRITICAL: Set conversation state before routing to feature management
                context.user_data["conversation_state"] = "ADMIN_PANEL"
                from .feature_management import FeatureManagementHandler
                handler = FeatureManagementHandler()
                return await handler.toggle_lea_features(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_lea_features")
                await query.answer("❌ Error loading LEA features")
                return ADMIN_PANEL

        elif data == "admin_normal_features":
            try:
                # CRITICAL: Set conversation state before routing to feature management
                context.user_data["conversation_state"] = "ADMIN_PANEL"
                from .feature_management import FeatureManagementHandler
                handler = FeatureManagementHandler()
                return await handler.toggle_normal_features(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_normal_features")
                await query.answer("❌ Error loading normal features")
                return ADMIN_PANEL

        elif data.startswith("toggle_"):
            try:
                from .feature_management import FeatureManagementHandler
                handler = FeatureManagementHandler()
                return await handler.handle_feature_toggle(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "feature_toggle")
                await query.answer("❌ Error toggling feature")
                return MENU

        # Enhanced: Pricing Management with error handling
        elif data == "admin_pricing":
            try:
                log_callback_interaction(update, "ADMIN_PRICING_ACCESS", success=True)
                from .pricing_management import PricingManagementHandler
                handler = PricingManagementHandler()
                return await handler.show_pricing_panel(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_pricing")
                await query.answer("❌ Error loading pricing settings")
                return MENU

        elif data == "admin_toggle_global_pricing":
            try:
                from .pricing_management import PricingManagementHandler
                handler = PricingManagementHandler()
                return await handler.toggle_global_pricing(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_toggle_global_pricing")
                await query.answer("❌ Error toggling global pricing")
                return MENU

        elif data == "admin_set_costs":
            try:
                from .pricing_management import PricingManagementHandler
                handler = PricingManagementHandler()
                return await handler.set_pricing_costs(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_set_costs")
                await query.answer("❌ Error loading cost settings")
                return MENU



        elif data == "admin_lea_costs":
            try:
                from .pricing_management import PricingManagementHandler
                handler = PricingManagementHandler()
                return await handler.set_lea_costs(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_lea_costs")
                await query.answer("❌ Error loading LEA costs")
                return MENU

        elif data == "admin_pricing_preview":
            try:
                from .pricing_management import PricingManagementHandler
                handler = PricingManagementHandler()
                return await handler.show_pricing_preview(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_pricing_preview")
                await query.answer("❌ Error loading pricing preview")
                return MENU

        # Settings with error handling
        elif data == "admin_messages":
            try:
                log_callback_interaction(update, "ADMIN_MESSAGES_ACCESS", success=True)
                return await self.admin_message_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_message_settings")
                await query.answer("❌ Error loading message settings")
                return MENU

        elif data == "admin_notifications":
            try:
                log_callback_interaction(update, "ADMIN_NOTIFICATIONS_ACCESS", success=True)
                return await self.admin_notification_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_notification_settings")
                await query.answer("❌ Error loading notification settings")
                return MENU

        elif data == "admin_logs_settings":
            try:
                log_callback_interaction(update, "ADMIN_LOGS_ACCESS", success=True)
                return await self.admin_logs_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_logs_settings")
                await query.answer("❌ Error loading logs settings")
                return MENU

        elif data == "admin_all_logs":
            try:
                return await self.admin_view_all_logs(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_view_all_logs")
                await query.answer("❌ Error loading logs")
                return MENU

        # Broadcast with error handling
        elif data == "admin_broadcast":
            try:
                log_callback_interaction(update, "ADMIN_BROADCAST_ACCESS", success=True)
                return await self.admin_broadcast_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_broadcast_settings")
                await query.answer("❌ Error loading broadcast settings")
                return MENU

        elif data == "admin_send_broadcast":
            try:
                return await self.admin_broadcast_prompt(update, context, "broadcast")
            except Exception as e:
                log_callback_error(update, context, e, "admin_broadcast_prompt")
                await query.answer("❌ Error loading broadcast form")
                return MENU

        elif data == "admin_broadcast_active":
            try:
                return await self.admin_broadcast_prompt(update, context, "active")
            except Exception as e:
                log_callback_error(update, context, e, "admin_broadcast_active")
                await query.answer("❌ Error loading active broadcast")
                return MENU

        elif data == "admin_broadcast_premium":
            try:
                return await self.admin_broadcast_prompt(update, context, "premium")
            except Exception as e:
                log_callback_error(update, context, e, "admin_broadcast_premium")
                await query.answer("❌ Error loading premium broadcast")
                return MENU

        elif data == "admin_broadcast_trial":
            try:
                return await self.admin_broadcast_prompt(update, context, "trial")
            except Exception as e:
                log_callback_error(update, context, e, "admin_broadcast_trial")
                await query.answer("❌ Error loading trial broadcast")
                return MENU

        # Rate limiting and access control with error handling
        elif data == "admin_rate_limiting":
            try:
                log_callback_interaction(update, "ADMIN_RATE_LIMITING_ACCESS", success=True)
                return await self.admin_rate_limiting_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_rate_limiting_settings")
                await query.answer("❌ Error loading rate limiting settings")
                return MENU

        elif data == "admin_access_control":
            try:
                return await self.admin_access_control_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_access_control_settings")
                await query.answer("❌ Error loading access control")
                return MENU

        # Export with error handling
        elif data == "admin_export":
            try:
                log_callback_interaction(update, "ADMIN_EXPORT_ACCESS", success=True)
                return await self.admin_export_menu(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_export_menu")
                await query.answer("❌ Error loading export menu")
                return MENU

        elif data == "admin_export_users":
            try:
                return await self.admin_export_users(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_export_users")
                await query.answer("❌ Error exporting users")
                return MENU

        elif data == "admin_export_logs":
            try:
                return await self.admin_export_logs(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_export_logs")
                await query.answer("❌ Error exporting logs")
                return MENU

        elif data == "admin_export_vehicles":
            try:
                return await self.admin_export_vehicles(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "admin_export_vehicles")
                await query.answer("❌ Error exporting vehicles")
                return MENU

        # Statistics with error handling
        elif data == "admin_stats":
            try:
                log_callback_interaction(update, "ADMIN_STATS_ACCESS", success=True)
                return await self.show_statistics(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "show_statistics")
                await query.answer("❌ Error loading statistics")
                return MENU

        # Hidden records with error handling
        elif data == "admin_hidden_records":
            try:
                log_callback_interaction(update, "ADMIN_HIDDEN_RECORDS_ACCESS", success=True)
                return await self.show_hidden_records_menu(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "show_hidden_records_menu")
                await query.answer("❌ Error loading hidden records")
                return MENU
        elif data == "admin_view_hidden":
            return await self.show_hidden_records(update, context)
        elif data == "admin_hide_record":
            return await self.admin_hide_record_prompt(update, context)
        elif data == "admin_unhide_record":
            return await self.admin_unhide_record_prompt(update, context)

        # Vehicle data
        elif data == "admin_vehicle_data":
            return await self.show_vehicle_data_menu(update, context)
        elif data == "admin_vehicle_stats":
            return await self.show_vehicle_statistics(update, context)
        elif data == "admin_remove_vehicle":
            return await self.admin_remove_vehicle_prompt(update, context)

        # Search settings
        elif data == "admin_search_settings":
            return await self.show_search_settings(update, context)

        # Feature management - ALL callbacks
        elif data in [
            # Bulk operations from feature management pages
            "enable_all_global", "disable_all_global",
            "enable_all_lea", "disable_all_lea",
            "enable_all_normal", "disable_all_normal",
            # Bulk operations from main feature menu
            "admin_enable_all_lea", "admin_disable_all_normal",
            "admin_reset_features", "admin_individual_features",
            # IMPROVEMENT 2: Add new toggle callbacks
            "admin_toggle_all_lea", "admin_toggle_all_normal",
            "toggle_all_lea", "toggle_all_normal", "toggle_all_global"
        ]:
            try:
                # CRITICAL: Ensure conversation state is set for feature management
                context.user_data["conversation_state"] = "ADMIN_PANEL"
                from .feature_management import FeatureManagementHandler
                handler = FeatureManagementHandler()
                return await handler.handle_feature_callback(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"feature_bulk_{data}")
                await query.answer("❌ Error processing feature operation")
                return ADMIN_PANEL

        # Individual feature toggles (search features only)
        elif data.startswith("toggle_") and any(x in data for x in ["mobile", "aadhar", "alternate", "vehicle", "global", "lea", "normal"]):
            try:
                # CRITICAL: Ensure conversation state is set for feature management
                context.user_data["conversation_state"] = "ADMIN_PANEL"
                from .feature_management import FeatureManagementHandler
                handler = FeatureManagementHandler()
                return await handler.handle_feature_callback(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"feature_toggle_{data}")
                await query.answer("❌ Error toggling feature")
                return ADMIN_PANEL

        # Admin system toggles (rate limiting, open access, etc.)
        elif data.startswith("admin_toggle_") and not any(x in data for x in ["all_lea", "all_normal"]):
            try:
                return await self.handle_feature_toggle(update, context, data)
            except Exception as e:
                log_callback_error(update, context, e, f"admin_toggle_{data}")
                await query.answer("❌ Error toggling setting")
                return MENU

        elif data.startswith("toggle_lea_"):
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.handle_lea_toggle(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"toggle_lea_{data}")
                await query.answer("❌ Error toggling LEA plan")
                return MENU

        elif data.startswith("set_lea_") and "cost" in data:
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.handle_lea_cost_setting(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"set_lea_cost_{data}")
                await query.answer("❌ Error setting LEA cost")
                return MENU

        # Pagination callbacks
        elif data.startswith("admin_users_page:"):
            page = self.pagination_service.get_page_from_callback(data)
            return await self.show_all_users(update, context, page)
        elif data.startswith("admin_logs_page:"):
            page = self.pagination_service.get_page_from_callback(data)
            return await self.admin_view_all_logs(update, context, page)
        elif data.startswith("admin_hidden_page:"):
            page = self.pagination_service.get_page_from_callback(data)
            return await self.show_hidden_records(update, context, page)

        # New callback handlers for enhanced functionality
        elif data.startswith("msg_"):
            # Message settings callbacks
            try:
                from .settings import AdminSettingsHandler
                handler = AdminSettingsHandler()
                return await handler.handle_message_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "msg_callback")
                await query.answer("❌ Error loading message settings")
                return MENU

        # Specific message type callbacks
        elif data in ["msg_welcome", "msg_search", "msg_pricing", "msg_error", "msg_success", "msg_notification", "msg_system", "msg_reset_all", "msg_confirm_reset"]:
            try:
                from .settings import AdminSettingsHandler
                handler = AdminSettingsHandler()
                return await handler.handle_message_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"msg_specific_{data}")
                await query.answer("❌ Error loading message settings")
                return MENU

        elif data.startswith("notif_"):
            # Notification settings callbacks
            try:
                from .settings import AdminSettingsHandler
                handler = AdminSettingsHandler()
                return await handler.handle_notification_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "notif_callback")
                await query.answer("❌ Error loading notification settings")
                return MENU

        # Specific notification callbacks
        elif data in ["notif_welcome", "notif_credits", "notif_expiry", "notif_usage", "notif_lea", "notif_users", "notif_system", "notif_customize"]:
            try:
                from .settings import AdminSettingsHandler
                handler = AdminSettingsHandler()
                return await handler.handle_notification_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"notif_specific_{data}")
                await query.answer("❌ Error loading notification settings")
                return MENU

        elif data.startswith("rate_"):
            # Rate limiting callbacks
            try:
                from .settings import AdminSettingsHandler
                handler = AdminSettingsHandler()
                return await handler.handle_rate_limiting_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "rate_callback")
                await query.answer("❌ Error loading rate limiting settings")
                return MENU

        # Specific rate limiting callbacks
        elif data in ["rate_toggle_main", "rate_daily_limits", "rate_search_limits", "rate_user_limits", "rate_cooldown", "rate_lea_exempt", "rate_stats", "rate_reset_all"]:
            try:
                from .settings import AdminSettingsHandler
                handler = AdminSettingsHandler()
                return await handler.handle_rate_limiting_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"rate_specific_{data}")
                await query.answer("❌ Error loading rate limiting settings")
                return MENU

        elif data.startswith("access_"):
            # Access control callbacks
            try:
                from .settings import AdminSettingsHandler
                handler = AdminSettingsHandler()
                return await handler.handle_access_control_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "access_callback")
                await query.answer("❌ Error loading access control settings")
                return MENU

        # Specific access control callbacks
        elif data in ["access_trial", "access_user_types", "access_lea", "access_premium", "access_auto_convert", "access_stats", "access_banned", "access_defaults", "toggle_trial_system", "set_trial_days", "set_trial_credits", "toggle_auto_convert"]:
            try:
                from .settings import AdminSettingsHandler
                handler = AdminSettingsHandler()
                return await handler.handle_access_control_settings(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"access_specific_{data}")
                await query.answer("❌ Error loading access control settings")
                return MENU

        elif data.startswith("broadcast_"):
            # Broadcast callbacks
            try:
                from .broadcast import AdminBroadcastHandler
                handler = AdminBroadcastHandler()
                return await handler.handle_broadcast_callback(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "broadcast_callback")
                await query.answer("❌ Error loading broadcast settings")
                return MENU

        # Specific broadcast callbacks
        elif data in ["broadcast_all", "broadcast_lea", "broadcast_expired", "broadcast_expiring", "broadcast_active", "broadcast_premium", "broadcast_trial", "broadcast_schedule"]:
            try:
                from .broadcast import AdminBroadcastHandler
                handler = AdminBroadcastHandler()
                return await handler.handle_broadcast_callback(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"broadcast_specific_{data}")
                await query.answer("❌ Error loading broadcast settings")
                return MENU

        elif data.startswith("lea_"):
            # LEA management callbacks
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                return await handler.handle_lea_callback(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "lea_callback")
                await query.answer("❌ Error loading LEA settings")
                return MENU

        # Specific LEA plan callbacks
        elif data.startswith("admin_lea_"):
            try:
                from .lea_management import LEAManagementHandler
                handler = LEAManagementHandler()
                if data == "admin_lea_30d":
                    return await handler.manage_lea_30d_plan(update, context)
                elif data == "admin_lea_6m":
                    return await handler.manage_lea_6m_plan(update, context)
                elif data == "admin_lea_1y":
                    return await handler.manage_lea_1y_plan(update, context)
                elif data == "admin_lea_settings":
                    return await handler.show_lea_settings(update, context)
                elif data == "admin_lea_stats":
                    return await handler.show_lea_statistics(update, context)
                elif data == "admin_lea_expiring":
                    return await handler.show_expiring_lea_officers(update, context)
                else:
                    return await handler.handle_lea_callback(update, context)
            except Exception as e:
                log_callback_error(update, context, e, f"admin_lea_callback_{data}")
                await query.answer("❌ Error loading LEA plan settings")
                return MENU

        # Pricing management callbacks
        elif data.startswith("set_") and "cost" in data:
            try:
                from .pricing_management import PricingManagementHandler
                handler = PricingManagementHandler()
                if data == "set_mobile_cost":
                    return await handler.set_mobile_cost_prompt(update, context)
                elif data == "set_aadhar_cost":
                    return await handler.set_aadhar_cost_prompt(update, context)
                elif data == "set_alt_cost":
                    return await handler.set_alternate_cost_prompt(update, context)
                elif data == "set_vehicle_cost":
                    return await handler.set_vehicle_cost_prompt(update, context)
                elif data == "set_global_cost":
                    return await handler.set_global_cost_prompt(update, context)
                elif "lea" in data:
                    # Handle LEA cost callbacks (set_lea_30d_cost, set_lea_6m_cost, set_lea_1y_cost)
                    return await handler.set_lea_cost_prompt(update, context)
                else:
                    return await handler.set_individual_cost_prompt(update, context)
            except Exception as e:
                log_callback_error(update, context, e, "set_cost_callback")
                await query.answer("❌ Error loading cost settings")
                return MENU

        # Default fallback
        else:
            return await self.show_admin_panel(update, context)
    
    async def show_admin_panel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main admin panel"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU
        
        message = self.message_templates.admin_panel_message()
        keyboard = self.keyboard_builder.admin_panel_keyboard()

        await self.safe_edit_message(update.callback_query, message, keyboard)
        
        self.log_admin_action(user["user_id"], "viewed_admin_panel")
        return ADMIN_PANEL
    
    async def show_user_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user management menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU
        
        message = self.message_templates.user_management_message()
        keyboard = self.keyboard_builder.admin_user_management_keyboard()

        await self.safe_edit_message(update.callback_query, message, keyboard)
        
        self.log_admin_action(user["user_id"], "viewed_user_management")
        return ADMIN_PANEL
    
    async def show_all_users(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page=0):
        """Show all users with pagination"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU
        
        all_users = self.user_model.get_all_users()
        message, total_pages = self.pagination_service.format_user_list(all_users, page)
        
        keyboard = self.pagination_service.create_admin_list_keyboard(
            page, total_pages, "admin_users", "admin_user_mgmt"
        )
        
        await self.safe_edit_message(update.callback_query, message, keyboard)
        
        self.log_admin_action(user["user_id"], "viewed_all_users", f"page_{page}")
        return ADMIN_PANEL
    
    async def show_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show fast-loading essential statistics"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU

        try:
            # Fast essential metrics only - no heavy queries
            from datetime import datetime, timedelta
            import time

            start_time = time.time()

            # Get basic counts (fast queries)
            total_users = self.user_model.get_total_user_count()  # Simple count
            today = datetime.now().strftime("%Y-%m-%d")

            # Get today's search count (limited query)
            today_searches = self.search_model.get_today_search_count() if hasattr(self.search_model, 'get_today_search_count') else 0

            # Get basic system info
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Calculate load time
            load_time = round((time.time() - start_time) * 1000, 1)  # in milliseconds

            message = (
                f"📊 **System Statistics** ⚡\n\n"
                f"👥 **Users:**\n"
                f"├─ Total Users: {total_users:,}\n"
                f"└─ System Status: 🟢 Online\n\n"
                f"🔍 **Today's Activity:**\n"
                f"├─ Searches Today: {today_searches:,}\n"
                f"└─ Date: {today}\n\n"
                f"⚡ **Performance:**\n"
                f"├─ Load Time: {load_time}ms\n"
                f"├─ Last Updated: {current_time}\n"
                f"└─ Status: 🚀 Fast Loading\n\n"
                f"💡 *Optimized for speed - showing essential metrics only*"
            )

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("🔄 Refresh Stats", callback_data="admin_stats"),
                    InlineKeyboardButton("📈 Detailed Analytics", callback_data="admin_detailed_stats")
                ],
                [InlineKeyboardButton("🔙 Back to Admin Panel", callback_data="admin_panel")]
            ])

            await self.safe_edit_message(update.callback_query, message, keyboard)

            self.log_admin_action(user["user_id"], "viewed_fast_statistics")
            return ADMIN_PANEL

        except Exception as e:
            log_callback_error(update, context, e, "show_statistics")

            # Fallback minimal stats
            message = (
                f"📊 **System Statistics** ⚡\n\n"
                f"🟢 **System Status: Online**\n"
                f"📅 **Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}**\n\n"
                f"⚠️ *Some statistics temporarily unavailable*\n"
                f"💡 *System is running normally*"
            )

            keyboard = self.keyboard_builder.admin_back_keyboard()
            await self.safe_edit_message(update.callback_query, message, keyboard)
            return ADMIN_PANEL
    
    async def show_hidden_records_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show hidden records management menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU
        
        count = self.hidden_model.get_hidden_records_count()
        message = self.message_templates.hidden_records_message(count)
        keyboard = self.keyboard_builder.admin_hidden_records_keyboard()
        
        await self.safe_edit_message(update.callback_query, message, keyboard)
        
        self.log_admin_action(user["user_id"], "viewed_hidden_records_menu")
        return ADMIN_PANEL
    
    async def show_hidden_records(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page=0):
        """Show hidden records with pagination"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU
        
        hidden_records = self.hidden_model.get_hidden_records(50)
        
        if not hidden_records:
            message = "📋 **Hidden Records**\n\nNo hidden records found."
        else:
            lines = [f"📋 **Hidden Records (Page {page + 1})**\n"]
            
            for idx, record in enumerate(hidden_records, 1):
                field = record.get('field', 'unknown').upper()
                value = record.get('value', 'N/A')
                reason = record.get('reason', 'No reason')
                hide_type = record.get('hide_type', 'unknown')
                
                lines.append(f"{idx}. **{field}:** {value}")
                if reason and reason != 'No reason':
                    lines.append(f"   📝 {reason}")
                if hide_type == 'associated':
                    lines.append(f"   🔗 Associated record")
                lines.append("")
            
            message = "\n".join(lines)
        
        keyboard = self.keyboard_builder.admin_back_keyboard("admin_hidden_records")
        
        await self.safe_edit_message(update.callback_query, message, keyboard)
        
        self.log_admin_action(user["user_id"], "viewed_hidden_records", f"page_{page}")
        return ADMIN_PANEL
    
    async def show_vehicle_data_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show vehicle data management menu"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU
        
        message = self.message_templates.vehicle_data_message()
        keyboard = self.keyboard_builder.admin_vehicle_data_keyboard()
        
        await self.safe_edit_message(update.callback_query, message, keyboard)
        
        self.log_admin_action(user["user_id"], "viewed_vehicle_data_menu")
        return ADMIN_PANEL
    
    async def show_vehicle_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show vehicle database statistics"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU
        
        stats = self.vehicle_model.get_vehicle_database_stats()
        message = FormattingService.format_vehicle_stats(stats) if stats else "Vehicle statistics not available"
        keyboard = self.keyboard_builder.admin_back_keyboard("admin_vehicle_data")
        
        await self.safe_edit_message(update.callback_query, message, keyboard)
        
        self.log_admin_action(user["user_id"], "viewed_vehicle_statistics")
        return ADMIN_PANEL
    
    async def show_search_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show search settings"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU
        
        message = self.message_templates.search_settings_message()
        keyboard = self.keyboard_builder.admin_search_settings_keyboard()
        
        await self.safe_edit_message(update.callback_query, message, keyboard)
        
        self.log_admin_action(user["user_id"], "viewed_search_settings")
        return ADMIN_PANEL
    
    async def handle_feature_toggle(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data):
        """Handle feature toggle callbacks"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU

        feature_map = {
            "admin_toggle_mobile_search": "mobile_search_enabled",
            "admin_toggle_aadhar_search": "aadhar_search_enabled",
            "admin_toggle_alternate_search": "alternate_search_enabled",
            "admin_toggle_vehicle_search": "vehicle_search_enabled",
            "admin_toggle_rate_limiting": "rate_limiting_enabled",
            "admin_toggle_open_access": "open_access_enabled",
            "admin_toggle_notifications": "notifications_enabled",
            "admin_toggle_credit_notifications": "credit_recharge_notifications"
        }

        setting_key = feature_map.get(callback_data)
        if setting_key:
            current_value = self.auth_service.get_setting(setting_key, True)
            new_value = not current_value
            self.auth_service.set_setting(setting_key, new_value)

            feature_name = setting_key.replace("_enabled", "").replace("_", " ").title()
            status = "enabled" if new_value else "disabled"

            await update.callback_query.answer(f"{feature_name} {status}")
            self.log_admin_action(user["user_id"], "toggled_feature", f"{feature_name} -> {status}")

            # Return to appropriate settings page
            if "search" in callback_data:
                return await self.show_search_settings(update, context)
            elif "rate_limiting" in callback_data:
                return await self.admin_rate_limiting_settings(update, context)
            elif "access" in callback_data:
                return await self.admin_access_control_settings(update, context)
            elif "notification" in callback_data:
                return await self.admin_notification_settings(update, context)

        return await self.show_admin_panel(update, context)
    
    # Command handlers (placeholder implementations)
    async def adduser_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /adduser command"""
        await update.message.reply_text("Use the admin panel for user management.")
    
    async def addcredits_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /addcredits command"""
        await update.message.reply_text("Use the admin panel for credit management.")
    
    async def setcredits_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /setcredits command"""
        await update.message.reply_text("Use the admin panel for credit management.")
    
    async def suspenduser_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /suspenduser command"""
        await update.message.reply_text("Use the admin panel for user management.")
    
    async def unsuspenduser_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /unsuspenduser command"""
        await update.message.reply_text("Use the admin panel for user management.")
    
    async def removeuser_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /removeuser command"""
        await update.message.reply_text("Use the admin panel for user management.")
    
    async def setprice_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /setprice command"""
        await update.message.reply_text("Use the admin panel for pricing settings.")
    
    async def hiderecord_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /hiderecord command"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return
        
        if len(context.args) < 2:
            await update.message.reply_text(
                "**Smart Hide Person Usage:**\n"
                "/hiderecord <field> <value> [reason]\n\n"
                "**Fields:** mobile, id, alt\n"
                "**Example:** /hiderecord mobile 9876543210 Privacy request",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        field = context.args[0].lower()
        value = context.args[1]
        reason = " ".join(context.args[2:]) if len(context.args) > 2 else ""
        
        success, message = self.hidden_model.hide_record(field, value, user["user_id"], reason)
        
        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN
        )
        
        if success:
            self.log_admin_action(user["user_id"], "hide_record", f"{field}:{value}")
    
    async def unhiderecord_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /unhiderecord command"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return
        
        if len(context.args) < 2:
            await update.message.reply_text(
                "**Smart Unhide Person Usage:**\n"
                "/unhiderecord <field> <value>\n\n"
                "**Fields:** mobile, id, alt\n"
                "**Example:** /unhiderecord mobile 9876543210",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        field = context.args[0].lower()
        value = context.args[1]
        
        success, message = self.hidden_model.unhide_record(field, value)
        
        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN
        )
        
        if success:
            self.log_admin_action(user["user_id"], "unhide_record", f"{field}:{value}")
    
    async def listhidden_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /listhidden command"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return
        
        hidden_records = self.hidden_model.get_hidden_records(10)
        
        if not hidden_records:
            await update.message.reply_text("No hidden records found.")
            return
        
        lines = ["📋 **Hidden Records (Last 10):**\n"]
        
        for idx, record in enumerate(hidden_records, 1):
            field = record.get('field', 'unknown').upper()
            value = record.get('value', 'N/A')
            reason = record.get('reason', 'No reason')
            
            lines.append(f"{idx}. **{field}:** {value}")
            if reason and reason != 'No reason':
                lines.append(f"   📝 {reason}")
            lines.append("")
        
        message = "\n".join(lines)
        
        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN
        )
        
        self.log_admin_action(user["user_id"], "list_hidden_records")

    # Add all the missing admin functions
    async def admin_add_user_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for adding new user"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "add_user"

        message = ("➕ **Add New User**\n\n"
                  "Please send user details in this format:\n"
                  "`user_id credits expiry_days`\n\n"
                  "Examples:\n"
                  "`123456789 100 30`\n\n"
                  "Expiry days: 1, 2, 7, 15, 30, or 'lifetime'")
        keyboard = self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        await self.safe_edit_message(query, message, keyboard)
        return ADMIN_ADD_USER

    async def admin_add_credits_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for adding credits"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "add_credits"

        await self.safe_edit_message_simple(
            query,
            "💳 **Add Credits**\n\n"
            "Please send in this format:\n"
            "`user_id credits expiry_days`\n\n"
            "Examples:\n"
            "`123456789 50 30`\n\n"
            "Expiry days: 1, 2, 7, 15, 30, or 'lifetime'",
            self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )
        return ADMIN_ADD_USER

    async def admin_modify_credits_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for modifying credits"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "modify_credits"

        await self.safe_edit_message_simple(
            query,
            "✏️ **Modify Credits**\n\n"
            "Please send in this format:\n"
            "`user_id new_credits`\n\n"
            "Example:\n"
            "`123456789 100`",
            self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )
        return ADMIN_ADD_USER

    async def admin_suspend_user_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for suspending user"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "suspend_user"

        await self.safe_edit_message_simple(
            query,
            "🚫 **Suspend User**\n\n"
            "Please send in this format:\n"
            "`user_id [days]`\n\n"
            "Examples:\n"
            "`123456789` (indefinite)\n"
            "`123456789 7` (7 days)",
            self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )
        return ADMIN_ADD_USER

    async def admin_unsuspend_user_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for unsuspending user"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "unsuspend_user"

        await self.safe_edit_message_simple(
            query,
            "✅ **Unsuspend User**\n\n"
            "Please send the user ID:\n"
            "`123456789`",
            self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )
        return ADMIN_ADD_USER

    async def admin_remove_user_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for removing user"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "remove_user"

        await self.safe_edit_message_simple(
            query,
            "❌ **Remove User**\n\n"
            "⚠️ **Warning**: This action cannot be undone!\n\n"
            "Please send the user ID:\n"
            "`123456789`",
            self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )
        return ADMIN_ADD_USER

    # REMOVED: admin_convert_user_prompt - Now redirects to LEA wizard

    async def admin_hide_record_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for hiding record"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "hide_record"

        await self.safe_edit_message_simple(
            query,
            self.message_templates.hide_record_prompt_message(),
            self.keyboard_builder.admin_back_keyboard("admin_hidden_records")
        )
        return ADMIN_ADD_USER

    async def admin_unhide_record_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for unhiding record"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "unhide_record"

        await self.safe_edit_message_simple(
            query,
            self.message_templates.unhide_record_prompt_message(),
            self.keyboard_builder.admin_back_keyboard("admin_hidden_records")
        )
        return ADMIN_ADD_USER

    async def admin_remove_vehicle_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for removing vehicle"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "remove_vehicle"

        await self.safe_edit_message_simple(
            query,
            self.message_templates.remove_vehicle_prompt_message(),
            self.keyboard_builder.admin_back_keyboard("admin_vehicle_data")
        )
        return ADMIN_REMOVE_VEHICLE

    # Import and delegate to specialized handlers
    async def admin_pricing_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to settings handler"""
        from .settings import AdminSettingsHandler
        handler = AdminSettingsHandler()
        return await handler.admin_pricing_settings(update, context)

    async def admin_message_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to settings handler"""
        from .settings import AdminSettingsHandler
        handler = AdminSettingsHandler()
        return await handler.admin_message_settings(update, context)

    async def admin_notification_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to settings handler"""
        from .settings import AdminSettingsHandler
        handler = AdminSettingsHandler()
        return await handler.admin_notification_settings(update, context)

    async def admin_logs_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to settings handler"""
        from .settings import AdminSettingsHandler
        handler = AdminSettingsHandler()
        return await handler.admin_logs_settings(update, context)

    async def admin_view_all_logs(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page=0):
        """Delegate to settings handler"""
        from .settings import AdminSettingsHandler
        handler = AdminSettingsHandler()
        return await handler.admin_view_all_logs(update, context, page)

    async def admin_rate_limiting_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to settings handler"""
        from .settings import AdminSettingsHandler
        handler = AdminSettingsHandler()
        return await handler.admin_rate_limiting_settings(update, context)

    async def admin_access_control_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to settings handler"""
        from .settings import AdminSettingsHandler
        handler = AdminSettingsHandler()
        return await handler.admin_access_control_settings(update, context)

    async def admin_broadcast_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to broadcast handler"""
        from .broadcast import AdminBroadcastHandler
        handler = AdminBroadcastHandler()
        return await handler.admin_broadcast_settings(update, context)

    async def admin_broadcast_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE, broadcast_type: str):
        """Delegate to broadcast handler"""
        from .broadcast import AdminBroadcastHandler
        handler = AdminBroadcastHandler()
        return await handler.admin_broadcast_prompt(update, context, broadcast_type)

    async def admin_export_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to broadcast handler"""
        from .broadcast import AdminBroadcastHandler
        handler = AdminBroadcastHandler()
        return await handler.admin_export_menu(update, context)

    async def admin_export_users(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to broadcast handler"""
        from .broadcast import AdminBroadcastHandler
        handler = AdminBroadcastHandler()
        return await handler.admin_export_users(update, context)

    async def admin_export_logs(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to broadcast handler"""
        from .broadcast import AdminBroadcastHandler
        handler = AdminBroadcastHandler()
        return await handler.admin_export_logs(update, context)

    async def admin_export_vehicles(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Delegate to broadcast handler"""
        from .broadcast import AdminBroadcastHandler
        handler = AdminBroadcastHandler()
        return await handler.admin_export_vehicles(update, context)
    
    async def handle_admin_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Enhanced admin input handler for all operations"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return MENU

        text = update.message.text.strip()
        current_state = context.user_data.get("admin_state")

        if not current_state:
            await update.message.reply_text("❌ Invalid operation")
            return MENU

        # Enhanced: Route LEA management inputs
        if current_state in ["add_lea_officer", "bulk_lea_step1", "lea_wizard_step1", "lea_wizard_step2", "lea_wizard_step3", "lea_wizard_step4", "lea_wizard_step5", "lea_wizard_step6", "set_lea_30d_cost", "set_lea_6m_cost", "set_lea_1y_cost"]:
            from .lea_management import LEAManagementHandler
            handler = LEAManagementHandler()
            return await handler.handle_lea_input(update, context)

        # Enhanced: Route pricing management inputs
        elif current_state.startswith("set_") and "cost" in current_state:
            from .pricing_management import PricingManagementHandler
            handler = PricingManagementHandler()
            return await handler.handle_pricing_input(update, context)

        try:
            if current_state == "add_user":
                parts = text.split()
                if len(parts) < 3:
                    await update.message.reply_text("❌ Invalid format. Use: `user_id credits expiry_days`", parse_mode=ParseMode.MARKDOWN)
                    return ADMIN_ADD_USER

                user_id, credits, expiry_input = int(parts[0]), int(parts[1]), parts[2]

                if expiry_input.lower() == "lifetime":
                    expiry_days = 365 * 100
                else:
                    expiry_days = int(expiry_input)

                success, message = self.user_model.add_user(user_id, f"user_{user_id}", credits, expiry_days)

                if success:
                    # Send notification to the newly added user
                    try:
                        from datetime import datetime, timedelta
                        expiry_date = datetime.now() + timedelta(days=expiry_days)
                        expiry_str = "Never expires" if expiry_days >= 36500 else expiry_date.strftime("%Y-%m-%d")

                        user_notification = (
                            f"🎉 Welcome to Law Enforcement Bot!\n\n"
                            f"✅ Your account has been activated successfully!\n\n"
                            f"📋 Account Details:\n"
                            f"💎 Credits: {credits}\n"
                            f"📅 Plan: {'Lifetime' if expiry_days >= 36500 else 'Premium'}\n"
                            f"⏰ Valid until: {expiry_str}\n\n"
                            f"🚀 You can now use /start to access all features!"
                        )

                        await context.bot.send_message(
                            chat_id=user_id,
                            text=user_notification
                            # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                        )

                        # Show admin success message with notification status
                        await update.message.reply_text(
                            f"✅ {message}\n\n"
                            f"📤 User notification sent successfully!"
                            # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                        )
                    except Exception as e:
                        # Show admin success message with notification failure
                        await update.message.reply_text(
                            f"✅ {message}\n\n"
                            f"⚠️ Could not send notification to user (user may need to start the bot first)"
                            # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                        )
                else:
                    await update.message.reply_text(
                        f"❌ {message}"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )

                # IMPROVEMENT 5: Show input prompt again for easy re-use
                await self.show_add_user_prompt_again(update, context)

            # REMOVED: convert_user handling - Now redirects to LEA wizard

            elif current_state == "add_credits":
                parts = text.split()
                if len(parts) < 2:
                    await update.message.reply_text("❌ Invalid format. Use: `user_id credits [expiry_days]`", parse_mode=ParseMode.MARKDOWN)
                    return ADMIN_ADD_USER

                user_id, credits = int(parts[0]), int(parts[1])
                expiry_days = int(parts[2]) if len(parts) > 2 else None

                success = self.user_model.add_credits(user_id, credits, expiry_days)
                if success:
                    # Send notification to user about added credits
                    try:
                        user = self.user_model.get_user(user_id)
                        total_credits = user.get("credits", 0)

                        credit_notification = (
                            f"💎 Credits Added!\n\n"
                            f"✅ Your account has been credited successfully!\n\n"
                            f"📋 Credit Details:\n"
                            f"➕ Added: {credits} credits\n"
                            f"💎 Total Credits: {total_credits}\n"
                        )

                        if expiry_days:
                            from datetime import datetime, timedelta
                            expiry_date = datetime.now() + timedelta(days=expiry_days)
                            credit_notification += f"⏰ Extended until: {expiry_date.strftime('%Y-%m-%d')}\n"

                        credit_notification += f"\n🚀 Use /start to access your features!"

                        await context.bot.send_message(
                            chat_id=user_id,
                            text=credit_notification
                            # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                        )

                        await update.message.reply_text(
                            f"✅ Added {credits} credits to user {user_id}\n\n"
                            f"📤 User notification sent successfully!"
                            # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                        )
                    except Exception as e:
                        await update.message.reply_text(
                            f"✅ Added {credits} credits to user {user_id}\n\n"
                            f"⚠️ Could not send notification to user"
                            # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                        )
                else:
                    await update.message.reply_text(
                        f"❌ User {user_id} not found"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )

                # IMPROVEMENT 5: Show input prompt again for easy re-use
                await self.show_add_credits_prompt_again(update, context)

            elif current_state == "modify_credits":
                parts = text.split()
                if len(parts) < 2:
                    await update.message.reply_text("❌ Invalid format. Use: `user_id new_credits`", parse_mode=ParseMode.MARKDOWN)
                    return ADMIN_ADD_USER

                user_id, credits = int(parts[0]), int(parts[1])
                success = self.user_model.update_user_credits(user_id, credits, "set")
                if success:
                    # NO NOTIFICATION TO USER (as per requirement 3)
                    await update.message.reply_text(
                        f"✅ Set credits to {credits} for user {user_id}"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )
                else:
                    await update.message.reply_text(
                        f"❌ User {user_id} not found"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )

                # IMPROVEMENT 5: Show input prompt again for easy re-use
                await self.show_modify_credits_prompt_again(update, context)

            elif current_state == "suspend_user":
                parts = text.split()
                if len(parts) < 1:
                    await update.message.reply_text("❌ Invalid format. Use: `user_id [days]`", parse_mode=ParseMode.MARKDOWN)
                    return ADMIN_ADD_USER

                user_id = int(parts[0])
                days = int(parts[1]) if len(parts) > 1 else None

                success = self.user_model.suspend_user(user_id, days)
                if success:
                    duration_text = f"for {days} days" if days else "indefinitely"
                    await update.message.reply_text(
                        f"✅ Suspended user {user_id} {duration_text}"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )
                else:
                    await update.message.reply_text(
                        f"❌ User {user_id} not found"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )

                # IMPROVEMENT 5: Show input prompt again for easy re-use
                await self.show_suspend_user_prompt_again(update, context)

            elif current_state == "unsuspend_user":
                user_id = int(text)
                success = self.user_model.unsuspend_user(user_id)
                if success:
                    await update.message.reply_text(
                        f"✅ Unsuspended user {user_id}"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )
                else:
                    await update.message.reply_text(
                        f"❌ User {user_id} not found"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )

                # IMPROVEMENT 5: Show input prompt again for easy re-use
                await self.show_unsuspend_user_prompt_again(update, context)

            elif current_state == "remove_user":
                user_id = int(text)
                success = self.user_model.remove_user(user_id)
                if success:
                    await update.message.reply_text(
                        f"✅ Removed user {user_id}"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )
                else:
                    await update.message.reply_text(
                        f"❌ User {user_id} not found"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )

                # IMPROVEMENT 5: Show input prompt again for easy re-use
                await self.show_remove_user_prompt_again(update, context)

            elif current_state == "hide_record":
                parts = text.split(':', 2)
                if len(parts) < 2:
                    await update.message.reply_text("❌ Invalid format. Use: `field:value:reason`", parse_mode=ParseMode.MARKDOWN)
                    return ADMIN_ADD_USER

                field, value = parts[0].strip().lower(), parts[1].strip()
                reason = parts[2].strip() if len(parts) > 2 else ""

                success, message = self.hidden_model.hide_record(field, value, user["user_id"], reason)
                await update.message.reply_text(
                    message
                    # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                )

            elif current_state == "unhide_record":
                parts = text.split(':', 1)
                if len(parts) < 2:
                    await update.message.reply_text("❌ Invalid format. Use: `field:value`", parse_mode=ParseMode.MARKDOWN)
                    return ADMIN_ADD_USER

                field, value = parts[0].strip().lower(), parts[1].strip()
                success, message = self.hidden_model.unhide_record(field, value)
                await update.message.reply_text(
                    message
                    # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                )

            elif current_state == "remove_vehicle":
                vehicle_number = text.upper()
                success = self.vehicle_model.remove_vehicle(vehicle_number)
                if success:
                    await update.message.reply_text(
                        f"✅ Removed vehicle {vehicle_number}"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )
                else:
                    await update.message.reply_text(
                        f"❌ Vehicle {vehicle_number} not found"
                        # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                    )

            elif current_state.startswith("broadcast_"):
                broadcast_type = current_state.split("_")[1]
                message_text = text

                # Get target users based on broadcast type
                all_users = self.user_model.get_all_users()

                if broadcast_type in ["broadcast", "all"]:
                    users = all_users
                elif broadcast_type == "active":
                    users = [u for u in all_users if self.user_model.is_user_authorized(u)]
                elif broadcast_type == "premium":
                    users = [u for u in all_users if self.user_model.get_plan(u) == "Premium"]
                elif broadcast_type == "trial":
                    users = [u for u in all_users if self.user_model.get_plan(u) == "Trial"]
                elif broadcast_type == "lea":
                    users = [u for u in all_users if u.get("user_type") == "lea_officer"]
                elif broadcast_type == "expired":
                    users = [u for u in all_users if not self.user_model.is_user_authorized(u)]
                elif broadcast_type == "expiring":
                    # Users expiring in next 3 days
                    from datetime import datetime, timedelta, timezone
                    users = []
                    for user in all_users:
                        expiry = self.user_model.normalize_datetime(user.get("expiry"))
                        if expiry:
                            days_left = (expiry - datetime.now(timezone.utc)).days
                            if 0 <= days_left <= 3:
                                users.append(user)
                else:
                    users = []

                # Send broadcast
                success_count = 0
                for target_user in users:
                    try:
                        await context.bot.send_message(
                            chat_id=target_user["user_id"],
                            text=f"📢 **Announcement**\n\n{message_text}",
                            parse_mode=ParseMode.MARKDOWN
                        )
                        success_count += 1
                    except Exception as e:
                        logger.error(f"Failed to send broadcast to {target_user['user_id']}: {e}")

                await update.message.reply_text(
                    f"✅ Announcement sent to {success_count}/{len(users)} users"
                    # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                )

            # Clear admin state
            context.user_data.pop("admin_state", None)

        except ValueError as e:
            await update.message.reply_text(
                f"❌ Invalid input: {str(e)}"
                # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
            )
        except Exception as e:
            logger.error(f"Error in admin input handling: {e}")
            await update.message.reply_text(
                "❌ An error occurred. Please try again."
                # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
            )

        return MENU

    # IMPROVEMENT 5: Helper methods to show input prompts again after operations
    async def show_add_user_prompt_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show add user prompt again after operation"""
        await update.message.reply_text(
            "➕ **Add User** (Ready for next entry)\n\n"
            "Please send in this format:\n"
            "📝 `user_id credits expiry_days`\n\n"
            "**Examples:**\n"
            "• `123456789 50 30` (50 credits, 30 days)\n"
            "• `987654321 100 lifetime` (100 credits, lifetime)\n\n"
            "**Expiry options:** 1, 2, 7, 15, 30, or 'lifetime'\n\n"
            "Use /cancel or click Back to exit.",
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )

    async def show_add_credits_prompt_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show add credits prompt again after operation"""
        await update.message.reply_text(
            "💳 **Add Credits** (Ready for next entry)\n\n"
            "Please send in this format:\n"
            "📝 `user_id credits [expiry_days]`\n\n"
            "**Examples:**\n"
            "• `123456789 25` (add 25 credits)\n"
            "• `987654321 50 15` (add 50 credits, extend 15 days)\n\n"
            "Use /cancel or click Back to exit.",
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )

    async def show_modify_credits_prompt_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show modify credits prompt again after operation"""
        await update.message.reply_text(
            "✏️ **Modify Credits** (Ready for next entry)\n\n"
            "Please send in this format:\n"
            "📝 `user_id new_credits`\n\n"
            "**Examples:**\n"
            "• `123456789 100` (set to 100 credits)\n"
            "• `987654321 0` (set to 0 credits)\n\n"
            "Use /cancel or click Back to exit.",
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )

    # REMOVED: show_convert_user_prompt_again - Now uses LEA wizard

    async def show_suspend_user_prompt_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show suspend user prompt again after operation"""
        await update.message.reply_text(
            "🚫 **Suspend User** (Ready for next entry)\n\n"
            "Please send in this format:\n"
            "📝 `user_id [days]`\n\n"
            "**Examples:**\n"
            "• `123456789` (suspend indefinitely)\n"
            "• `987654321 7` (suspend for 7 days)\n\n"
            "Use /cancel or click Back to exit.",
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )

    async def show_unsuspend_user_prompt_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show unsuspend user prompt again after operation"""
        await update.message.reply_text(
            "✅ **Unsuspend User** (Ready for next entry)\n\n"
            "Send the user ID to unsuspend:\n\n"
            "📝 **Format:** `user_id`\n"
            "**Example:** `123456789`\n\n"
            "Use /cancel or click Back to exit.",
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )

    async def show_remove_user_prompt_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show remove user prompt again after operation"""
        await update.message.reply_text(
            "🗑️ **Remove User** (Ready for next entry)\n\n"
            "Send the user ID to remove:\n\n"
            "📝 **Format:** `user_id`\n"
            "**Example:** `123456789`\n\n"
            "⚠️ **Warning:** This will permanently delete the user!\n\n"
            "Use /cancel or click Back to exit.",
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        )
