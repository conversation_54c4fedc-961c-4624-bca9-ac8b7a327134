# database/connection.py
import logging
from pymongo import MongoClient
from datetime import datetime, timezone, timedelta
from config.settings import MONGO_URI, DB_NAME, OWNER_ID
from config.constants import COLLECTIONS

logger = logging.getLogger(__name__)

# Global database connection
_client = None
_db = None

def get_database():
    """Get database connection (singleton pattern)"""
    global _client, _db
    
    if _db is None:
        # Configure MongoDB client with relaxed settings for VPS
        _client = MongoClient(
            MONGO_URI,
            serverSelectionTimeoutMS=30000,   # 30 second timeout for server selection
            connectTimeoutMS=30000,           # 30 second timeout for connection
            socketTimeoutMS=30000,            # 30 second timeout for socket operations
            heartbeatFrequencyMS=60000,       # Heartbeat every 60 seconds
            maxPoolSize=5,                    # Smaller connection pool
            minPoolSize=1,                    # Minimum connections in pool
            maxIdleTimeMS=60000,              # Close connections after 60s idle
            retryWrites=True,                 # Enable retry writes
            w=1                               # Write concern: acknowledge from primary
        )
        _db = _client[DB_NAME]
        logger.info(f"Connected to MongoDB database: {DB_NAME} with optimized settings")
    
    return _db

def get_collection(collection_name):
    """Get a specific collection"""
    db = get_database()
    return db[collection_name]

def init_database():
    """Initialize database with default settings and indexes"""
    db = get_database()
    
    # Initialize default settings
    _init_default_settings(db)
    
    # Create indexes
    _create_indexes(db)
    
    # Create admin user
    _create_admin_user(db)
    
    logger.info("Database initialized successfully")

def _init_default_settings(db):
    """Initialize default bot settings"""
    settings_collection = db[COLLECTIONS['SETTINGS']]
    
    default_config = {
        "_id": "config",
        "pricing_button_enabled": True,
        "pricing_button_text": "💲 Pricing",
        "pricing_text": "Each search costs 1 credit.\nContact admin to recharge.",
        "start_message": "👮 Welcome to Law Enforcement Bot!",
        "join_channel_text": "📢 Join Channel",
        "join_channel_link": "https://t.me/your_channel_here",
        "search_credit_cost": 1,
        "notifications_enabled": True,
        "expiry_notification_days": 3,
        "renewal_notification_enabled": True,
        "open_access_enabled": False,
        "trial_credits": 5,
        "daily_search_limit": 10,
        "per_minute_search_limit": 5,
        "rate_limiting_enabled": True,
        "credit_recharge_notifications": True,
        "insufficient_credits_message": "❌ **Insufficient Credits**\n\nYou don't have enough credits to perform this search.\nContact admin to recharge your account.",
        "access_denied_message": "❌ **Access Denied**\n\nYou are not authorized to use this bot. Please contact the administrator to get access.",
        "bot_restart_message": "🔄 **CyberNetra is Updated.**\n\nPlease use /start to continue.",
        "mobile_search_enabled": True,
        "aadhar_search_enabled": True,
        "alternate_search_enabled": True,
        "vehicle_search_enabled": True,
    }
    
    if settings_collection.count_documents({"_id": "config"}) == 0:
        settings_collection.insert_one(default_config)
        logger.info("Default settings initialized")

def _create_indexes(db):
    """Create database indexes for optimal performance"""
    # Userdata indexes
    userdata = db[COLLECTIONS['USERDATA']]
    userdata.create_index("mobile", background=True)
    userdata.create_index("id", background=True)
    userdata.create_index("alt", background=True)
    
    # Raj_data indexes (same as userdata for consistency)
    raj_data = db[COLLECTIONS['RAJ_DATA']]
    raj_data.create_index("mobile", background=True)
    raj_data.create_index("id", background=True)
    raj_data.create_index("AlternateNo", background=True)  # Different field name
    
    # Users indexes
    users = db[COLLECTIONS['USERS']]
    users.create_index("user_id", unique=True)
    users.create_index("username")
    users.create_index("is_admin")
    
    # Search logs indexes
    search_logs = db[COLLECTIONS['SEARCH_LOGS']]
    search_logs.create_index("user_id")
    search_logs.create_index("timestamp")
    search_logs.create_index([("user_id", 1), ("timestamp", -1)])
    
    # Notifications indexes
    notifications = db[COLLECTIONS['NOTIFICATIONS']]
    notifications.create_index("user_id")
    notifications.create_index("timestamp")
    
    # Hidden records indexes
    hidden_records = db[COLLECTIONS['HIDDEN_RECORDS']]
    hidden_records.create_index([("field", 1), ("value", 1)], unique=True)
    hidden_records.create_index("hidden_by")
    hidden_records.create_index("hidden_at")
    hidden_records.create_index("hide_type")
    
    # Vehicle data indexes
    vehicledata = db[COLLECTIONS['VEHICLE_DATA']]
    vehicledata.create_index("vehicle_number", unique=True)
    vehicledata.create_index("search_count")
    vehicledata.create_index("last_searched")
    vehicledata.create_index("created_by_user")
    
    logger.info("Database indexes created")

def _create_admin_user(db):
    """Create admin user if not exists"""
    users = db[COLLECTIONS['USERS']]
    
    admin = users.find_one({"user_id": OWNER_ID})
    if not admin:
        admin_doc = {
            "user_id": OWNER_ID,
            "username": "Owner",
            "credits": 9999999,
            "expiry": datetime.now(timezone.utc) + timedelta(days=365*100),
            "is_admin": True,
            "is_suspended": False,
            "suspended_until": None,
            "plan": "Admin",
            "search_limit_daily": None,
            "search_count_today": 0,
            "last_search_day": None,
            "created_at": datetime.now(timezone.utc),
        }
        users.insert_one(admin_doc)
        logger.info(f"Admin user created with ID: {OWNER_ID}")
    else:
        # Ensure admin privileges
        if not admin.get("is_admin", False):
            users.update_one({"user_id": OWNER_ID}, {"$set": {"is_admin": True}})
            logger.info("Admin privileges updated")

def close_connection():
    """Close database connection"""
    global _client
    if _client:
        _client.close()
        logger.info("Database connection closed")
