# admin.py
import logging
import csv
import io
from datetime import datetime, timedelta, timezone
from telegram import InlineKeyboardMarkup, InlineKeyboardButton, Update
from telegram.constants import ParseMode
from telegram.ext import ContextTypes
import config
import db_utils

logger = logging.getLogger(__name__)

def make_usernames_clickable(text):
    """Make @usernames clickable in text"""
    import re
    # Replace @username with clickable link
    pattern = r'@(\w+)'
    return re.sub(pattern, r'[@\1](tg://user?username=\1)', text)

async def admin_panel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Main admin panel"""
    query = update.callback_query
    if query:
        await query.answer()
    
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin", False):
        text = "❌ **Access Denied**\n\nYou don't have admin privileges."
        kb = InlineKeyboardMarkup([[InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")]])
    else:
        text = "⚙️ **Admin Panel**\n\nChoose an option:"
        kb = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("👥 User Management", callback_data="admin_user_mgmt"),
                InlineKeyboardButton("💰 Pricing Settings", callback_data="admin_pricing")
            ],
            [
                InlineKeyboardButton("✏️ Message Settings", callback_data="admin_messages"),
                InlineKeyboardButton("🔔 Notifications", callback_data="admin_notifications")
            ],
            [
                InlineKeyboardButton("📊 Statistics", callback_data="admin_stats"),
                InlineKeyboardButton("📑 Logs Settings", callback_data="admin_logs_settings")
            ],
            [
                InlineKeyboardButton("📢 Broadcast Message", callback_data="admin_broadcast"),
                InlineKeyboardButton("⚡ Rate Limiting", callback_data="admin_rate_limiting")
            ],
            [
                InlineKeyboardButton("🔐 Access Control", callback_data="admin_access_control"),
                InlineKeyboardButton("📤 Export Data", callback_data="admin_export")
            ],
            [
                InlineKeyboardButton("🔍 Search Settings", callback_data="admin_search_settings"),
                InlineKeyboardButton("🙈 Hidden Records", callback_data="admin_hidden_records")
            ],
            [
                InlineKeyboardButton("🚗 Vehicle Data", callback_data="admin_vehicle_data")
            ],
            [InlineKeyboardButton("⬅️ Back", callback_data="back_main")]
        ])
    
    if query:
        await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    else:
        await update.message.reply_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    
    return config.ADMIN_PANEL

async def admin_user_management(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """User management submenu"""
    query = update.callback_query
    await query.answer()
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("➕ Add User", callback_data="admin_add_user"),
            InlineKeyboardButton("👥 View All Users", callback_data="admin_view_users")
        ],
        [
            InlineKeyboardButton("💳 Add Credits", callback_data="admin_add_credits"),
            InlineKeyboardButton("✏️ Modify Credits", callback_data="admin_modify_credits")
        ],
        [
            InlineKeyboardButton("🚫 Suspend User", callback_data="admin_suspend"),
            InlineKeyboardButton("✅ Unsuspend User", callback_data="admin_unsuspend")
        ],
        [
            InlineKeyboardButton("❌ Remove User", callback_data="admin_remove_user")
        ],
        [
            InlineKeyboardButton("⬅️ Back", callback_data="admin_panel"),
            InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
        ],
    ])
    
    await query.edit_message_text(
        "👥 **User Management**\n\nChoose an action:",
        reply_markup=kb,
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_PANEL

async def admin_pricing_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Pricing settings submenu"""
    query = update.callback_query
    await query.answer()
    
    current_cost = db_utils.get_setting("search_credit_cost", 1)
    pricing_enabled = db_utils.get_setting("pricing_button_enabled", True)
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("💵 Set Search Cost", callback_data="admin_set_cost"),
            InlineKeyboardButton("✏️ Edit Pricing Text", callback_data="admin_edit_pricing_text")
        ],
        [
            InlineKeyboardButton(
                f"💰 Toggle Pricing Button ({'ON' if pricing_enabled else 'OFF'})", 
                callback_data="admin_toggle_pricing"
            )
        ],
        [
            InlineKeyboardButton("⬅️ Back", callback_data="admin_panel"),
            InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
        ],
    ])
    
    text = (
        f"💰 **Pricing Settings**\n\n"
        f"Current search cost: {current_cost} credits\n"
        f"Pricing button: {'Enabled' if pricing_enabled else 'Disabled'}"
    )
    
    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_message_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Message settings submenu"""
    query = update.callback_query
    await query.answer()
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("🚀 Edit Start Message", callback_data="admin_edit_start"),
            InlineKeyboardButton("💰 Edit Insufficient Credits", callback_data="admin_edit_insufficient_credits")
        ],
        [
            InlineKeyboardButton("🚫 Edit Access Denied", callback_data="admin_edit_access_denied"),
            InlineKeyboardButton("🔄 Edit Restart Message", callback_data="admin_edit_restart")
        ],
        [
            InlineKeyboardButton("📢 Edit Channel Settings", callback_data="admin_edit_channel")
        ],
        [
            InlineKeyboardButton("⬅️ Back", callback_data="admin_panel"),
            InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
        ]
    ])
    
    await query.edit_message_text(
        "💬 **Message Settings**\n\n"
        "Configure bot messages and responses.",
        reply_markup=kb,
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_PANEL

async def admin_notification_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Notification settings submenu"""
    query = update.callback_query
    await query.answer()
    
    notifications_enabled = db_utils.get_setting("notifications_enabled", True)
    renewal_notifications = db_utils.get_setting("renewal_notification_enabled", True)
    expiry_days = db_utils.get_setting("expiry_notification_days", 3)
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton(
                f"🔔 Credit Notifications ({'ON' if notifications_enabled else 'OFF'})",
                callback_data="admin_toggle_notifications"
            ),
            InlineKeyboardButton(
                f"📅 Expiry Notifications ({'ON' if renewal_notifications else 'OFF'})",
                callback_data="admin_toggle_renewal"
            )
        ],
        [
            InlineKeyboardButton("⏰ Set Expiry Warning Days", callback_data="admin_set_expiry_days")
        ],
        [
            InlineKeyboardButton("⬅️ Back", callback_data="admin_panel"),
            InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
        ],
    ])
    
    text = (
        f"🔔 **Notification Settings**\n\n"
        f"Credit notifications: {'Enabled' if notifications_enabled else 'Disabled'}\n"
        f"Expiry notifications: {'Enabled' if renewal_notifications else 'Disabled'}\n"
        f"Expiry warning: {expiry_days} days before"
    )
    
    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_logs_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Logs settings submenu"""
    query = update.callback_query
    await query.answer()
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("📑 View All Logs", callback_data="admin_all_logs"),
            InlineKeyboardButton("👤 View User Logs", callback_data="admin_user_logs")
        ],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")],
    ])
    
    await query.edit_message_text(
        "📑 **Logs Settings**\n\nChoose log type to view:",
        reply_markup=kb,
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_PANEL

async def admin_statistics(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Show simplified admin statistics - fast and lightweight"""
    query = update.callback_query
    await query.answer()

    try:
        # Fast queries only - no heavy operations
        total_users = db_utils.users.estimated_document_count()  # Much faster than count_documents

        # Get premium users (users with more than trial credits and not expired)
        trial_credits = db_utils.get_setting("trial_credits", 5)
        premium_users = db_utils.users.count_documents({
            "credits": {"$gt": trial_credits},
            "expiry": {"$gt": datetime.now(timezone.utc)}
        })

        text = (
            f"📊 **Bot Statistics**\n\n"
            f"👥 Total Users: {total_users:,}\n"
            f"💎 Premium Users: {premium_users}"
        )

    except Exception as e:
        # Fallback in case of any errors
        text = (
            f"📊 **Bot Statistics**\n\n"
            f"⚠️ Statistics temporarily unavailable\n"
            f"Error: {str(e)[:50]}...\n\n"
            f"Please try again in a moment."
        )

    kb = InlineKeyboardMarkup([
        [InlineKeyboardButton("🔄 Refresh", callback_data="admin_stats")],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")],
    ])

    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_view_all_users(update: Update, context: ContextTypes.DEFAULT_TYPE, page=0):
    """View all users with pagination"""
    query = update.callback_query
    if query:
        await query.answer()
    
    users_list = db_utils.get_all_users()
    
    if not users_list:
        text = "👥 **All Users**\n\n❌ No users found."
        kb = InlineKeyboardMarkup([[InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")]])
    else:
        start = page * config.USERS_PER_PAGE
        page_users = users_list[start:start + config.USERS_PER_PAGE]
        total_pages = (len(users_list) - 1) // config.USERS_PER_PAGE + 1
        
        user_lines = []
        for user in page_users:
            username = user.get("username", "Unknown")
            user_id = user.get("user_id", "N/A")
            credits = user.get("credits", 0)
            plan = db_utils.get_plan(user)
            
            # Determine status
            status_parts = []
            if user.get("is_suspended", False):
                suspend_until = db_utils.normalize_datetime(user.get("suspended_until"))
                if suspend_until:
                    status_parts.append(f"🚫 Suspended until {suspend_until.strftime('%Y-%m-%d')}")
                else:
                    status_parts.append("🚫 Suspended permanently")
            
            # Check if expired
            expiry = db_utils.normalize_datetime(user.get("expiry"))
            if expiry and expiry < datetime.now(timezone.utc) and not user.get("is_admin", False):
                status_parts.append("⏰ Expired")
            
            if not status_parts:
                status_parts.append("✅ Active")
            
            status = " | ".join(status_parts)
            
            # Escape special characters for Markdown
            username_safe = username.replace("_", "\\_").replace("*", "\\*").replace("`", "\\`")
            
            user_lines.append(
                f"**{username_safe}** (`{user_id}`)\n"
                f"💎 Credits: {credits} | 🛡 Plan: {plan}\n"
                f"📊 Status: {status}"
            )
        
        text = f"👥 **All Users** (Page {page + 1}/{total_pages})\n\n" + "\n\n".join(user_lines)
        
        buttons = []
        nav_buttons = []
        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ Prev", callback_data=f"admin_users_page:{page-1}"))
        if page < total_pages - 1:
            nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"admin_users_page:{page+1}"))
        
        if nav_buttons:
            buttons.append(nav_buttons)
        
        buttons.append([InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")])
        kb = InlineKeyboardMarkup(buttons)
    
    if query:
        await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    else:
        await update.message.reply_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    
    return config.ADMIN_PANEL

async def admin_view_all_logs(update: Update, context: ContextTypes.DEFAULT_TYPE, page=0):
    """View all search logs with pagination"""
    query = update.callback_query
    if query:
        await query.answer()
    
    logs = db_utils.get_all_logs(limit=1000)
    
    if not logs:
        text = "📑 **All Search Logs**\n\n❌ No logs found."
        kb = InlineKeyboardMarkup([[InlineKeyboardButton("⬅️ Back", callback_data="admin_logs")]])
    else:
        start = page * config.LOGS_PER_PAGE
        page_logs = logs[start:start + config.LOGS_PER_PAGE]
        total_pages = (len(logs) - 1) // config.LOGS_PER_PAGE + 1
        
        log_lines = []
        for log in page_logs:
            user = db_utils.get_user(log["user_id"])
            username = user.get("username", "Unknown") if user else "Deleted"
            timestamp = log["timestamp"].strftime("%m-%d %H:%M")
            query_type = log["query_type"].title()
            query_text = log["query"]
            results = log.get("results_count", 0)
            
            log_lines.append(
                f"👤 **{username}** (`{log['user_id']}`)\n"
                f"🔍 {query_type}: `{query_text}` | 📊 {results} results\n"
                f"⏰ {timestamp}"
            )
        
        text = f"📑 **All Search Logs** (Page {page + 1}/{total_pages})\n\n" + "\n\n".join(log_lines)
        
        buttons = []
        nav_buttons = []
        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ Prev", callback_data=f"admin_logs_page:{page-1}"))
        if page < total_pages - 1:
            nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"admin_logs_page:{page+1}"))
        
        if nav_buttons:
            buttons.append(nav_buttons)
        
        buttons.append([InlineKeyboardButton("⬅️ Back", callback_data="admin_logs")])
        kb = InlineKeyboardMarkup(buttons)
    
    if query:
        await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    else:
        await update.message.reply_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    
    return config.ADMIN_PANEL

# Command handlers for direct admin commands
async def adduser_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Add user command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 2:
        await update.message.reply_text(
            "Usage: /adduser <user_id> <username> [credits] [expiry_days]\n"
            "Example: /adduser 123456789 john_doe 10 30"
        )
        return
    
    try:
        user_id = int(context.args[0])
        username = context.args[1]
        credits = int(context.args[2]) if len(context.args) > 2 else 5
        expiry_days = int(context.args[3]) if len(context.args) > 3 else 30
        
        success, message = db_utils.add_user(user_id, username, credits, expiry_days)
        await update.message.reply_text(f"{'✅' if success else '❌'} {message}")
        
    except ValueError:
        await update.message.reply_text("❌ Invalid user ID or numbers.")
    except Exception as e:
        logger.error(f"Error adding user: {e}")
        await update.message.reply_text("❌ Error adding user.")

async def addcredits_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Add credits command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 2:
        await update.message.reply_text("Usage: /addcredits <user_id> <credits>")
        return
    
    try:
        user_id = int(context.args[0])
        credits = int(context.args[1])
        
        if db_utils.update_user_credits(user_id, credits, "add"):
            await update.message.reply_text(f"✅ Added {credits} credits to user {user_id}")
        else:
            await update.message.reply_text("❌ User not found.")
            
    except ValueError:
        await update.message.reply_text("❌ Invalid user ID or credit amount.")
    except Exception as e:
        logger.error(f"Error adding credits: {e}")
        await update.message.reply_text("❌ Error adding credits.")

async def setcredits_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set credits command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 2:
        await update.message.reply_text("Usage: /setcredits <user_id> <credits>")
        return
    
    try:
        user_id = int(context.args[0])
        credits = int(context.args[1])
        
        if db_utils.update_user_credits(user_id, credits, "set"):
            await update.message.reply_text(f"✅ Set credits to {credits} for user {user_id}")
        else:
            await update.message.reply_text("❌ User not found.")
            
    except ValueError:
        await update.message.reply_text("❌ Invalid user ID or credit amount.")
    except Exception as e:
        logger.error(f"Error setting credits: {e}")
        await update.message.reply_text("❌ Error setting credits.")

async def suspenduser_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Suspend user command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /suspenduser <user_id> [days]")
        return
    
    try:
        user_id = int(context.args[0])
        days = int(context.args[1]) if len(context.args) > 1 else None
        
        if db_utils.suspend_user(user_id, days):
            duration = f"for {days} days" if days else "permanently"
            await update.message.reply_text(f"✅ Suspended user {user_id} {duration}")
        else:
            await update.message.reply_text("❌ User not found.")
            
    except ValueError:
        await update.message.reply_text("❌ Invalid user ID or days.")
    except Exception as e:
        logger.error(f"Error suspending user: {e}")
        await update.message.reply_text("❌ Error suspending user.")

async def unsuspenduser_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Unsuspend user command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /unsuspenduser <user_id>")
        return
    
    try:
        user_id = int(context.args[0])
        
        if db_utils.unsuspend_user(user_id):
            await update.message.reply_text(f"✅ Unsuspended user {user_id}")
        else:
            await update.message.reply_text("❌ User not found.")
            
    except ValueError:
        await update.message.reply_text("❌ Invalid user ID.")
    except Exception as e:
        logger.error(f"Error unsuspending user: {e}")
        await update.message.reply_text("❌ Error unsuspending user.")

async def removeuser_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Remove user command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /removeuser <user_id>")
        return
    
    try:
        user_id = int(context.args[0])
        
        if user_id == config.OWNER_ID:
            await update.message.reply_text("❌ Cannot remove owner.")
            return
        
        if db_utils.remove_user(user_id):
            await update.message.reply_text(f"✅ Removed user {user_id}")
        else:
            await update.message.reply_text("❌ User not found.")
            
    except ValueError:
        await update.message.reply_text("❌ Invalid user ID.")
    except Exception as e:
        logger.error(f"Error removing user: {e}")
        await update.message.reply_text("❌ Error removing user.")

async def setprice_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set search price command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /setprice <credits_per_search>")
        return
    
    try:
        price = int(context.args[0])
        if price < 0:
            await update.message.reply_text("❌ Price cannot be negative.")
            return
        
        db_utils.set_setting("search_credit_cost", price)
        await update.message.reply_text(f"✅ Set search cost to {price} credits")
        
    except ValueError:
        await update.message.reply_text("❌ Invalid price.")
    except Exception as e:
        logger.error(f"Error setting price: {e}")
        await update.message.reply_text("❌ Error setting price.")

async def toggle_notifications_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toggle notifications command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    current = db_utils.get_setting("notifications_enabled", True)
    new_value = not current
    db_utils.set_setting("notifications_enabled", new_value)
    
    status = "enabled" if new_value else "disabled"
    await update.message.reply_text(f"✅ Notifications {status}")

# Handler for admin panel callback queries
async def admin_callback_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle admin panel callbacks"""
    query = update.callback_query
    data = query.data
    
    # Handle pagination callbacks
    if data.startswith("admin_users_page:"):
        page = int(data.split(":")[1])
        return await admin_view_all_users(update, context, page)
    elif data.startswith("admin_logs_page:"):
        page = int(data.split(":")[1])
        return await admin_view_all_logs(update, context, page)
    
    # Handle other admin callbacks
    if data == "admin_view_users":
        return await admin_view_all_users(update, context)
    elif data == "admin_user_mgmt":
        return await admin_user_management(update, context)
    elif data == "admin_logs":
        return await admin_logs_settings(update, context)
    elif data == "admin_all_logs":
        return await admin_view_all_logs(update, context)
    elif data == "admin_export":
        return await admin_export_menu(update, context)
    elif data == "admin_export_users":
        return await admin_export_users(update, context)
    elif data == "admin_export_logs":
        return await admin_export_logs(update, context)
    elif data == "admin_export_vehicles":
        return await admin_export_vehicles(update, context)
    elif data == "admin_vehicle_data":
        return await admin_vehicle_data_menu(update, context)
    elif data == "admin_remove_vehicle":
        return await admin_remove_vehicle_prompt(update, context)
    elif data == "admin_pricing":
        return await admin_pricing_settings(update, context)
    elif data == "admin_modify_credits":
        return await admin_modify_credits_prompt(update, context)
    elif data == "admin_user_logs":
        return await admin_view_user_logs_prompt(update, context)
    
    if data == "admin_panel":
        return await admin_panel_handler(update, context)
    elif data == "admin_user_mgmt":
        return await admin_user_management(update, context)
    elif data == "admin_add_user":
        return await admin_add_user_prompt(update, context)
    elif data == "admin_add_credits":
        return await admin_add_credits_prompt(update, context)
    elif data == "admin_modify_credits":
        return await admin_modify_credits_prompt(update, context)
    elif data == "admin_suspend":
        return await admin_suspend_user_prompt(update, context)
    elif data == "admin_unsuspend":
        return await admin_unsuspend_user_prompt(update, context)
    elif data == "admin_remove_user":
        return await admin_remove_user_prompt(update, context)
    elif data == "admin_view_users":
        return await admin_view_users(update, context)
    elif data == "admin_logs_settings":
        return await admin_logs_settings(update, context)
    elif data == "admin_all_logs":
        return await admin_view_all_logs(update, context)
    elif data == "admin_user_logs":
        return await admin_view_user_logs_prompt(update, context)
    elif data == "admin_messages":
        return await admin_message_settings(update, context)
    elif data == "admin_edit_start":
        return await admin_edit_start_prompt(update, context)
    elif data == "admin_edit_insufficient_credits":
        return await admin_edit_insufficient_credits_prompt(update, context)
    elif data == "admin_edit_channel":
        return await admin_channel_settings(update, context)
    elif data == "admin_change_channel_link":
        return await admin_change_channel_link_prompt(update, context)
    elif data == "admin_change_channel_text":
        return await admin_change_channel_text_prompt(update, context)
    elif data == "admin_notifications":
        return await admin_notification_settings(update, context)
    elif data == "admin_set_expiry_days":
        return await admin_set_expiry_days_prompt(update, context)
    elif data == "admin_toggle_notifications":
        current = db_utils.get_setting("notifications_enabled", True)
        db_utils.set_setting("notifications_enabled", not current)
        await query.answer(f"Notifications {'disabled' if current else 'enabled'}")
        return await admin_notification_settings(update, context)
    elif data == "admin_pricing":
        return await admin_pricing_settings(update, context)
    elif data == "admin_set_cost":
        return await admin_set_cost_prompt(update, context)
    elif data == "admin_edit_pricing_text":
        return await admin_edit_pricing_text_prompt(update, context)
    elif data == "admin_toggle_pricing":
        current = db_utils.get_setting("pricing_button_enabled", True)
        db_utils.set_setting("pricing_button_enabled", not current)
        await query.answer(f"Pricing button {'disabled' if current else 'enabled'}")
        return await admin_pricing_settings(update, context)
    elif data == "admin_toggle_renewal":
        current = db_utils.get_setting("renewal_notification_enabled", True)
        db_utils.set_setting("renewal_notification_enabled", not current)
        await query.answer(f"Renewal notifications {'disabled' if current else 'enabled'}")
        return await admin_notification_settings(update, context)
    elif data == "admin_broadcast":
        return await admin_broadcast_settings(update, context)
    elif data == "admin_rate_limiting":
        return await admin_rate_limiting_settings(update, context)
    elif data == "admin_access_control":
        return await admin_access_control_settings(update, context)
    elif data == "admin_send_broadcast":
        return await admin_broadcast_prompt(update, context)
    elif data == "admin_broadcast_active":
        return await admin_broadcast_prompt(update, context)
    elif data == "admin_broadcast_premium":
        return await admin_broadcast_prompt(update, context)
    elif data == "admin_broadcast_trial":
        return await admin_broadcast_prompt(update, context)
    elif data == "admin_set_daily_limit":
        return await admin_set_daily_limit_prompt(update, context)
    elif data == "admin_set_minute_limit":
        return await admin_set_minute_limit_prompt(update, context)
    elif data == "admin_trial_settings":
        return await admin_trial_settings_prompt(update, context)
    elif data == "admin_toggle_open_access":
        current = db_utils.get_setting("open_access_enabled", False)
        db_utils.set_setting("open_access_enabled", not current)
        await query.answer(f"Open access {'disabled' if current else 'enabled'}")
        return await admin_access_control_settings(update, context)
    elif data == "admin_toggle_credit_notifications":
        current = db_utils.get_setting("credit_recharge_notifications", True)
        db_utils.set_setting("credit_recharge_notifications", not current)
        await query.answer(f"Credit notifications {'disabled' if current else 'enabled'}")
        return await admin_access_control_settings(update, context)
    elif data == "admin_toggle_rate_limiting":
        current = db_utils.get_setting("rate_limiting_enabled", True)
        db_utils.set_setting("rate_limiting_enabled", not current)
        await query.answer(f"Rate limiting {'disabled' if current else 'enabled'}")
        return await admin_rate_limiting_settings(update, context)
    elif data == "admin_stats":
        return await admin_statistics(update, context)
    elif data == "admin_search_settings":
        return await admin_search_settings(update, context)
    elif data == "admin_toggle_mobile_search":
        current = db_utils.get_setting("mobile_search_enabled", True)
        db_utils.set_setting("mobile_search_enabled", not current)
        await query.answer(f"Mobile search {'disabled' if current else 'enabled'}")
        return await admin_search_settings(update, context)
    elif data == "admin_toggle_aadhar_search":
        current = db_utils.get_setting("aadhar_search_enabled", True)
        db_utils.set_setting("aadhar_search_enabled", not current)
        await query.answer(f"Aadhar search {'disabled' if current else 'enabled'}")
        return await admin_search_settings(update, context)
    elif data == "admin_toggle_alternate_search":
        current = db_utils.get_setting("alternate_search_enabled", True)
        db_utils.set_setting("alternate_search_enabled", not current)
        await query.answer(f"Alternate search {'disabled' if current else 'enabled'}")
        return await admin_search_settings(update, context)
    elif data == "admin_toggle_vehicle_search":
        current = db_utils.get_setting("vehicle_search_enabled", True)
        db_utils.set_setting("vehicle_search_enabled", not current)
        await query.answer(f"Vehicle search {'disabled' if current else 'enabled'}")
        return await admin_search_settings(update, context)
    elif data == "admin_edit_access_denied":
        return await admin_edit_access_denied_prompt(update, context)
    elif data == "admin_edit_restart":
        return await admin_edit_restart_prompt(update, context)
    elif data.startswith("admin_users_page:"):
        page = int(data.split(":")[1])
        return await admin_view_users(update, context, page)
    elif data.startswith("admin_logs_page:"):
        page = int(data.split(":")[1])
        return await admin_view_all_logs(update, context, page)
    elif data == "admin_hidden_records":
        return await admin_hidden_records_menu(update, context)
    elif data == "admin_hide_record":
        return await admin_hide_record_prompt(update, context)
    elif data == "admin_unhide_record":
        return await admin_unhide_record_prompt(update, context)
    elif data == "admin_view_hidden":
        return await admin_view_hidden_records(update, context)
    elif data.startswith("admin_hidden_page:"):
        page = int(data.split(":")[1])
        return await admin_view_hidden_records(update, context, page)
    else:
        await query.answer("Unknown action.")
        return config.ADMIN_PANEL

# Additional command handlers for message settings
async def setprestart_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set pre-start message command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /setprestart <message>")
        return
    
    message = " ".join(context.args)
    db_utils.set_setting("prestart_message", message)
    await update.message.reply_text("✅ Pre-start message updated")

async def setstart_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set start message command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /setstart <message>")
        return
    
    message = " ".join(context.args)
    db_utils.set_setting("start_message", message)
    await update.message.reply_text("✅ Start message updated")

async def setpricingtext_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set pricing text command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /setpricingtext <text>")
        return
    
    text = " ".join(context.args)
    db_utils.set_setting("pricing_text", text)
    await update.message.reply_text("✅ Pricing text updated")

async def setchanneltext_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set channel button text command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /setchanneltext <text>")
        return
    
    text = " ".join(context.args)
    db_utils.set_setting("join_channel_text", text)
    await update.message.reply_text("✅ Channel button text updated")

async def setchannellink_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set channel link command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return
    
    if len(context.args) < 1:
        await update.message.reply_text("Usage: /setchannellink <link>")
        return
    
    link = context.args[0]
    db_utils.set_setting("join_channel_link", link)
    await update.message.reply_text("✅ Channel link updated")

# Vehicle Data Management
async def admin_vehicle_data_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Show vehicle data management menu"""
    query = update.callback_query
    await query.answer()

    # Get vehicle database stats
    vehicle_stats = db_utils.get_vehicle_database_stats()
    if vehicle_stats:
        total_vehicles = vehicle_stats.get("total_vehicles", 0)
        top_vehicles = vehicle_stats.get("top_vehicles", [])

        text = (
            f"🚗 **Vehicle Data Management**\n\n"
            f"📊 **Database Statistics:**\n"
            f"Total Vehicles: {total_vehicles:,}\n"
            f"RC Data: {vehicle_stats.get('rc_data_count', 0):,}\n"
            f"GTPlay Data: {vehicle_stats.get('gtplay_data_count', 0):,}\n"
            f"Challan Data: {vehicle_stats.get('challan_data_count', 0):,}\n"
            f"FASTag Data: {vehicle_stats.get('fastag_data_count', 0):,}\n\n"
        )

        if top_vehicles:
            text += f"🔥 **Most Searched Vehicles:**\n"
            for i, vehicle in enumerate(top_vehicles[:3], 1):
                text += f"{i}. {vehicle.get('vehicle_number', 'N/A')} ({vehicle.get('search_count', 0)} searches)\n"
    else:
        text = (
            f"🚗 **Vehicle Data Management**\n\n"
            f"📊 No vehicle data available."
        )

    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("🗑️ Remove Vehicle", callback_data="admin_remove_vehicle")
        ],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
    ])

    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_remove_vehicle_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt admin to enter vehicle number to remove"""
    query = update.callback_query
    await query.answer()

    text = (
        f"🗑️ **Remove Vehicle Data**\n\n"
        f"Enter the vehicle number you want to remove from our database.\n\n"
        f"⚠️ **Warning**: This action cannot be undone!\n\n"
        f"Example: DL1AB123"
    )

    kb = InlineKeyboardMarkup([
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_vehicle_data")]
    ])

    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_REMOVE_VEHICLE

async def admin_remove_vehicle_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle vehicle removal"""
    vehicle_number = update.message.text.strip().upper()

    # Validate vehicle number format
    if len(vehicle_number) < 7 or len(vehicle_number) > 10 or not vehicle_number.isalnum():
        await update.message.reply_text(
            "❌ **Invalid Vehicle Number**\n\nPlease enter a valid vehicle number (7-10 characters, letters and numbers only).",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_vehicle_data")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        return config.ADMIN_REMOVE_VEHICLE

    try:
        # Check if vehicle exists
        vehicle = db_utils.get_cached_vehicle(vehicle_number)

        if not vehicle:
            await update.message.reply_text(
                f"❌ **Vehicle Not Found**\n\nVehicle `{vehicle_number}` is not in our database.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_vehicle_data")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return config.ADMIN_REMOVE_VEHICLE

        # Remove vehicle from database
        result = db_utils.vehicledata.delete_one({"_id": vehicle_number})

        if result.deleted_count > 0:
            # Format timestamps for display
            first_searched = vehicle.get('first_searched', 'N/A')
            last_searched = vehicle.get('last_searched', 'N/A')

            if hasattr(first_searched, 'strftime'):
                first_searched = first_searched.strftime('%Y-%m-%d %H:%M:%S')
            if hasattr(last_searched, 'strftime'):
                last_searched = last_searched.strftime('%Y-%m-%d %H:%M:%S')

            await update.message.reply_text(
                f"✅ **Vehicle Removed Successfully**\n\n"
                f"Vehicle `{vehicle_number}` has been removed from our database.\n\n"
                f"📊 **Removed Data:**\n"
                f"• Search Count: {vehicle.get('search_count', 0)}\n"
                f"• Data Sources: {', '.join(vehicle.get('data_sources', []))}\n"
                f"• First Searched: {first_searched}\n"
                f"• Last Searched: {last_searched}",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_vehicle_data")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                f"❌ **Removal Failed**\n\nFailed to remove vehicle `{vehicle_number}` from database.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_vehicle_data")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )

    except Exception as e:
        logger.error(f"Vehicle removal error: {e}")
        await update.message.reply_text(
            "❌ **Error**\n\nAn error occurred while removing the vehicle. Please try again.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_vehicle_data")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

    return config.ADMIN_PANEL

# Export functionality
async def admin_export_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Show export options menu"""
    query = update.callback_query
    await query.answer()

    # Get statistics
    vehicle_stats = db_utils.get_vehicle_database_stats()
    vehicle_count = vehicle_stats.get("total_vehicles", 0) if vehicle_stats else 0

    user_count = db_utils.users.count_documents({})
    log_count = db_utils.search_logs.count_documents({})

    text = (
        f"📤 **Export Data**\n\n"
        f"Choose what data to export:\n\n"
        f"👥 **All Users**: Export user data ({user_count:,} users)\n"
        f"📊 **Search Logs**: Export search history ({log_count:,} logs)\n"
        f"🚗 **Vehicle Database**: Export vehicle database ({vehicle_count:,} vehicles)"
    )

    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("👥 All Users", callback_data="admin_export_users"),
            InlineKeyboardButton("📊 Search Logs", callback_data="admin_export_logs")
        ],
        [
            InlineKeyboardButton("🚗 Vehicle Database", callback_data="admin_export_vehicles")
        ],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
    ])

    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_export_vehicles(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Export vehicle database to CSV"""
    query = update.callback_query
    await query.answer()

    try:
        # Get all vehicles from our database
        vehicles = list(db_utils.vehicledata.find({}))

        if not vehicles:
            await query.edit_message_text(
                "📊 **No Vehicle Data**\n\nNo vehicles found in database to export.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return config.ADMIN_PANEL

        # Create CSV
        vehicle_csv = io.StringIO()
        writer = csv.writer(vehicle_csv)

        # Headers
        writer.writerow([
            'Vehicle Number', 'Owner Name', 'Father Name', 'Present Address', 'Permanent Address',
            'Registration Date', 'RC Status', 'Maker Model', 'Body Type', 'Color', 'Fuel Type',
            'Chassis No', 'Engine No', 'Insurance Company', 'Insurance Expiry', 'PUC Expiry',
            'Search Count', 'First Searched', 'Last Searched', 'Created By User', 'Data Sources'
        ])

        # Data rows
        for vehicle in vehicles:
            # Extract data from different API sources
            rc_data = vehicle.get('rc_data', {}) or {}
            gtplay_data = vehicle.get('gtplay_data', {}) or {}

            # Helper function to get value with fallback
            def get_value(primary, secondary, key, default='N/A'):
                if primary and primary.get(key):
                    return str(primary.get(key, default))
                elif secondary and secondary.get(key):
                    return str(secondary.get(key, default))
                return default

            writer.writerow([
                vehicle.get('vehicle_number', 'N/A'),
                get_value(rc_data, gtplay_data, 'owner_name'),
                rc_data.get('father_name', 'N/A'),
                rc_data.get('present_address', 'N/A'),
                rc_data.get('permanent_address', 'N/A'),
                get_value(rc_data, gtplay_data, 'registration_date'),
                get_value(rc_data, gtplay_data, 'rc_status'),
                get_value(rc_data, gtplay_data, 'brand_name', get_value(rc_data, gtplay_data, 'maker_model')),
                rc_data.get('brand_model', gtplay_data.get('body_type_desc', 'N/A')),
                get_value(rc_data, gtplay_data, 'color', get_value(rc_data, gtplay_data, 'vehicle_color')),
                get_value(rc_data, gtplay_data, 'fuel_type'),
                get_value(rc_data, gtplay_data, 'chassis_number', get_value(rc_data, gtplay_data, 'chassis_no')),
                get_value(rc_data, gtplay_data, 'engine_number', get_value(rc_data, gtplay_data, 'engine_no')),
                get_value(rc_data, gtplay_data, 'insurance_company'),
                get_value(rc_data, gtplay_data, 'insurance_expiry', get_value(rc_data, gtplay_data, 'insurance_upto')),
                get_value(rc_data, gtplay_data, 'pucc_upto', get_value(rc_data, gtplay_data, 'puc_upto')),
                vehicle.get('search_count', 0),
                vehicle.get('first_searched', '').strftime('%Y-%m-%d %H:%M:%S') if vehicle.get('first_searched') else 'N/A',
                vehicle.get('last_searched', '').strftime('%Y-%m-%d %H:%M:%S') if vehicle.get('last_searched') else 'N/A',
                vehicle.get('created_by_user', 'N/A'),
                ', '.join(vehicle.get('data_sources', []))
            ])

        # Send CSV file
        vehicle_csv.seek(0)
        await query.message.reply_document(
            document=io.BytesIO(vehicle_csv.getvalue().encode()),
            filename=f"vehicle_database_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            caption=f"🚗 **Vehicle Database Export**\n\n{len(vehicles):,} vehicles exported successfully."
        )

        await query.edit_message_text(
            f"✅ **Vehicle Export Completed**\n\n{len(vehicles):,} vehicles exported successfully!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"Vehicle export error: {e}")
        await query.edit_message_text(
            "❌ **Export Failed**\n\nAn error occurred during vehicle export. Please try again.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

    return config.ADMIN_PANEL

async def admin_export_users(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Export users data to CSV"""
    query = update.callback_query
    await query.answer()

    try:
        # Export users
        users = db_utils.get_all_users()
        user_csv = io.StringIO()
        writer = csv.writer(user_csv)
        writer.writerow(['User ID', 'Username', 'Credits', 'Plan', 'Expiry', 'Suspended', 'Status'])

        for user in users:
            expiry = user.get('expiry', '')
            if expiry:
                expiry_str = expiry.strftime('%Y-%m-%d') if hasattr(expiry, 'strftime') else str(expiry)
            else:
                expiry_str = 'N/A'

            # Determine status - FIX DATETIME COMPARISON
            status = "Active"
            if user.get("is_suspended", False):
                status = "Suspended"
            elif expiry and hasattr(expiry, 'strftime') and not user.get("is_admin", False):
                # Fix timezone comparison issue
                now = datetime.now(timezone.utc)
                if expiry.tzinfo is None:
                    expiry = expiry.replace(tzinfo=timezone.utc)
                if expiry < now:
                    status = "Expired"

            writer.writerow([
                user.get('user_id', ''),
                user.get('username', ''),
                user.get('credits', 0),
                db_utils.get_plan(user),
                expiry_str,
                'Yes' if user.get('is_suspended') else 'No',
                status
            ])

        # Send CSV file
        user_csv.seek(0)
        await query.message.reply_document(
            document=io.BytesIO(user_csv.getvalue().encode()),
            filename=f"users_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            caption=f"👥 **Users Export**\n\n{len(users):,} users exported successfully."
        )

        await query.edit_message_text(
            f"✅ **Users Export Completed**\n\n{len(users):,} users exported successfully!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"Users export error: {e}")
        await query.edit_message_text(
            "❌ **Users Export Failed**\n\nAn error occurred during users export. Please try again.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

    return config.ADMIN_PANEL

async def admin_export_logs(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Export search logs to CSV"""
    query = update.callback_query
    await query.answer()

    try:
        
        # Export logs
        logs = db_utils.get_all_logs(limit=5000)
        log_csv = io.StringIO()
        writer = csv.writer(log_csv)
        writer.writerow(['User ID', 'Username', 'Query Type', 'Query', 'Results', 'Timestamp'])
        
        for log in logs:
            user = db_utils.get_user(log.get('user_id'))
            username = user.get('username', 'Unknown') if user else 'Deleted User'
            
            timestamp_str = ''
            if log.get('timestamp'):
                if hasattr(log['timestamp'], 'strftime'):
                    timestamp_str = log['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                else:
                    timestamp_str = str(log['timestamp'])
            
            writer.writerow([
                log.get('user_id', ''),
                username,
                log.get('query_type', ''),
                log.get('query', ''),
                log.get('results_count', 0),
                timestamp_str
            ])
        
        log_csv.seek(0)
        await query.message.reply_document(
            document=io.BytesIO(log_csv.getvalue().encode()),
            filename=f"logs_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            caption="📑 **Logs Export**\n\nAll search logs exported successfully."
        )
        
        await query.edit_message_text(
            f"✅ **Search Logs Export Completed**\n\n{len(logs):,} search logs exported successfully!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"Logs export error: {e}")
        await query.edit_message_text(
            "❌ **Search Logs Export Failed**\n\nAn error occurred during logs export. Please try again.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_export")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )

    return config.ADMIN_PANEL

# Add these new admin functions

async def admin_add_user_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for adding new user"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "add_user"
    
    await query.edit_message_text(
        "➕ **Add New User**\n\n"
        "Please send user details in this format:\n"
        "`user_id credits expiry_days`\n\n"
        "Examples:\n"
        "`123456789 100 30`\n\n"
        "Expiry days: 1, 2, 7, 15, 30, or 'lifetime'",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_ADD_USER

async def admin_add_credits_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for adding credits"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "add_credits"
    
    await query.edit_message_text(
        "💳 **Add Credits**\n\n"
        "Please send in this format:\n"
        "`user_id credits expiry_days`\n\n"
        "Examples:\n"
        "`123456789 50 30`\n\n"
        "Expiry days: 1, 2, 7, 15, 30, or 'lifetime'",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_ADD_USER

async def admin_modify_credits_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for modifying credits"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "modify_credits"
    
    await query.edit_message_text(
        "✏️ **Modify Credits**\n\n"
        "Please send in this format:\n"
        "`user_id new_credits expiry_days`\n\n"
        "Example:\n"
        "`123456789 200 30`\n\n"
        "Expiry days: 1, 2, 7, 15, 30, or 'lifetime'",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_ADD_USER

async def admin_suspend_user_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for suspending user"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "suspend_user"
    
    await query.edit_message_text(
        "🚫 **Suspend User**\n\n"
        "Please send in this format:\n"
        "`user_id days`\n\n"
        "Examples:\n"
        "`123456789 7` (suspend for 7 days)\n"
        "`123456789 0` (permanent suspension)",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_unsuspend_user_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for unsuspending user"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "unsuspend_user"
    
    await query.edit_message_text(
        "✅ **Unsuspend User**\n\n"
        "Please send the user ID:\n"
        "`user_id`\n\n"
        "Example: `123456789`",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_remove_user_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for removing user"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "remove_user"
    
    await query.edit_message_text(
        "🗑️ **Remove User**\n\n"
        "⚠️ **WARNING: This will permanently delete the user!**\n\n"
        "Please send the user ID:\n"
        "`user_id`\n\n"
        "Example: `123456789`",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_set_cost_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for setting search cost"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "set_cost"
    current_cost = db_utils.get_setting("search_credit_cost", 1)
    
    await query.edit_message_text(
        f"💵 **Set Search Cost**\n\n"
        f"Current cost: {current_cost} credits per search\n\n"
        "Please send the new cost:\n"
        "`credits_per_search`\n\n"
        "Example: `2`",
        reply_markup=InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_pricing"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_SET_PRICE

async def admin_edit_pricing_text_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for editing pricing text"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "edit_pricing_text"
    
    current_text = db_utils.get_setting("pricing_text", "Each search costs 1 credit.\nContact admin to recharge.")
    
    await query.edit_message_text(
        f"✏️ **Edit Pricing Text**\n\n"
        f"Current text:\n{current_text}\n\n"
        "Please send the new pricing text:",
        reply_markup=InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_pricing"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_edit_prestart_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for editing pre-start message"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "edit_prestart"
    
    current_msg = db_utils.get_setting("prestart_message", "👋 Welcome! Please click /start to begin.")
    
    await query.edit_message_text(
        f"✏️ **Edit Pre-Start Message**\n\n"
        f"Current message:\n{current_msg}\n\n"
        "Please send the new pre-start message:",
        reply_markup=InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_messages"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_edit_start_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for editing start message"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "edit_start"
    
    current_msg = db_utils.get_setting("start_message", "👮 Welcome to Law Enforcement Bot!")
    
    await query.edit_message_text(
        f"✏️ **Edit Start Message**\n\n"
        f"Current message:\n{current_msg}\n\n"
        "Please send the new start message:",
        reply_markup=InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_messages"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_channel_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Channel settings submenu"""
    query = update.callback_query
    await query.answer()
    
    current_text = db_utils.get_setting("join_channel_text", "📢 Join Channel")
    current_link = db_utils.get_setting("join_channel_link", "https://t.me/your_channel_here")
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("✏️ Change Button Text", callback_data="admin_change_channel_text"),
            InlineKeyboardButton("🔗 Change Channel Link", callback_data="admin_change_channel_link")
        ],
        [
            InlineKeyboardButton("⬅️ Back", callback_data="admin_messages"),
            InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
        ]
    ])
    
    text = (
        f"📢 **Channel Settings**\n\n"
        f"**Current Button Text:** {current_text}\n"
        f"**Current Channel Link:** {current_link}\n\n"
        "Choose what to edit:"
    )
    
    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_change_channel_text_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for changing channel button text"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "change_channel_text"
    
    current_text = db_utils.get_setting("join_channel_text", "📢 Join Channel")
    
    await query.edit_message_text(
        f"✏️ **Change Channel Button Text**\n\n"
        f"Current text: {current_text}\n\n"
        "Please send the new button text:",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_edit_channel")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_change_channel_link_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for changing channel link"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "change_channel_link"
    
    current_link = db_utils.get_setting("join_channel_link", "https://t.me/your_channel_here")
    
    await query.edit_message_text(
        f"🔗 **Change Channel Link**\n\n"
        f"Current link: {current_link}\n\n"
        "Please send the new channel link:",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_edit_channel")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_set_expiry_days_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for setting expiry warning days"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "set_expiry_days"
    
    current_days = db_utils.get_setting("expiry_notification_days", 3)
    
    await query.edit_message_text(
        f"⏰ **Set Expiry Warning Days**\n\n"
        f"Current warning: {current_days} days before expiry\n\n"
        "Please send the number of days:\n"
        "`days`\n\n"
        "Example: `5`",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_notifications")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_view_user_logs_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for viewing user logs"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "view_user_logs"
    
    await query.edit_message_text(
        "📊 **View User Logs**\n\n"
        "Please send the user ID:\n"
        "`user_id`\n\n"
        "Example: `123456789`",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_logs")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def handle_admin_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle admin text input"""
    text = update.message.text.strip()
    current_state = context.user_data.get("admin_state")
    
    if not current_state:
        await update.message.reply_text("❌ No active admin operation.")
        return config.ADMIN_PANEL
    
    try:
        if current_state == "add_user":
            parts = text.split()
            if len(parts) < 3:
                await update.message.reply_text("❌ Invalid format. Use: `user_id credits expiry_days`")
                return config.ADMIN_ADD_USER
            
            user_id, credits, expiry_input = int(parts[0]), int(parts[1]), parts[2]
            
            if expiry_input.lower() == "lifetime":
                expiry_days = 365 * 100
            else:
                expiry_days = int(expiry_input)
            
            success, message = db_utils.add_user(user_id, f"user_{user_id}", credits, expiry_days)
            await update.message.reply_text(f"{'✅' if success else '❌'} {message}")
            
        elif current_state == "add_credits":
            parts = text.split()
            if len(parts) < 3:
                await update.message.reply_text("❌ Invalid format. Use: `user_id credits expiry_days`")
                return config.ADMIN_ADD_USER
            
            user_id, credits, expiry_input = int(parts[0]), int(parts[1]), parts[2]
            
            if expiry_input.lower() == "lifetime":
                expiry_days = 365 * 100
            else:
                expiry_days = int(expiry_input)
            
            target_user = db_utils.get_user(user_id)
            
            if target_user:
                success = db_utils.add_credits(user_id, credits, expiry_days)
                if success:
                    new_user = db_utils.get_user(user_id)
                    await update.message.reply_text(f"✅ Added {credits} credits to user {user_id}")
                    
                    if db_utils.get_setting("credit_recharge_notifications", True):
                        try:
                            await context.bot.send_message(
                                chat_id=user_id,
                                text=f"💳 **Credit Recharge Successful!**\n\n✅ {credits} credits added to your account\n💎 Total credits: {new_user.get('credits', 0)}\n📅 Extended validity: {expiry_days} days",
                                parse_mode=ParseMode.MARKDOWN
                            )
                        except Exception as e:
                            logger.error(f"Failed to send credit notification: {e}")
                else:
                    await update.message.reply_text("❌ Failed to add credits.")
            else:
                await update.message.reply_text("❌ User not found.")
                
        elif current_state == "modify_credits":
            parts = text.split()
            if len(parts) < 3:
                await update.message.reply_text("❌ Invalid format. Use: `user_id new_credits expiry_days`")
                return config.ADMIN_ADD_USER
            
            user_id, credits, expiry_input = int(parts[0]), int(parts[1]), parts[2]
            
            if expiry_input.lower() == "lifetime":
                expiry_days = 365 * 100
            else:
                expiry_days = int(expiry_input)
            
            success = db_utils.modify_credits(user_id, credits, expiry_days)
            if success:
                await update.message.reply_text(f"✅ Set credits to {credits} for user {user_id}")
            else:
                await update.message.reply_text("❌ User not found.")
                
        elif current_state == "suspend_user":
            parts = text.split()
            if len(parts) != 2:
                await update.message.reply_text("❌ Invalid format. Use: `user_id days`")
                return config.ADMIN_EDIT_MESSAGES
            
            user_id, days = int(parts[0]), int(parts[1])
            success = db_utils.suspend_user(user_id, days if days > 0 else None)
            if success:
                await update.message.reply_text(f"✅ User {user_id} suspended {'permanently' if days == 0 else f'for {days} days'}")
            else:
                await update.message.reply_text("❌ Failed to suspend user.")
                
        elif current_state == "unsuspend_user":
            user_id = int(text)
            success = db_utils.unsuspend_user(user_id)
            if success:
                await update.message.reply_text(f"✅ User {user_id} unsuspended")
            else:
                await update.message.reply_text("❌ Failed to unsuspend user.")
                
        elif current_state == "remove_user":
            if text.startswith("@"):
                username = text[1:]
                success = db_utils.remove_user_by_username(username)
                identifier = f"@{username}"
            else:
                user_id = int(text)
                if user_id == config.OWNER_ID:
                    await update.message.reply_text("❌ Cannot remove owner.")
                    return config.ADMIN_EDIT_MESSAGES
                success = db_utils.remove_user(user_id)
                identifier = str(user_id)
            
            if success:
                await update.message.reply_text(f"✅ Removed user {identifier}")
            else:
                await update.message.reply_text("❌ User not found.")
                
        elif current_state == "view_user_logs":
            user_id = int(text)
            logs = db_utils.get_user_logs(user_id, limit=20)
            if logs:
                log_lines = []
                for log in logs:
                    timestamp = log["timestamp"].strftime("%Y-%m-%d %H:%M")
                    query_type = log["query_type"].title()
                    query = log["query"]
                    results = log.get("results_count", 0)
                    log_lines.append(f"🔍 **{query_type}:** `{query}`\n⏰ {timestamp} | 📊 {results} results")
                
                text_msg = f"📊 **User {user_id} Search Logs**\n\n" + "\n\n".join(log_lines[:10])
                await update.message.reply_text(text_msg, parse_mode=ParseMode.MARKDOWN)
            else:
                await update.message.reply_text(f"❌ No logs found for user {user_id}")
                
        elif current_state == "set_cost":
            cost = int(text)
            if cost < 0:
                await update.message.reply_text("❌ Cost cannot be negative.")
                return config.ADMIN_SET_PRICE
            db_utils.set_setting("search_credit_cost", cost)
            await update.message.reply_text(f"✅ Search cost set to {cost} credits")
            
        elif current_state == "edit_pricing_text":
            db_utils.set_setting("pricing_text", text)
            await update.message.reply_text("✅ Pricing text updated")
            
        elif current_state == "edit_prestart":
            db_utils.set_setting("prestart_message", text)
            await update.message.reply_text("✅ Pre-start message updated")
            
        elif current_state == "edit_start":
            db_utils.set_setting("start_message", text)
            await update.message.reply_text("✅ Start message updated")
            
        elif current_state == "change_channel_link":
            db_utils.set_setting("join_channel_link", text)
            await update.message.reply_text("✅ Channel link updated")
            
        elif current_state == "change_channel_text":
            db_utils.set_setting("join_channel_text", text)
            await update.message.reply_text("✅ Channel button text updated")
            
        elif current_state == "set_expiry_days":
            days = int(text)
            if days < 1:
                await update.message.reply_text("❌ Days must be at least 1.")
                return config.ADMIN_EDIT_MESSAGES
            db_utils.set_setting("expiry_notification_days", days)
            await update.message.reply_text(f"✅ Expiry warning set to {days} days")
            
        elif current_state == "set_trial_settings":
            credits = int(text)
            if credits < 1:
                await update.message.reply_text("❌ Credits must be at least 1.")
                return config.ADMIN_EDIT_MESSAGES
            
            db_utils.set_setting("trial_credits", credits)
            await update.message.reply_text(f"✅ Trial credits updated: {credits} credits")
            
        elif current_state == "edit_insufficient_credits":
            db_utils.set_setting("insufficient_credits_message", text)
            await update.message.reply_text("✅ Insufficient credits message updated")
            
        elif current_state == "set_daily_limit":
            limit = int(text)
            if limit < 1:
                await update.message.reply_text("❌ Limit must be at least 1.")
                return config.ADMIN_EDIT_MESSAGES
            db_utils.set_setting("daily_search_limit", limit)
            await update.message.reply_text(f"✅ Daily search limit set to {limit}")
            
        elif current_state == "set_minute_limit":
            limit = int(text)
            if limit < 1:
                await update.message.reply_text("❌ Limit must be at least 1.")
                return config.ADMIN_EDIT_MESSAGES
            db_utils.set_setting("per_minute_search_limit", limit)
            await update.message.reply_text(f"✅ Per-minute search limit set to {limit}")
            
        elif current_state == "edit_access_denied":
            db_utils.set_setting("access_denied_message", text)
            await update.message.reply_text("✅ Access denied message updated")
            
        elif current_state == "edit_restart":
            db_utils.set_setting("bot_restart_message", text)
            await update.message.reply_text("✅ Bot restart message updated")
            
        elif current_state.startswith("broadcast_"):
            broadcast_type = current_state.split("_")[1]
            message_text = text
            
            if broadcast_type == "broadcast":
                users = db_utils.get_all_users()
            elif broadcast_type == "active":
                users = [u for u in db_utils.get_all_users() if db_utils.is_user_authorized(u)]
            elif broadcast_type == "premium":
                users = [u for u in db_utils.get_all_users() if db_utils.get_plan(u) == "Premium"]
            elif broadcast_type == "trial":
                users = [u for u in db_utils.get_all_users() if db_utils.get_plan(u) == "Trial"]
            
            # Send broadcast
            success_count = 0
            for target_user in users:
                try:
                    await context.bot.send_message(
                        chat_id=target_user["user_id"],
                        text=f"📢 **Announcement**\n\n{message_text}",
                        parse_mode=ParseMode.MARKDOWN
                    )
                    success_count += 1
                except Exception as e:
                    logger.error(f"Failed to send broadcast to {target_user['user_id']}: {e}")
            
            await update.message.reply_text(f"✅ Announcement sent to {success_count}/{len(users)} users")

        elif current_state == "hide_record":
            parts = text.split(":")
            if len(parts) < 2:
                await update.message.reply_text("❌ Invalid format. Use: `field:value:reason`")
                return config.ADMIN_EDIT_MESSAGES

            field = parts[0].strip().lower()
            value = parts[1].strip()
            reason = parts[2].strip() if len(parts) > 2 else ""

            if field not in ["mobile", "id", "alt"]:
                await update.message.reply_text("❌ Invalid field. Use: mobile, id, or alt")
                return config.ADMIN_EDIT_MESSAGES

            success, message = db_utils.hide_record(field, value, update.effective_user.id, reason)
            await update.message.reply_text(f"{'✅' if success else '❌'} {message}")

        elif current_state == "unhide_record":
            parts = text.split(":")
            if len(parts) < 2:
                await update.message.reply_text("❌ Invalid format. Use: `field:value`")
                return config.ADMIN_EDIT_MESSAGES

            field = parts[0].strip().lower()
            value = parts[1].strip()

            if field not in ["mobile", "id", "alt"]:
                await update.message.reply_text("❌ Invalid field. Use: mobile, id, or alt")
                return config.ADMIN_EDIT_MESSAGES

            success, message = db_utils.unhide_record(field, value)
            await update.message.reply_text(f"{'✅' if success else '❌'} {message}")

        # Determine which submenu to return to based on the admin state
        admin_state = context.user_data.get("admin_state", "")
        context.user_data.pop("admin_state", None)

        # Send success message and provide navigation buttons
        success_msg = "✅ Settings updated successfully!"

        # Determine which menu to return to
        if admin_state in ["edit_start", "edit_prestart", "edit_insufficient_credits", "edit_access_denied", "edit_restart"]:
            success_msg += "\n\nUse the buttons below to navigate:"
            kb = InlineKeyboardMarkup([
                [InlineKeyboardButton("✏️ Message Settings", callback_data="admin_messages")],
                [InlineKeyboardButton("🏠 Admin Panel", callback_data="admin_panel")]
            ])
        elif admin_state in ["edit_pricing_text", "set_cost"]:
            success_msg += "\n\nUse the buttons below to navigate:"
            kb = InlineKeyboardMarkup([
                [InlineKeyboardButton("💰 Pricing Settings", callback_data="admin_pricing")],
                [InlineKeyboardButton("🏠 Admin Panel", callback_data="admin_panel")]
            ])
        elif admin_state in ["set_expiry_days"]:
            success_msg += "\n\nUse the buttons below to navigate:"
            kb = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔔 Notifications", callback_data="admin_notifications")],
                [InlineKeyboardButton("🏠 Admin Panel", callback_data="admin_panel")]
            ])
        else:
            kb = InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Admin Panel", callback_data="admin_panel")]
            ])

        await update.message.reply_text(success_msg, reply_markup=kb)
        return config.ADMIN_PANEL
        
    except ValueError:
        await update.message.reply_text("❌ Invalid input format.")
        return config.ADMIN_EDIT_MESSAGES
    except Exception as e:
        logger.error(f"Admin input error: {e}")
        await update.message.reply_text("❌ An error occurred.")
        return config.ADMIN_EDIT_MESSAGES

async def admin_broadcast_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Broadcast message settings"""
    query = update.callback_query
    await query.answer()
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("📢 Send Broadcast", callback_data="admin_send_broadcast"),
            InlineKeyboardButton("👥 Broadcast to Active", callback_data="admin_broadcast_active")
        ],
        [
            InlineKeyboardButton("💎 Broadcast to Premium", callback_data="admin_broadcast_premium"),
            InlineKeyboardButton("🆓 Broadcast to Trial", callback_data="admin_broadcast_trial")
        ],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
    ])
    
    await query.edit_message_text(
        "📢 **Broadcast Settings**\n\nChoose broadcast type:",
        reply_markup=kb,
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_PANEL

async def admin_rate_limiting_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Rate limiting settings"""
    query = update.callback_query
    await query.answer()
    
    current_daily_limit = db_utils.get_setting("daily_search_limit", 10)
    current_minute_limit = db_utils.get_setting("per_minute_search_limit", 5)
    rate_enabled = db_utils.get_setting("rate_limiting_enabled", True)
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("📅 Set Daily Limit", callback_data="admin_set_daily_limit"),
            InlineKeyboardButton("⏱️ Set Minute Limit", callback_data="admin_set_minute_limit")
        ],
        [
            InlineKeyboardButton(
                f"🔄 Rate Limiting ({'ON' if rate_enabled else 'OFF'})",
                callback_data="admin_toggle_rate_limiting"
            )
        ],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
    ])
    
    text = (
        f"⚡ **Rate Limiting Settings**\n\n"
        f"Daily search limit: {current_daily_limit} searches\n"
        f"Per-minute limit: {current_minute_limit} searches\n"
        f"Rate limiting: {'Enabled' if rate_enabled else 'Disabled'}"
    )
    
    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_access_control_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Access control settings"""
    query = update.callback_query
    await query.answer()
    
    open_access = db_utils.get_setting("open_access_enabled", False)
    trial_credits = db_utils.get_setting("trial_credits", 5)
    credit_notifications = db_utils.get_setting("credit_recharge_notifications", True)
    
    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton(
                f"🔓 Open Access ({'ON' if open_access else 'OFF'})",
                callback_data="admin_toggle_open_access"
            ),
            InlineKeyboardButton("🆓 Set Trial Credits", callback_data="admin_trial_settings")
        ],
        [
            InlineKeyboardButton(
                f"💳 Credit Notifications ({'ON' if credit_notifications else 'OFF'})",
                callback_data="admin_toggle_credit_notifications"
            )
        ],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
    ])
    
    text = (
        f"🔐 **Access Control Settings**\n\n"
        f"Open access: {'Enabled' if open_access else 'Disabled'}\n"
        f"Trial credits: {trial_credits}\n"
        f"Credit notifications: {'Enabled' if credit_notifications else 'Disabled'}"
    )
    
    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_trial_settings_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Trial settings prompt"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "set_trial_settings"
    
    current_credits = db_utils.get_setting("trial_credits", 5)
    
    await query.edit_message_text(
        f"🆓 **Set Trial Settings**\n\n"
        f"Current trial credits: {current_credits}\n\n"
        "Please send the new trial credits:\n"
        "`credits`\n\n"
        "Example: `10`",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_access_control")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_broadcast_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Broadcast message prompt"""
    query = update.callback_query
    await query.answer()
    
    broadcast_type = query.data.split("_")[-1]  # all, active, premium, trial
    context.user_data["admin_state"] = f"broadcast_{broadcast_type}"
    
    type_text = {
        "broadcast": "All Users",
        "active": "Active Users Only",
        "premium": "Premium Users Only", 
        "trial": "Trial Users Only"
    }.get(broadcast_type, "All Users")
    
    await query.edit_message_text(
        f"📢 **Send Broadcast to {type_text}**\n\n"
        "Please send the message you want to broadcast:",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_broadcast")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_set_daily_limit_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set daily limit prompt"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "set_daily_limit"
    current_limit = db_utils.get_setting("daily_search_limit", 10)
    
    await query.edit_message_text(
        f"⚡ **Set Daily Search Limit**\n\n"
        f"Current limit: {current_limit} searches per day\n\n"
        "Please send the new daily limit:\n"
        "`number_of_searches`\n\n"
        "Example: `15`",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_rate_limiting")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_set_minute_limit_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Set per-minute limit prompt"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "set_minute_limit"
    current_limit = db_utils.get_setting("per_minute_search_limit", 5)
    
    await query.edit_message_text(
        f"⚡ **Set Per-Minute Search Limit**\n\n"
        f"Current limit: {current_limit} searches per minute\n\n"
        "Please send the new per-minute limit:\n"
        "`number_of_searches`\n\n"
        "Example: `3`",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_rate_limiting")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_edit_insufficient_credits_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for editing insufficient credits message"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "edit_insufficient_credits"
    
    current_text = db_utils.get_setting("insufficient_credits_message", "❌ **Insufficient Credits**\n\nYou don't have enough credits to perform this search.\nContact admin to recharge your account.")
    
    await query.edit_message_text(
        f"💳 **Edit Insufficient Credits Message**\n\n"
        f"Current message:\n{current_text}\n\n"
        "Please send the new message:",
        reply_markup=InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_messages"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_edit_access_denied_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for editing access denied message"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "edit_access_denied"
    
    current_text = db_utils.get_setting(
        "access_denied_message",
        "❌ **Access Denied**\n\nYou are not authorized to use this bot. Please contact the administrator to get access."
    )
    
    await query.edit_message_text(
        f"🚫 **Edit Access Denied Message**\n\n"
        f"Current message:\n{current_text}\n\n"
        "Please send the new message:",
        reply_markup=InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_messages"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_edit_restart_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for editing bot restart message"""
    query = update.callback_query
    await query.answer()
    
    context.user_data["admin_state"] = "edit_restart"
    
    current_msg = db_utils.get_setting("bot_restart_message", "🔄 **CyberNetra is Updated.**\n\nPlease use /start to continue.")
    
    await query.edit_message_text(
        f"🔄 **Edit Bot Restart Message**\n\n"
        f"Current message:\n{current_msg}\n\n"
        "Please send the new restart message:",
        reply_markup=InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data="admin_messages"),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_search_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Search menu settings"""
    query = update.callback_query
    await query.answer()
    
    mobile_enabled = db_utils.get_setting("mobile_search_enabled", True)
    aadhar_enabled = db_utils.get_setting("aadhar_search_enabled", True)
    alternate_enabled = db_utils.get_setting("alternate_search_enabled", True)
    vehicle_enabled = db_utils.get_setting("vehicle_search_enabled", True)

    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton(
                f"📱 Mobile Search ({'ON' if mobile_enabled else 'OFF'})",
                callback_data="admin_toggle_mobile_search"
            )
        ],
        [
            InlineKeyboardButton(
                f"🆔 Aadhar Search ({'ON' if aadhar_enabled else 'OFF'})",
                callback_data="admin_toggle_aadhar_search"
            )
        ],
        [
            InlineKeyboardButton(
                f"📞 Alternate Search ({'ON' if alternate_enabled else 'OFF'})",
                callback_data="admin_toggle_alternate_search"
            )
        ],
        [
            InlineKeyboardButton(
                f"🚗 Vehicle Search ({'ON' if vehicle_enabled else 'OFF'})",
                callback_data="admin_toggle_vehicle_search"
            )
        ],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
    ])

    text = (
        f"🔍 **Search Menu Settings**\n\n"
        f"Mobile Search: {'Enabled' if mobile_enabled else 'Disabled'}\n"
        f"Aadhar Search: {'Enabled' if aadhar_enabled else 'Disabled'}\n"
        f"Alternate Search: {'Enabled' if alternate_enabled else 'Disabled'}\n"
        f"Vehicle Search: {'Enabled' if vehicle_enabled else 'Disabled'}\n\n"
        "Toggle search options on/off:"
    )
    
    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

# Hidden Records Management Functions

async def admin_hidden_records_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Hidden records management menu"""
    query = update.callback_query
    await query.answer()

    hidden_count = db_utils.get_hidden_records_count()

    kb = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("➕ Hide Record", callback_data="admin_hide_record"),
            InlineKeyboardButton("👁 View Hidden", callback_data="admin_view_hidden")
        ],
        [
            InlineKeyboardButton("🔍 Search Hidden", callback_data="admin_search_hidden"),
            InlineKeyboardButton("👁‍🗨 Unhide Record", callback_data="admin_unhide_record")
        ],
        [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
    ])

    text = (
        f"🙈 **Hidden Records Management**\n\n"
        f"📊 Total Hidden Records: {hidden_count}\n\n"
        "Hidden records will not appear in search results for users.\n"
        "Only admins can see and manage hidden records.\n\n"
        "Choose an action:"
    )

    await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    return config.ADMIN_PANEL

async def admin_hide_record_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for hiding a record"""
    query = update.callback_query
    await query.answer()

    context.user_data["admin_state"] = "hide_record"

    await query.edit_message_text(
        "🙈 **Smart Hide Person**\n\n"
        "This will hide a complete person and all their associated records.\n\n"
        "**Format:** `field:value:reason`\n\n"
        "**Fields:** mobile, id, alt\n\n"
        "**Examples:**\n"
        "`mobile:9876543210:Privacy request`\n"
        "`id:123456789012:Admin friend`\n"
        "`alt:9123456789:Personal`\n\n"
        "**Smart Features:**\n"
        "• Finds all records with this identifier\n"
        "• Automatically hides associated IDs\n"
        "• Protects person's complete privacy\n"
        "• Preserves alternate search functionality\n\n"
        "**Note:** Reason is optional but recommended",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_hidden_records")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_unhide_record_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Prompt for unhiding a record"""
    query = update.callback_query
    await query.answer()

    context.user_data["admin_state"] = "unhide_record"

    await query.edit_message_text(
        "👁‍🗨 **Smart Unhide Person**\n\n"
        "This will unhide a complete person and all their associated records.\n\n"
        "**Format:** `field:value`\n\n"
        "**Fields:** mobile, id, alt\n\n"
        "**Examples:**\n"
        "`mobile:9876543210`\n"
        "`id:123456789012`\n"
        "`alt:9123456789`\n\n"
        "**Smart Features:**\n"
        "• Removes the original hidden entry\n"
        "• Automatically unhides all associated IDs\n"
        "• Restores complete person visibility\n"
        "• Makes all their records searchable again",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_hidden_records")]
        ]),
        parse_mode=ParseMode.MARKDOWN
    )
    return config.ADMIN_EDIT_MESSAGES

async def admin_view_hidden_records(update: Update, context: ContextTypes.DEFAULT_TYPE, page=0):
    """View hidden records with pagination"""
    query = update.callback_query
    if query:
        await query.answer()

    hidden_records_list = db_utils.get_hidden_records(limit=100)  # Get more for pagination

    if not hidden_records_list:
        text = "🙈 **Hidden Records**\n\n❌ No hidden records found."
        kb = InlineKeyboardMarkup([
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_hidden_records")]
        ])
    else:
        # Pagination
        start = page * config.LOGS_PER_PAGE
        page_records = hidden_records_list[start:start + config.LOGS_PER_PAGE]
        total_pages = (len(hidden_records_list) - 1) // config.LOGS_PER_PAGE + 1

        records_text = []
        for i, record in enumerate(page_records, start=start + 1):
            hidden_at = record.get('hidden_at', datetime.now(timezone.utc))
            if isinstance(hidden_at, str):
                hidden_at = datetime.fromisoformat(hidden_at.replace('Z', '+00:00'))

            reason = record.get('reason', 'No reason provided')
            hide_type = record.get('hide_type', 'original')

            # Show different formatting for original vs associated records
            if hide_type == "original":
                # Count associated records
                linked_to = f"{record['field']}:{record['value']}"
                associated_count = db_utils.hidden_records.count_documents({"linked_to": linked_to})

                records_text.append(
                    f"**{i}.** 👤 {record['field'].upper()}: `{record['value']}`\n"
                    f"   📅 Hidden: {hidden_at.strftime('%Y-%m-%d %H:%M')}\n"
                    f"   📝 Reason: {reason[:40]}{'...' if len(reason) > 40 else ''}\n"
                    f"   🔗 Associated: {associated_count} ID(s) also hidden"
                )
            else:
                linked_to = record.get('linked_to', 'Unknown')
                records_text.append(
                    f"**{i}.** 🔗 {record['field'].upper()}: `{record['value']}`\n"
                    f"   📅 Hidden: {hidden_at.strftime('%Y-%m-%d %H:%M')}\n"
                    f"   🔗 Linked to: {linked_to}\n"
                    f"   📝 Auto-hidden (associated record)"
                )

        text = (
            f"🙈 **Hidden Records** (Page {page + 1}/{total_pages})\n\n"
            + "\n\n".join(records_text) +
            f"\n\n📊 Total: {len(hidden_records_list)} hidden records"
        )

        # Pagination buttons
        nav_buttons = []
        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ Prev", callback_data=f"admin_hidden_page:{page-1}"))
        if page < total_pages - 1:
            nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"admin_hidden_page:{page+1}"))

        kb_rows = []
        if nav_buttons:
            kb_rows.append(nav_buttons)
        kb_rows.append([InlineKeyboardButton("⬅️ Back", callback_data="admin_hidden_records")])
        kb = InlineKeyboardMarkup(kb_rows)

    if query:
        await query.edit_message_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)
    else:
        await update.message.reply_text(text, reply_markup=kb, parse_mode=ParseMode.MARKDOWN)

    return config.ADMIN_PANEL

# Hidden Records Command Handlers

async def hiderecord_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Hide record command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return

    if len(context.args) < 2:
        await update.message.reply_text(
            "**Smart Hide Person Usage:**\n"
            "/hiderecord <field> <value> [reason]\n\n"
            "**Fields:** mobile, id, alt\n"
            "**Example:** /hiderecord mobile 9876543210 Privacy request\n\n"
            "**Smart Features:**\n"
            "• Finds all records with this identifier\n"
            "• Automatically hides associated IDs\n"
            "• Protects complete person privacy",
            parse_mode=ParseMode.MARKDOWN
        )
        return

    try:
        field = context.args[0].lower()
        value = context.args[1]
        reason = " ".join(context.args[2:]) if len(context.args) > 2 else ""

        if field not in ["mobile", "id", "alt"]:
            await update.message.reply_text("❌ Invalid field. Use: mobile, id, or alt")
            return

        success, message = db_utils.hide_record(field, value, update.effective_user.id, reason)
        await update.message.reply_text(f"{'✅' if success else '❌'} {message}")

    except Exception as e:
        logger.error(f"Error hiding record: {e}")
        await update.message.reply_text("❌ Error hiding record.")

async def unhiderecord_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Unhide record command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return

    if len(context.args) < 2:
        await update.message.reply_text(
            "**Smart Unhide Person Usage:**\n"
            "/unhiderecord <field> <value>\n\n"
            "**Fields:** mobile, id, alt\n"
            "**Example:** /unhiderecord mobile 9876543210\n\n"
            "**Smart Features:**\n"
            "• Removes original hidden entry\n"
            "• Automatically unhides all associated IDs\n"
            "• Restores complete person visibility",
            parse_mode=ParseMode.MARKDOWN
        )
        return

    try:
        field = context.args[0].lower()
        value = context.args[1]

        if field not in ["mobile", "id", "alt"]:
            await update.message.reply_text("❌ Invalid field. Use: mobile, id, or alt")
            return

        success, message = db_utils.unhide_record(field, value)
        await update.message.reply_text(f"{'✅' if success else '❌'} {message}")

    except Exception as e:
        logger.error(f"Error unhiding record: {e}")
        await update.message.reply_text("❌ Error unhiding record.")

async def listhidden_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """List hidden records command"""
    user = db_utils.get_user(update.effective_user.id)
    if not user or not user.get("is_admin"):
        await update.message.reply_text("❌ Not authorized.")
        return

    try:
        hidden_records_list = db_utils.get_hidden_records(limit=20)  # Limit for message size

        if not hidden_records_list:
            await update.message.reply_text("🙈 No hidden records found.")
            return

        records_text = []
        for i, record in enumerate(hidden_records_list, 1):
            hidden_at = record.get('hidden_at', datetime.now(timezone.utc))
            if isinstance(hidden_at, str):
                hidden_at = datetime.fromisoformat(hidden_at.replace('Z', '+00:00'))

            reason = record.get('reason', 'No reason')
            hide_type = record.get('hide_type', 'original')

            if hide_type == "original":
                # Count associated records for original entries
                linked_to = f"{record['field']}:{record['value']}"
                associated_count = db_utils.hidden_records.count_documents({"linked_to": linked_to})

                records_text.append(
                    f"{i}. 👤 {record['field'].upper()}: `{record['value']}`\n"
                    f"   📅 {hidden_at.strftime('%Y-%m-%d %H:%M')}\n"
                    f"   📝 {reason[:25]}{'...' if len(reason) > 25 else ''}\n"
                    f"   🔗 +{associated_count} ID(s)"
                )
            else:
                linked_to = record.get('linked_to', 'Unknown')
                records_text.append(
                    f"{i}. 🔗 {record['field'].upper()}: `{record['value']}`\n"
                    f"   📅 {hidden_at.strftime('%Y-%m-%d %H:%M')}\n"
                    f"   🔗 Linked to: {linked_to}"
                )

        text = f"🙈 **Hidden Records** (Showing {len(records_text)})\n\n" + "\n\n".join(records_text)

        if len(hidden_records_list) == 20:
            text += f"\n\n💡 Use admin panel to view all records"

        await update.message.reply_text(text, parse_mode=ParseMode.MARKDOWN)

    except Exception as e:
        logger.error(f"Error listing hidden records: {e}")
        await update.message.reply_text("❌ Error retrieving hidden records.")


