# handlers/search/base_search.py
"""
Universal Base Search Handler
Provides consistent search result display across all search types
"""
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.base import BaseHandler

logger = logging.getLogger(__name__)

class BaseSearchHandler(BaseHandler):
    """Universal base class for all search handlers"""
    
    def __init__(self):
        super().__init__()
    
    async def show_search_success_message(self, update: Update, user: dict, credits_charged: int, context: ContextTypes.DEFAULT_TYPE = None):
        """Show separate success message with credit info - UNIVERSAL"""
        remaining_credits = user.get("credits", 0) - credits_charged

        if credits_charged > 0:
            success_text = (
                f"✅ **Search Completed**\n\n"
                f"💳 Used {credits_charged} credit(s)\n"
                f"💎 Credits remaining: {remaining_credits}"
            )
        elif self.auth_service.is_lea_officer(user):
            success_text = (
                f"✅ **Search Completed**\n\n"
                f"🏛️ LEA Officer - Unlimited Access\n"
                f"💎 Credits: Unlimited"
            )
        else:
            success_text = (
                f"✅ **Search Completed**\n\n"
                f"👑 Admin Access - No Charge\n"
                f"💎 Credits: Unlimited"
            )

        # Send separate success message and store message ID for later deletion
        success_message = await update.message.reply_text(
            success_text,
            parse_mode=ParseMode.MARKDOWN
        )

        # Store success message ID in context for deletion when user interacts with buttons
        if context:
            context.user_data["success_message_id"] = success_message.message_id
            context.user_data["success_chat_id"] = success_message.chat_id

    async def delete_success_message(self, context: ContextTypes.DEFAULT_TYPE):
        """Delete the search success message when user interacts with buttons"""
        try:
            success_message_id = context.user_data.get("success_message_id")
            success_chat_id = context.user_data.get("success_chat_id")

            if success_message_id and success_chat_id:
                await context.bot.delete_message(
                    chat_id=success_chat_id,
                    message_id=success_message_id
                )
                # Clear the stored message IDs
                context.user_data.pop("success_message_id", None)
                context.user_data.pop("success_chat_id", None)
                logger.info(f"Deleted success message {success_message_id}")
        except Exception as e:
            # Message might already be deleted or not exist
            logger.debug(f"Could not delete success message: {e}")
            # Clear the stored message IDs anyway
            context.user_data.pop("success_message_id", None)
            context.user_data.pop("success_chat_id", None)

    async def show_search_action_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE, search_type: str, total_results: int):
        """Show action buttons as separate message - UNIVERSAL for all search types"""
        # Get pagination info from context
        current_page = context.user_data.get("current_page", 0)
        total_pages = context.user_data.get("total_pages", 1)
        
        buttons = []
        
        # Pagination buttons (only if multiple pages)
        if total_pages > 1:
            nav_buttons = []
            if current_page > 0:
                nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"{search_type}_page:{current_page-1}"))
            
            nav_buttons.append(InlineKeyboardButton(f"{current_page+1}/{total_pages}", callback_data="page_info"))
            
            if current_page < total_pages - 1:
                nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"{search_type}_page:{current_page+1}"))
            
            buttons.append(nav_buttons)
        
        # Action buttons row
        action_buttons = []
        action_buttons.append(InlineKeyboardButton("🔍 Search Again", callback_data=f"do_search_{search_type}"))
        action_buttons.append(InlineKeyboardButton("🔙 Back", callback_data="search_menu"))
        buttons.append(action_buttons)
        
        # Main menu button
        buttons.append([InlineKeyboardButton("🏠 Main Menu", callback_data="main_menu")])
        
        keyboard = InlineKeyboardMarkup(buttons)
        
        # Send action buttons as separate message
        await update.message.reply_text(
            f"🎛️ **Search Actions** ({total_results} results found)",
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

    async def show_paginated_results_universal(self, context: ContextTypes.DEFAULT_TYPE, update: Update, results: list, page: int, search_type: str, result_title: str = "results"):
        """Show ONLY search results - no buttons (separate message) - UNIVERSAL"""
        # PAGINATION FIX: Clear any old results message IDs from previous searches
        context.user_data.pop("results_message_id", None)
        context.user_data.pop("results_chat_id", None)

        # Format results with pagination
        text, total_pages = self.pagination_service.format_search_results(results, page)

        # Send results as separate message WITHOUT any buttons
        results_message = await update.message.reply_text(
            f"📊 **Found {len(results)} {result_title}** (Page {page+1}/{total_pages})\n\n{text}",
            parse_mode=ParseMode.MARKDOWN
        )

        # CRITICAL FIX: Store results message ID for pagination editing
        context.user_data["results_message_id"] = results_message.message_id
        context.user_data["results_chat_id"] = results_message.chat_id

        # Store pagination info for button handler
        context.user_data["current_page"] = page
        context.user_data["total_pages"] = total_pages

    async def handle_pagination_universal(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page: int, search_type: str, results_key: str):
        """Handle pagination - Updates results and buttons separately - UNIVERSAL"""
        user = await self.check_user_authorization(update, context)
        if not user:
            from config.constants import MENU
            return MENU

        results = context.user_data.get(results_key)
        if not results:
            await update.callback_query.answer("Session expired. Please search again.")
            return await self.show_main_menu(update, context)

        await update.callback_query.answer()

        # Delete success message when user interacts with pagination
        await self.delete_success_message(context)

        # Format results with pagination
        text, total_pages = self.pagination_service.format_search_results(results, page)

        # Update pagination info in context
        context.user_data["current_page"] = page
        context.user_data["total_pages"] = total_pages

        # CRITICAL FIX: Edit existing results message instead of creating new one
        results_message_id = context.user_data.get("results_message_id")
        results_chat_id = context.user_data.get("results_chat_id")

        if results_message_id and results_chat_id:
            try:
                # Edit the existing results message
                await context.bot.edit_message_text(
                    chat_id=results_chat_id,
                    message_id=results_message_id,
                    text=f"📊 **Found {len(results)} results** (Page {page+1}/{total_pages})\n\n{text}",
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e:
                # Fallback: If editing fails, create new message
                logger.warning(f"Could not edit results message: {e}")
                new_message = await update.callback_query.message.reply_text(
                    f"📊 **Found {len(results)} results** (Page {page+1}/{total_pages})\n\n{text}",
                    parse_mode=ParseMode.MARKDOWN
                )
                # Update stored message ID
                context.user_data["results_message_id"] = new_message.message_id
                context.user_data["results_chat_id"] = new_message.chat_id
        else:
            # No stored message ID, create new message
            new_message = await update.callback_query.message.reply_text(
                f"📊 **Found {len(results)} results** (Page {page+1}/{total_pages})\n\n{text}",
                parse_mode=ParseMode.MARKDOWN
            )
            # Store message ID for future pagination
            context.user_data["results_message_id"] = new_message.message_id
            context.user_data["results_chat_id"] = new_message.chat_id
        
        # Update the button message with new pagination
        buttons = []
        
        # Pagination buttons
        if total_pages > 1:
            nav_buttons = []
            if page > 0:
                nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"{search_type}_page:{page-1}"))
            
            nav_buttons.append(InlineKeyboardButton(f"{page+1}/{total_pages}", callback_data="page_info"))
            
            if page < total_pages - 1:
                nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"{search_type}_page:{page+1}"))
            
            buttons.append(nav_buttons)
        
        # Action buttons
        action_buttons = []
        action_buttons.append(InlineKeyboardButton("🔍 Search Again", callback_data=f"do_search_{search_type}"))
        action_buttons.append(InlineKeyboardButton("🔙 Back", callback_data="search_menu"))
        buttons.append(action_buttons)
        
        buttons.append([InlineKeyboardButton("🏠 Main Menu", callback_data="main_menu")])
        
        keyboard = InlineKeyboardMarkup(buttons)
        
        # Update the button message
        await update.callback_query.edit_message_text(
            f"🎛️ **Search Actions** ({len(results)} results found)",
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        self.log_user_action(user["user_id"], f"{search_type}_search_pagination", f"page_{page}")
        from config.constants import MENU
        return MENU

    async def show_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main menu (imported from start handler)"""
        from handlers.start import StartHandler
        start_handler = StartHandler()
        return await start_handler.show_main_menu(update, context)
