# db_utils.py
from pymongo import MongoClient
from datetime import datetime, timezone, timedelta
import config
import logging

logger = logging.getLogger(__name__)

client = MongoClient(config.MONGO_URI)
db = client[config.DB_NAME]

# Collections
userdata = db["userdata"]
raj_data = db["raj_data"]  # Add new collection
users = db["users"]
settings = db["settings"]
search_logs = db["search_logs"]
notifications = db["notifications"]
hidden_records = db["hidden_records"]  # New collection for hidden records
vehicledata = db["vehicledata"]  # New collection for vehicle database

def normalize_datetime(dt):
    """Normalize datetime to UTC timezone"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt

def get_setting(key, default=None):
    """Get a setting from database"""
    doc = settings.find_one({"_id": "config"})
    if not doc:
        return default
    return doc.get(key, default)

def set_setting(key, value):
    """Set a setting in database"""
    settings.update_one(
        {"_id": "config"}, 
        {"$set": {key: value}}, 
        upsert=True
    )

def init_defaults():
    """Initialize default settings and create indexes"""
    default_config = {
        "_id": "config",
        "pricing_button_enabled": True,
        "pricing_button_text": "💲 Pricing",
        "pricing_text": "Each search costs 1 credit.\nContact admin to recharge.",
        "start_message": "👮 Welcome to Law Enforcement Bot!",
        "join_channel_text": "📢 Join Channel",
        "join_channel_link": "https://t.me/your_channel_here",
        "search_credit_cost": 1,
        "notifications_enabled": True,
        "expiry_notification_days": 3,
        "renewal_notification_enabled": True,
        "open_access_enabled": False,
        "trial_credits": 5,
        "daily_search_limit": 10,
        "per_minute_search_limit": 5,
        "rate_limiting_enabled": True,
        "credit_recharge_notifications": True,
        "insufficient_credits_message": "❌ **Insufficient Credits**\n\nYou don't have enough credits to perform this search.\nContact admin to recharge your account.",
        "access_denied_message": "❌ **Access Denied**\n\nYou are not authorized to use this bot. Please contact the administrator to get access.",
        "bot_restart_message": "🔄 **CyberNetra is Updated.**\n\nPlease use /start to continue.",
        "mobile_search_enabled": True,
        "aadhar_search_enabled": True,
        "alternate_search_enabled": True,
        "vehicle_search_enabled": True,
    }
    
    if settings.count_documents({"_id": "config"}) == 0:
        settings.insert_one(default_config)

    # Create indexes
    userdata.create_index("mobile", background=True)
    userdata.create_index("id", background=True)
    userdata.create_index("alt", background=True)
    users.create_index("user_id", unique=True)
    search_logs.create_index("user_id")
    search_logs.create_index("timestamp")
    notifications.create_index("user_id")
    notifications.create_index("timestamp")
    hidden_records.create_index([("field", 1), ("value", 1)], unique=True)
    hidden_records.create_index("hidden_by")
    hidden_records.create_index("hidden_at")
    vehicledata.create_index("vehicle_number", unique=True)  # For vehicle searches
    vehicledata.create_index("search_count")  # For analytics
    vehicledata.create_index("last_searched")  # For sorting

    # Create admin user
    admin = users.find_one({"user_id": config.OWNER_ID})
    if not admin:
        users.insert_one({
            "user_id": config.OWNER_ID,
            "username": "Owner",
            "credits": 9999999,
            "expiry": datetime.now(timezone.utc) + timedelta(days=365*100),
            "is_admin": True,
            "is_suspended": False,
            "suspended_until": None,
            "plan": "Admin",
            "search_limit_daily": None,
            "search_count_today": 0,
            "last_search_day": None,
            "created_at": datetime.now(timezone.utc),
        })
    else:
        if not admin.get("is_admin", False):
            users.update_one({"user_id": config.OWNER_ID}, {"$set": {"is_admin": True}})

def get_user(user_id):
    """Get user by user_id"""
    return users.find_one({"user_id": user_id})

def get_user_by_username(username):
    """Get user by username"""
    return users.find_one({"username": username})

def create_user_if_missing(user_id, username):
    """Create user if missing, considering open access setting"""
    user = get_user(user_id)
    if user:
        # Update username if it has changed and is not generic
        current_username = user.get('username', '')
        if username and not username.startswith('user_') and current_username != username:
            users.update_one(
                {"user_id": user_id}, 
                {"$set": {"username": username}}
            )
            user['username'] = username
        return user
    
    # Check if open access is enabled
    open_access = get_setting("open_access_enabled", False)
    if not open_access:
        return None  # User not authorized
    
    # Create new trial user
    trial_credits = get_setting("trial_credits", 5)
    
    doc = {
        "user_id": user_id,
        "username": username or f"user_{user_id}",
        "credits": trial_credits,
        "expiry": datetime.now(timezone.utc) + timedelta(days=7),  # 7 days trial
        "is_admin": False,
        "is_suspended": False,
        "suspended_until": None,
        "plan": "Trial",
        "search_limit_daily": None,
        "search_count_today": 0,
        "last_search_day": None,
        "created_at": datetime.now(timezone.utc),
    }
    users.insert_one(doc)
    return doc

def add_user(user_id, username, credits=5, expiry_days=30, plan="Free"):
    """Add new user (admin function)"""
    if get_user(user_id):
        return False, "User already exists"
    
    doc = {
        "user_id": user_id,
        "username": username or "unknown",
        "credits": credits,
        "expiry": datetime.now(timezone.utc) + timedelta(days=expiry_days),
        "is_admin": False,
        "is_suspended": False,
        "suspended_until": None,
        "plan": plan,
        "search_limit_daily": None,
        "search_count_today": 0,
        "last_search_day": None,
        "created_at": datetime.now(timezone.utc),
    }
    users.insert_one(doc)
    return True, "User added successfully"

def update_user_credits(user_id, credits, operation="set"):
    """Update user credits (set or add)"""
    if operation == "set":
        result = users.update_one({"user_id": user_id}, {"$set": {"credits": credits}})
    else:  # add
        result = users.update_one({"user_id": user_id}, {"$inc": {"credits": credits}})
    return result.modified_count > 0

def suspend_user(user_id, days=None):
    """Suspend a user"""
    update_data = {"is_suspended": True}
    if days:
        suspend_until = datetime.now(timezone.utc) + timedelta(days=days)
        update_data["suspended_until"] = suspend_until
    else:
        update_data["suspended_until"] = None
    
    result = users.update_one({"user_id": user_id}, {"$set": update_data})
    return result.modified_count > 0

def unsuspend_user(user_id):
    """Unsuspend a user"""
    result = users.update_one(
        {"user_id": user_id}, 
        {"$set": {"is_suspended": False, "suspended_until": None}}
    )
    return result.modified_count > 0

def remove_user(user_id):
    """Remove a user"""
    result = users.delete_one({"user_id": user_id})
    return result.deleted_count > 0

def add_credits(user_id, credits, expiry_days=None):
    """Add credits to existing user"""
    user = get_user(user_id)
    if not user:
        return False
    
    update_data = {"$inc": {"credits": credits}}
    
    if expiry_days:
        new_expiry = datetime.now(timezone.utc) + timedelta(days=expiry_days)
        update_data["$set"] = {"expiry": new_expiry}
    
    result = users.update_one({"user_id": user_id}, update_data)
    return result.modified_count > 0

def modify_credits(user_id, credits, expiry_days=None):
    """Set user credits to specific amount"""
    user = get_user(user_id)
    if not user:
        return False
    
    update_data = {"$set": {"credits": credits}}
    
    if expiry_days:
        new_expiry = datetime.now(timezone.utc) + timedelta(days=expiry_days)
        update_data["$set"]["expiry"] = new_expiry
    
    result = users.update_one({"user_id": user_id}, update_data)
    return result.modified_count > 0

def remove_user_by_username(username):
    """Remove user by username"""
    result = users.delete_one({"username": username})
    return result.deleted_count > 0

def increment_search_count(user):
    """Increment daily search count"""
    today = datetime.now(timezone.utc).date()
    users.update_one(
        {"user_id": user["user_id"]}, 
        {
            "$set": {"last_search_day": today.isoformat()},
            "$inc": {"search_count_today": 1}
        }
    )

def check_rate_limit(user):
    """Check if user has exceeded rate limits"""
    # Check daily limit
    daily_limit = get_setting("daily_search_limit", 10)
    today = datetime.now(timezone.utc).date()
    
    if user.get("last_search_day") != today.isoformat():
        users.update_one(
            {"user_id": user["user_id"]}, 
            {"$set": {"search_count_today": 0, "last_search_day": today.isoformat()}}
        )
        user["search_count_today"] = 0
    
    if user.get("search_count_today", 0) >= daily_limit:
        return False, "daily"
    
    # Check per-minute limit
    minute_limit = get_setting("per_minute_search_limit", 5)
    now = datetime.now(timezone.utc)
    last_search = normalize_datetime(user.get("last_search_time"))
    
    if last_search and (now - last_search).total_seconds() < 60:
        minute_count = user.get("searches_this_minute", 0)
        if minute_count >= minute_limit:
            return False, "minute"
    else:
        users.update_one(
            {"user_id": user["user_id"]}, 
            {"$set": {"searches_this_minute": 0, "last_search_time": now}}
        )
    
    return True, None

def update_search_time(user_id):
    """Update last search time and minute counter"""
    now = datetime.now(timezone.utc)
    users.update_one(
        {"user_id": user_id}, 
        {
            "$set": {"last_search_time": now},
            "$inc": {"searches_this_minute": 1}
        }
    )

def is_user_authorized(user):
    """Check if user is authorized to use the bot"""
    if not user:
        return False
    
    # Check suspension
    if user.get("is_suspended", False):
        suspend_until = normalize_datetime(user.get("suspended_until"))
        if suspend_until and suspend_until > datetime.now(timezone.utc):
            return False
        else:
            # Auto-unsuspend if time expired
            users.update_one(
                {"user_id": user["user_id"]}, 
                {"$set": {"is_suspended": False, "suspended_until": None}}
            )

    # Check credits
    if user.get("credits", 0) <= 0 and not user.get("is_admin", False):
        return False
    
    # Check expiry
    expiry = normalize_datetime(user.get("expiry"))
    if expiry < datetime.now(timezone.utc) and not user.get("is_admin", False):
        return False
    
    # Check daily limit
    limit = user.get("search_limit_daily")
    if limit is not None:
        today = datetime.now(timezone.utc).date()
        if user.get("last_search_day") != today.isoformat():
            users.update_one(
                {"user_id": user["user_id"]}, 
                {"$set": {"search_count_today": 0, "last_search_day": today.isoformat()}}
            )
        elif user.get("search_count_today", 0) >= limit:
            return False
    
    return True

def get_plan(user):
    """Get user plan status"""
    if user.get("is_admin"):
        return "Admin"
    
    expiry = normalize_datetime(user.get("expiry"))
    if expiry < datetime.now(timezone.utc) or user.get("credits", 0) <= 0:
        return "Expired"
    
    if user.get("credits", 0) > get_setting("trial_credits", 5):
        return "Premium"
    
    return "Trial"

def log_search(user_id, query_type, query, results_count=0):
    """Log search activity"""
    search_logs.insert_one({
        "user_id": user_id,
        "query_type": query_type,
        "query": query,
        "results_count": results_count,
        "timestamp": datetime.now(timezone.utc),
    })

def get_user_logs(user_id, limit=50):
    """Get user search logs"""
    return list(search_logs.find(
        {"user_id": user_id}
    ).sort("timestamp", -1).limit(limit))

def get_all_logs(limit=100):
    """Get all search logs"""
    return list(search_logs.find().sort("timestamp", -1).limit(limit))

def get_all_users():
    """Get all users"""
    return list(users.find({}))

def check_expiry_notifications():
    """Check and send expiry notifications"""
    notification_days = get_setting("expiry_notification_days", 3)
    cutoff_date = datetime.now(timezone.utc) + timedelta(days=notification_days)
    
    expiring_users = users.find({
        "expiry": {"$lt": cutoff_date, "$gt": datetime.now(timezone.utc)},
        "is_admin": False
    })
    
    return list(expiring_users)

def extend_user_expiry(user_id, days):
    """Extend user expiry by specified days"""
    user = get_user(user_id)
    if user:
        current_expiry = normalize_datetime(user.get("expiry"))
        if days == 365 * 100:  # lifetime
            new_expiry = datetime.now(timezone.utc) + timedelta(days=days)
        else:
            # Extend from current expiry or now, whichever is later
            base_date = max(current_expiry, datetime.now(timezone.utc))
            new_expiry = base_date + timedelta(days=days)
        
        users.update_one(
            {"user_id": user_id},
            {"$set": {"expiry": new_expiry}}
        )
        return True
    return False

def search_data(field, query):
    """Smart search with person-level hiding"""
    results = []

    # Step 1: Check if the search query itself is directly hidden
    if is_record_hidden(field, query):
        return results  # Return empty if query is directly hidden

    # Step 2: Perform the actual search
    userdata_results = list(userdata.find({field: query}))
    raj_results = list(raj_data.find({field: query}))
    all_results = userdata_results + raj_results

    # Step 3: Filter results based on smart hiding logic
    for record in all_results:
        if should_hide_record(record, field):
            continue  # Skip this record
        results.append(record)

    return results

def should_hide_record(record, search_field):
    """Check if a record should be hidden based on smart hiding rules"""
    # Check if record contains any hidden mobile numbers
    if record.get("mobile") and is_record_hidden("mobile", record["mobile"]):
        return True

    # Check if record contains any hidden IDs
    if record.get("id") and is_record_hidden("id", record["id"]):
        return True

    # Special case for alternate search: more permissive
    if search_field == "alt":
        # For alternate search, only hide if the record itself contains hidden values
        # Don't hide just because the search query matches a hidden value
        return False

    return False

def get_search_stats():
    """Get statistics from both collections"""
    userdata_count = userdata.count_documents({})
    raj_data_count = raj_data.count_documents({})
    hidden_count = hidden_records.count_documents({})

    return {
        "userdata_count": userdata_count,
        "raj_data_count": raj_data_count,
        "total_records": userdata_count + raj_data_count,
        "hidden_records_count": hidden_count
    }

# Hidden Records Management Functions

def hide_record(field, value, hidden_by_user_id, reason=""):
    """Smart hide: Hide a person by finding all their associated records and IDs"""
    try:
        # Step 1: Find all records containing this field:value
        all_records = []
        userdata_results = list(userdata.find({field: value}))

        # Handle raj_data field mapping
        if field == "alt":
            # For raj_data, alternate numbers are in 'AlternateNo' field
            raj_results = list(raj_data.find({"AlternateNo": value}))
        else:
            raj_results = list(raj_data.find({field: value}))

        all_records.extend(userdata_results)
        all_records.extend(raj_results)

        if not all_records:
            return False, f"No records found with {field}: {value}"

        # Step 2: Extract all unique IDs from found records
        associated_ids = set()
        for record in all_records:
            if record.get("id"):
                associated_ids.add(record["id"])

        # Step 3: Hide the original field:value
        original_doc = {
            "field": field,
            "value": value,
            "hidden_by": hidden_by_user_id,
            "hidden_at": datetime.now(timezone.utc),
            "reason": reason,
            "hide_type": "original"
        }

        try:
            hidden_records.insert_one(original_doc)
        except Exception as e:
            if "duplicate key" in str(e).lower():
                return False, f"{field.upper()}: {value} is already hidden"

        # Step 4: Hide all associated IDs
        hidden_ids = []
        for id_value in associated_ids:
            id_doc = {
                "field": "id",
                "value": id_value,
                "hidden_by": hidden_by_user_id,
                "hidden_at": datetime.now(timezone.utc),
                "reason": f"Associated with hidden {field}: {value}",
                "hide_type": "associated",
                "linked_to": f"{field}:{value}"
            }

            try:
                hidden_records.insert_one(id_doc)
                hidden_ids.append(id_value)
            except Exception as e:
                if "duplicate key" not in str(e).lower():
                    logger.error(f"Error hiding associated ID {id_value}: {e}")

        # Step 5: Create summary message
        message_parts = [f"✅ Hidden {field.upper()}: {value}"]
        if hidden_ids:
            message_parts.append(f"✅ Hidden {len(hidden_ids)} associated ID(s): {', '.join(hidden_ids[:3])}")
            if len(hidden_ids) > 3:
                message_parts.append(f"... and {len(hidden_ids) - 3} more")
        message_parts.append(f"📊 Total records affected: {len(all_records)}")

        return True, "\n".join(message_parts)

    except Exception as e:
        logger.error(f"Error in smart hide: {e}")
        return False, "Error hiding record"

def unhide_record(field, value):
    """Smart unhide: Remove person and all associated hidden records"""
    try:
        # Step 1: Find the original hidden record
        original_record = hidden_records.find_one({"field": field, "value": value, "hide_type": "original"})
        if not original_record:
            return False, f"{field.upper()}: {value} was not hidden"

        # Step 2: Find all associated hidden records
        linked_records = list(hidden_records.find({"linked_to": f"{field}:{value}"}))

        # Step 3: Delete the original record
        hidden_records.delete_one({"field": field, "value": value, "hide_type": "original"})

        # Step 4: Delete all associated records
        deleted_ids = []
        for linked_record in linked_records:
            hidden_records.delete_one({"_id": linked_record["_id"]})
            if linked_record["field"] == "id":
                deleted_ids.append(linked_record["value"])

        # Step 5: Create summary message
        message_parts = [f"✅ Unhidden {field.upper()}: {value}"]
        if deleted_ids:
            message_parts.append(f"✅ Unhidden {len(deleted_ids)} associated ID(s): {', '.join(deleted_ids[:3])}")
            if len(deleted_ids) > 3:
                message_parts.append(f"... and {len(deleted_ids) - 3} more")

        return True, "\n".join(message_parts)

    except Exception as e:
        logger.error(f"Error in smart unhide: {e}")
        return False, "Error unhiding record"

def is_record_hidden(field, value):
    """Check if a record is hidden"""
    return hidden_records.find_one({"field": field, "value": value}) is not None

def get_cached_vehicle(vehicle_number):
    """Get cached vehicle data from our database"""
    return vehicledata.find_one({"_id": vehicle_number})

def store_vehicle_data(vehicle_number, api_data, formatted_result, user_id):
    """Store vehicle data permanently in our database"""
    try:
        # Check if vehicle already exists
        existing = vehicledata.find_one({"_id": vehicle_number})

        if existing:
            # Update existing record
            update_data = {
                "$set": {
                    "last_searched": datetime.now(timezone.utc),
                    "formatted_result": formatted_result
                },
                "$inc": {"search_count": 1}
            }

            # Update API data if we got new data
            if api_data.get("rc_data"):
                update_data["$set"]["rc_data"] = api_data["rc_data"]
                update_data["$set"]["rc_updated_at"] = datetime.now(timezone.utc)

            if api_data.get("gtplay_data"):
                update_data["$set"]["gtplay_data"] = api_data["gtplay_data"]
                update_data["$set"]["gtplay_updated_at"] = datetime.now(timezone.utc)

            if api_data.get("challan_data"):
                update_data["$set"]["challan_data"] = api_data["challan_data"]
                update_data["$set"]["challan_updated_at"] = datetime.now(timezone.utc)

            if api_data.get("fastag_data"):
                update_data["$set"]["fastag_data"] = api_data["fastag_data"]
                update_data["$set"]["fastag_updated_at"] = datetime.now(timezone.utc)

            # Update data sources list
            current_sources = set(existing.get("data_sources", []))
            new_sources = set([k.replace("_data", "") for k, v in api_data.items() if v])
            all_sources = list(current_sources.union(new_sources))
            update_data["$set"]["data_sources"] = all_sources

            vehicledata.update_one({"_id": vehicle_number}, update_data)

        else:
            # Create new record
            vehicle_doc = {
                "_id": vehicle_number,
                "vehicle_number": vehicle_number,
                "rc_data": api_data.get("rc_data"),
                "gtplay_data": api_data.get("gtplay_data"),
                "challan_data": api_data.get("challan_data"),
                "fastag_data": api_data.get("fastag_data"),
                "first_searched": datetime.now(timezone.utc),
                "last_searched": datetime.now(timezone.utc),
                "search_count": 1,
                "created_by_user": user_id,
                "data_sources": [k.replace("_data", "") for k, v in api_data.items() if v],
                "formatted_result": formatted_result
            }

            # Add timestamps for each data source
            if api_data.get("rc_data"):
                vehicle_doc["rc_updated_at"] = datetime.now(timezone.utc)
            if api_data.get("gtplay_data"):
                vehicle_doc["gtplay_updated_at"] = datetime.now(timezone.utc)
            if api_data.get("challan_data"):
                vehicle_doc["challan_updated_at"] = datetime.now(timezone.utc)
            if api_data.get("fastag_data"):
                vehicle_doc["fastag_updated_at"] = datetime.now(timezone.utc)

            vehicledata.insert_one(vehicle_doc)

        return True

    except Exception as e:
        logger.error(f"Error storing vehicle data: {e}")
        return False

def update_vehicle_search_stats(vehicle_number):
    """Update search statistics for cached vehicle"""
    try:
        vehicledata.update_one(
            {"_id": vehicle_number},
            {
                "$set": {"last_searched": datetime.now(timezone.utc)},
                "$inc": {"search_count": 1}
            }
        )
        return True
    except Exception as e:
        logger.error(f"Error updating vehicle search stats: {e}")
        return False

def get_vehicle_database_stats():
    """Get statistics about our vehicle database"""
    try:
        total_vehicles = vehicledata.count_documents({})

        # Count by data sources
        rc_count = vehicledata.count_documents({"rc_data": {"$ne": None}})
        gtplay_count = vehicledata.count_documents({"gtplay_data": {"$ne": None}})
        challan_count = vehicledata.count_documents({"challan_data": {"$ne": None}})
        fastag_count = vehicledata.count_documents({"fastag_data": {"$ne": None}})

        # Most searched vehicles
        top_vehicles = list(vehicledata.find({}, {"vehicle_number": 1, "search_count": 1})
                           .sort("search_count", -1).limit(5))

        return {
            "total_vehicles": total_vehicles,
            "rc_data_count": rc_count,
            "gtplay_data_count": gtplay_count,
            "challan_data_count": challan_count,
            "fastag_data_count": fastag_count,
            "top_vehicles": top_vehicles
        }
    except Exception as e:
        logger.error(f"Error getting vehicle database stats: {e}")
        return None

def get_hidden_records(limit=50):
    """Get all hidden records, prioritizing original entries"""
    # Get original hidden records first, then associated ones
    original_records = list(hidden_records.find({"hide_type": "original"}).sort("hidden_at", -1).limit(limit))

    # If we need more records, get associated ones
    if len(original_records) < limit:
        remaining_limit = limit - len(original_records)
        associated_records = list(hidden_records.find({"hide_type": "associated"}).sort("hidden_at", -1).limit(remaining_limit))
        original_records.extend(associated_records)

    return original_records

def get_hidden_records_count():
    """Get count of hidden records"""
    return hidden_records.count_documents({})

def search_hidden_records(query=""):
    """Search hidden records by field or value"""
    if not query:
        return list(hidden_records.find().sort("hidden_at", -1))

    search_filter = {
        "$or": [
            {"field": {"$regex": query, "$options": "i"}},
            {"value": {"$regex": query, "$options": "i"}},
            {"reason": {"$regex": query, "$options": "i"}}
        ]
    }
    return list(hidden_records.find(search_filter).sort("hidden_at", -1))


