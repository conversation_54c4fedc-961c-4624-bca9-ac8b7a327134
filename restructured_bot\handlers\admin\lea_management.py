# handlers/admin/lea_management.py
import logging
from datetime import datetime, timedelta
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from handlers.base import <PERSON>Handler
from config.constants import ADMIN_PANEL, ADMIN_ADD_USER
from database.models import UserModel, SettingsModel
from services.formatting import FormattingService
from services.pagination import PaginationService

logger = logging.getLogger(__name__)

class LEAManagementHandler(BaseHandler):
    """Handler for LEA officer management functionality"""
    
    def __init__(self):
        super().__init__()
        self.user_model = UserModel()
        self.settings_model = SettingsModel()

    def sanitize_markdown(self, text):
        """Sanitize text for Markdown to prevent parsing errors"""
        if not text:
            return text
        # Escape common Markdown characters that might cause issues
        markdown_chars = ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
        sanitized = str(text)
        for char in markdown_chars:
            sanitized = sanitized.replace(char, f'\\{char}')
        return sanitized
    
    async def show_lea_plans_panel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show LEA plan management panel"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        # Get plan statistics
        lea_stats = self.user_model.get_user_statistics()
        
        # Get plan costs
        cost_30d = self.settings_model.get_lea_plan_cost("30D")
        cost_6m = self.settings_model.get_lea_plan_cost("6M")
        cost_1y = self.settings_model.get_lea_plan_cost("1Y")
        
        # Get active users per plan
        all_lea = self.user_model.get_lea_officers(active_only=True)
        plan_counts = {"30D": 0, "6M": 0, "1Y": 0}
        total_revenue = 0
        
        for officer in all_lea:
            lea_info = officer.get("lea_info", {})
            plan_duration = lea_info.get("plan_duration", "30D")
            plan_counts[plan_duration] += 1
            total_revenue += self.settings_model.get_lea_plan_cost(plan_duration)
        
        # Get expiring officers
        expiring_officers = self.user_model.get_expiring_lea_officers(3)
        
        text = (
            f"🏛️ **LEA Plan Management**\n\n"
            f"📅 **30-Day Plan**\n"
            f"├─ Cost: ₹{cost_30d:,}\n"
            f"├─ Active Users: {plan_counts['30D']}\n"
            f"└─ Revenue: ₹{plan_counts['30D'] * cost_30d:,}\n\n"
            f"📅 **6-Month Plan**\n"
            f"├─ Cost: ₹{cost_6m:,}\n"
            f"├─ Active Users: {plan_counts['6M']}\n"
            f"└─ Revenue: ₹{plan_counts['6M'] * cost_6m:,}\n\n"
            f"📅 **1-Year Plan**\n"
            f"├─ Cost: ₹{cost_1y:,}\n"
            f"├─ Active Users: {plan_counts['1Y']}\n"
            f"└─ Revenue: ₹{plan_counts['1Y'] * cost_1y:,}\n\n"
            f"📊 **Summary**\n"
            f"├─ Total LEA Officers: {lea_stats['active_lea_officers']}\n"
            f"├─ Expiring Soon (3 days): {len(expiring_officers)}\n"
            f"└─ Total Revenue: ₹{total_revenue:,}"
        )
        
        keyboard = self.keyboard_builder.admin_lea_plans_keyboard()
        
        try:
            await query.edit_message_text(
                text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            if "Message is not modified" in str(e):
                await query.answer("LEA panel refreshed")
            else:
                raise e
        
        self.log_admin_action(user["user_id"], "view_lea_plans")
        return ADMIN_PANEL
    
    async def add_lea_officer_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show LEA officer addition methods"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        text = (
            f"🏛️ **Add LEA Officer**\n\n"
            f"Choose your preferred method:\n\n"
            f"🚀 **Quick Method:**\n"
            f"• Direct User ID input\n"
            f"• Fast and simple\n\n"
            f"🧙‍♂️ **Wizard Method:**\n"
            f"• Step-by-step guidance\n"
            f"• User ID → Plan selection → Confirmation\n"
            f"• Recommended for new admins"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🚀 Quick Method", callback_data="lea_add_quick"),
                InlineKeyboardButton("🧙‍♂️ Wizard Method", callback_data="lea_add_wizard")
            ],
            [InlineKeyboardButton("🔙 Back to User Management", callback_data="admin_user_mgmt")]
        ])

        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

        return ADMIN_PANEL
    
    async def bulk_lea_assignment_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Prompt for bulk LEA assignment"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        context.user_data["admin_state"] = "bulk_lea_step1"
        
        text = self.message_templates.bulk_lea_assignment_prompt_message()
        keyboard = self.keyboard_builder.admin_back_keyboard("admin_user_mgmt")
        
        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )
        
        return ADMIN_ADD_USER
    
    async def handle_lea_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle LEA officer input"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        text = update.message.text.strip()
        current_state = context.user_data.get("admin_state")
        
        if not current_state:
            await update.message.reply_text("❌ Invalid operation")
            return ADMIN_PANEL
        
        try:
            if current_state == "add_lea_officer":
                # Parse LEA officer data: user_id|name|department|badge|contact|rank|plan
                parts = text.split('|')
                if len(parts) < 7:
                    await update.message.reply_text(
                        "❌ Invalid format. Use: user_id|name|department|badge|contact|rank|plan"
                    )
                    return ADMIN_ADD_USER
                
                user_id = int(parts[0])
                name = parts[1].strip()
                department = parts[2].strip()
                badge = parts[3].strip()
                contact = parts[4].strip()
                rank = parts[5].strip()
                plan = parts[6].strip().upper()
                
                if plan not in ["30D", "6M", "1Y"]:
                    await update.message.reply_text("❌ Invalid plan. Use: 30D, 6M, or 1Y")
                    return ADMIN_ADD_USER
                
                success, message = self.user_model.add_lea_officer(
                    user_id, name, department, badge, rank, contact, plan, True
                )
                
                await update.message.reply_text(f"{'✅' if success else '❌'} {message}")
                
                if success:
                    # Send enhanced welcome message to LEA officer
                    try:
                        welcome_msg = self.message_templates.lea_welcome_notification_message(
                            name, department, plan, user_id
                        )
                        await context.bot.send_message(
                            chat_id=user_id,
                            text=welcome_msg
                            # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                        )
                    except Exception as e:
                        logger.error(f"Failed to send welcome message to LEA {user_id}: {e}")

            elif current_state in ["set_lea_30d_cost", "set_lea_6m_cost", "set_lea_1y_cost"]:
                # Handle LEA plan cost setting
                try:
                    cost = int(text)
                    if cost < 0:
                        await update.message.reply_text("❌ Cost cannot be negative. Please enter a positive number.")
                        return ADMIN_ADD_USER

                    if current_state == "set_lea_30d_cost":
                        self.settings_model.set_setting("lea_30d_cost", cost)
                        await update.message.reply_text(f"✅ 30-Day LEA plan cost updated to ₹{cost:,}")
                        self.log_admin_action(user["user_id"], "update_lea_cost", f"30D plan -> ₹{cost}")
                        await self.show_lea_cost_input_again(update, context, "30d", cost)
                        return ADMIN_ADD_USER

                    elif current_state == "set_lea_6m_cost":
                        self.settings_model.set_setting("lea_6m_cost", cost)
                        await update.message.reply_text(f"✅ 6-Month LEA plan cost updated to ₹{cost:,}")
                        self.log_admin_action(user["user_id"], "update_lea_cost", f"6M plan -> ₹{cost}")
                        await self.show_lea_cost_input_again(update, context, "6m", cost)
                        return ADMIN_ADD_USER

                    elif current_state == "set_lea_1y_cost":
                        self.settings_model.set_setting("lea_1y_cost", cost)
                        await update.message.reply_text(f"✅ 1-Year LEA plan cost updated to ₹{cost:,}")
                        self.log_admin_action(user["user_id"], "update_lea_cost", f"1Y plan -> ₹{cost}")
                        await self.show_lea_cost_input_again(update, context, "1y", cost)
                        return ADMIN_ADD_USER

                except ValueError:
                    await update.message.reply_text("❌ Invalid cost. Please enter a valid number (digits only).")
                    return ADMIN_ADD_USER

            elif current_state == "lea_wizard_step1":
                # LEA Wizard Step 1: User ID input
                try:
                    user_id = int(text)

                    # Check if user exists
                    existing_user = self.user_model.get_user(user_id)

                    if existing_user:
                        # User exists - check if already LEA officer
                        if existing_user.get("user_type") == "lea_officer":
                            await update.message.reply_text("❌ User is already an LEA officer.")
                            return ADMIN_ADD_USER

                        # Store existing user info
                        context.user_data["lea_wizard"]["user_id"] = user_id
                        context.user_data["lea_wizard"]["username"] = existing_user.get("username", f"user_{user_id}")
                        context.user_data["lea_wizard"]["existing_user"] = True
                    else:
                        # User doesn't exist - will create new LEA officer
                        context.user_data["lea_wizard"]["user_id"] = user_id
                        context.user_data["lea_wizard"]["username"] = f"user_{user_id}"
                        context.user_data["lea_wizard"]["existing_user"] = False

                    context.user_data["admin_state"] = "lea_wizard_step2"

                    # Ask for officer name
                    user_status = "Existing User" if existing_user else "New User"
                    username_display = existing_user.get('username', f'user_{user_id}') if existing_user else f'user_{user_id}'

                    # Sanitize username to prevent Markdown parsing issues
                    safe_username = self.sanitize_markdown(username_display)

                    # Send Step 2 message without parse_mode to avoid Markdown parsing issues
                    await update.message.reply_text(
                        f"🧙‍♂️ **LEA Officer Wizard - Step 2/7**\n\n"
                        f"👤 **User:** {safe_username} ({user_id})\n"
                        f"📊 **Status:** {user_status}\n\n"
                        f"👮 **Enter Officer's Full Name:**\n\n"
                        f"Please provide the full name of the LEA officer.\n\n"
                        f"📝 **Example:** John Smith or Inspector John Doe",
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton("❌ Cancel Wizard", callback_data="admin_user_mgmt")]
                        ])
                    )

                    return ADMIN_ADD_USER

                except ValueError:
                    await update.message.reply_text("❌ Invalid user ID. Please enter a valid number.")
                    return ADMIN_ADD_USER

            elif current_state == "lea_wizard_step2":
                # LEA Wizard Step 2: Officer Name
                name = text.strip()
                if len(name) < 2:
                    await update.message.reply_text("❌ Name must be at least 2 characters long.")
                    return ADMIN_ADD_USER

                # Store name and move to step 3
                context.user_data["lea_wizard"]["name"] = name
                context.user_data["admin_state"] = "lea_wizard_step3"

                await update.message.reply_text(
                    f"🧙‍♂️ **LEA Officer Wizard - Step 3/7**\n\n"
                    f"👤 **Officer:** {name}\n\n"
                    f"🏛️ **Enter Department:**\n\n"
                    f"Please provide the department or organization.\n\n"
                    f"📝 **Examples:** Mumbai Police, CBI, Income Tax Department",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("❌ Cancel Wizard", callback_data="admin_user_mgmt")]
                    ])
                )
                return ADMIN_ADD_USER

            elif current_state == "lea_wizard_step3":
                # LEA Wizard Step 3: Department
                department = text.strip()
                if len(department) < 2:
                    await update.message.reply_text("❌ Department must be at least 2 characters long.")
                    return ADMIN_ADD_USER

                # Store department and move to step 4
                context.user_data["lea_wizard"]["department"] = department
                context.user_data["admin_state"] = "lea_wizard_step4"

                await update.message.reply_text(
                    f"🧙‍♂️ **LEA Officer Wizard - Step 4/7**\n\n"
                    f"👤 **Officer:** {context.user_data['lea_wizard']['name']}\n"
                    f"🏛️ **Department:** {department}\n\n"
                    f"🎖️ **Enter Badge Number:**\n\n"
                    f"Please provide the badge or ID number.\n\n"
                    f"📝 **Examples:** MP001, CBI123, IT456",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("❌ Cancel Wizard", callback_data="admin_user_mgmt")]
                    ])
                )
                return ADMIN_ADD_USER

            elif current_state == "lea_wizard_step4":
                # LEA Wizard Step 4: Badge Number
                badge = text.strip()
                if len(badge) < 2:
                    await update.message.reply_text("❌ Badge number must be at least 2 characters long.")
                    return ADMIN_ADD_USER

                # Store badge and move to step 5
                context.user_data["lea_wizard"]["badge"] = badge
                context.user_data["admin_state"] = "lea_wizard_step5"

                await update.message.reply_text(
                    f"🧙‍♂️ **LEA Officer Wizard - Step 5/7**\n\n"
                    f"👤 **Officer:** {context.user_data['lea_wizard']['name']}\n"
                    f"🏛️ **Department:** {context.user_data['lea_wizard']['department']}\n"
                    f"🎖️ **Badge:** {badge}\n\n"
                    f"📞 **Enter Contact Number:**\n\n"
                    f"Please provide contact number or email.\n\n"
                    f"📝 **Examples:** +919876543210, <EMAIL>",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("❌ Cancel Wizard", callback_data="admin_user_mgmt")]
                    ])
                )
                return ADMIN_ADD_USER

            elif current_state == "lea_wizard_step5":
                # LEA Wizard Step 5: Contact Number
                contact = text.strip()
                if len(contact) < 5:
                    await update.message.reply_text("❌ Contact must be at least 5 characters long.")
                    return ADMIN_ADD_USER

                # Store contact and move to step 6
                context.user_data["lea_wizard"]["contact"] = contact
                context.user_data["admin_state"] = "lea_wizard_step6"

                await update.message.reply_text(
                    f"🧙‍♂️ **LEA Officer Wizard - Step 6/7**\n\n"
                    f"👤 **Officer:** {context.user_data['lea_wizard']['name']}\n"
                    f"🏛️ **Department:** {context.user_data['lea_wizard']['department']}\n"
                    f"🎖️ **Badge:** {context.user_data['lea_wizard']['badge']}\n"
                    f"📞 **Contact:** {contact}\n\n"
                    f"👮 **Enter Rank/Position:**\n\n"
                    f"Please provide the officer's rank or position.\n\n"
                    f"📝 **Examples:** Inspector, Sub-Inspector, Officer, Senior Officer",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("❌ Cancel Wizard", callback_data="admin_user_mgmt")]
                    ])
                )
                return ADMIN_ADD_USER

            elif current_state == "lea_wizard_step6":
                # LEA Wizard Step 6: Rank
                rank = text.strip()
                if len(rank) < 2:
                    await update.message.reply_text("❌ Rank must be at least 2 characters long.")
                    return ADMIN_ADD_USER

                # Store rank and move to step 7 (plan selection)
                context.user_data["lea_wizard"]["rank"] = rank
                context.user_data["admin_state"] = "lea_wizard_step7"

                # CRITICAL: Ensure conversation state is set for callback routing
                context.user_data["conversation_state"] = "ADMIN_ADD_USER"

                # Show plan selection
                keyboard = self.keyboard_builder.plan_duration_keyboard()
                await update.message.reply_text(
                    f"🧙‍♂️ **LEA Officer Wizard - Step 7/7**\n\n"
                    f"👤 **Officer:** {context.user_data['lea_wizard']['name']}\n"
                    f"🏛️ **Department:** {context.user_data['lea_wizard']['department']}\n"
                    f"🎖️ **Badge:** {context.user_data['lea_wizard']['badge']}\n"
                    f"📞 **Contact:** {context.user_data['lea_wizard']['contact']}\n"
                    f"👮 **Rank:** {rank}\n\n"
                    f"📅 **Select Plan Duration:**",
                    reply_markup=keyboard
                )
                return ADMIN_ADD_USER

            elif current_state == "bulk_lea_step1":
                # Parse user IDs
                user_ids = [line.strip() for line in text.split('\n') if line.strip()]

                if not user_ids:
                    await update.message.reply_text("❌ No user IDs provided")
                    return ADMIN_ADD_USER

                # Validate user IDs
                valid_ids = []
                for uid in user_ids:
                    try:
                        valid_ids.append(int(uid))
                    except ValueError:
                        await update.message.reply_text(f"❌ Invalid user ID: {uid}")
                        return ADMIN_ADD_USER

                # Store for next step
                context.user_data["bulk_lea_ids"] = valid_ids
                context.user_data["admin_state"] = "bulk_lea_step2"

                # Ask for plan duration
                keyboard = self.keyboard_builder.plan_duration_keyboard()
                await update.message.reply_text(
                    f"📋 **Bulk LEA Assignment**\n\n"
                    f"Ready to add {len(valid_ids)} LEA officers.\n"
                    f"Select plan duration:",
                    reply_markup=keyboard
                )

                return ADMIN_ADD_USER
            
            # Clear admin state
            context.user_data.pop("admin_state", None)
            
        except ValueError as e:
            await update.message.reply_text(f"❌ Invalid input: {str(e)}")
        except Exception as e:
            logger.error(f"Error in LEA input handling: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
        
        return ADMIN_PANEL
    
    async def handle_bulk_lea_plan_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle LEA plan duration selection for both bulk and wizard"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        plan_duration = query.data.replace("plan_", "")

        # Check if this is LEA wizard or bulk assignment
        if context.user_data.get("admin_state") == "lea_wizard_step7":
            # LEA Wizard Step 7: Plan selected, move to confirmation
            lea_wizard = context.user_data.get("lea_wizard", {})
            user_id = lea_wizard.get("user_id")
            username = lea_wizard.get("username")

            if not user_id:
                await query.edit_message_text("❌ Session expired. Please start over.")
                return ADMIN_PANEL

            # Store plan and move to confirmation
            context.user_data["lea_wizard"]["plan"] = plan_duration
            context.user_data["admin_state"] = "lea_wizard_confirm"

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("✅ Confirm", callback_data="lea_wizard_confirm"),
                    InlineKeyboardButton("❌ Cancel", callback_data="admin_user_mgmt")
                ]
            ])

            # Get all collected details for confirmation
            wizard_data = context.user_data["lea_wizard"]

            # Sanitize all data to prevent Markdown parsing issues
            safe_username = self.sanitize_markdown(username)
            safe_name = self.sanitize_markdown(wizard_data.get('name', 'N/A'))
            safe_department = self.sanitize_markdown(wizard_data.get('department', 'N/A'))
            safe_badge = self.sanitize_markdown(wizard_data.get('badge', 'N/A'))
            safe_contact = self.sanitize_markdown(wizard_data.get('contact', 'N/A'))
            safe_rank = self.sanitize_markdown(wizard_data.get('rank', 'N/A'))

            await query.edit_message_text(
                f"🧙‍♂️ **LEA Officer Wizard - Final Confirmation**\n\n"
                f"👤 **User:** {safe_username} ({user_id})\n"
                f"👮 **Name:** {safe_name}\n"
                f"🏛️ **Department:** {safe_department}\n"
                f"🎖️ **Badge:** {safe_badge}\n"
                f"📞 **Contact:** {safe_contact}\n"
                f"👮 **Rank:** {safe_rank}\n"
                f"📅 **Plan:** {plan_duration}\n\n"
                f"✅ **Ready to create LEA officer with these details?**\n\n"
                f"This will give the user unlimited credits and LEA privileges.",
                reply_markup=keyboard
                # REMOVED parse_mode to prevent Markdown parsing errors
            )

            return ADMIN_ADD_USER

        else:
            # Bulk LEA assignment
            user_ids = context.user_data.get("bulk_lea_ids", [])

            if not user_ids:
                await query.edit_message_text("❌ No user IDs found. Please start over.")
                return ADMIN_PANEL

            # Ask about welcome messages
            context.user_data["bulk_lea_plan"] = plan_duration

            keyboard = self.keyboard_builder.bulk_lea_options_keyboard()
            await query.edit_message_text(
                f"📋 **Bulk LEA Assignment**\n\n"
                f"Plan: {plan_duration}\n"
                f"Users: {len(user_ids)}\n\n"
                f"Send welcome messages to all officers?",
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

            return ADMIN_ADD_USER
    
    async def execute_bulk_lea_assignment(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Execute bulk LEA assignment"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL
        
        query = update.callback_query
        await query.answer()
        
        send_welcome = query.data == "bulk_welcome_yes"
        user_ids = context.user_data.get("bulk_lea_ids", [])
        plan_duration = context.user_data.get("bulk_lea_plan", "30D")
        
        if not user_ids:
            await query.edit_message_text("❌ No user IDs found. Please start over.")
            return ADMIN_PANEL
        
        # Execute bulk assignment
        results = self.user_model.bulk_add_lea_officers(user_ids, plan_duration, send_welcome)
        
        success_count = sum(1 for r in results if r["success"])
        
        # Send enhanced welcome messages if requested
        if send_welcome:
            for result in results:
                if result["success"]:
                    try:
                        welcome_msg = self.message_templates.lea_welcome_notification_message(
                            f"Officer_{result['user_id']}", "Department_TBD", plan_duration, result['user_id']
                        )
                        await context.bot.send_message(
                            chat_id=result["user_id"],
                            text=welcome_msg
                            # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                        )
                    except Exception as e:
                        logger.error(f"Failed to send welcome to {result['user_id']}: {e}")
        
        # Clear context
        context.user_data.pop("bulk_lea_ids", None)
        context.user_data.pop("bulk_lea_plan", None)
        context.user_data.pop("admin_state", None)
        
        await query.edit_message_text(
            f"✅ **Bulk Assignment Complete**\n\n"
            f"Successfully added: {success_count}/{len(user_ids)} LEA officers\n"
            f"Plan: {plan_duration}\n"
            f"Welcome messages: {'Sent' if send_welcome else 'Not sent'}",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data="admin_user_mgmt")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
        
        self.log_admin_action(user["user_id"], "bulk_lea_assignment", f"{success_count} officers added")
        return ADMIN_PANEL

    async def handle_lea_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle LEA management callbacks"""
        query = update.callback_query
        data = query.data

        if data == "lea_add_quick":
            # Quick method - direct user ID input
            context.user_data["admin_state"] = "add_lea_officer"

            text = (
                f"🚀 **Quick LEA Addition**\n\n"
                f"Send the LEA officer details in this format:\n"
                f"`user_id|name|department|badge|contact|rank|plan`\n\n"
                f"**Example:**\n"
                f"`123456789|John Doe|Police Dept|B123|<EMAIL>|Inspector|30D`\n\n"
                f"**Plans:** 30D, 6M, 1Y"
            )

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="admin_user_mgmt")]
            ])

            await query.edit_message_text(
                text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_ADD_USER

        elif data == "lea_add_wizard":
            # Wizard method - step by step
            return await self.lea_add_wizard_start(update, context)

        elif data == "lea_wizard_confirm":
            # LEA Wizard confirmation
            return await self.lea_wizard_confirm(update, context)

        else:
            await query.answer("⚙️ Feature coming soon!")
            return await self.add_lea_officer_prompt(update, context)

    async def lea_add_wizard_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start LEA addition wizard"""
        query = update.callback_query
        await query.answer()

        context.user_data["admin_state"] = "lea_wizard_step1"
        context.user_data["lea_wizard"] = {}

        text = (
            f"🧙‍♂️ **LEA Officer Wizard - Step 1/7**\n\n"
            f"👤 **Enter User ID:**\n\n"
            f"Please send the Telegram User ID of the person you want to make an LEA officer.\n\n"
            f"💡 **Works for both:**\n"
            f"• ✅ **Existing users** (who used the bot before)\n"
            f"• ✅ **New users** (will be created as LEA officers)\n\n"
            f"💡 **How to get User ID:**\n"
            f"• Use @userinfobot to get any Telegram User ID\n"
            f"• Check user logs in admin panel for existing users\n\n"
            f"📝 **Example:** 123456789\n\n"
            f"📋 **Wizard will collect:** User ID → Name → Department → Badge → Contact → Rank → Plan"
        )

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("❌ Cancel Wizard", callback_data="admin_user_mgmt")]
        ])

        await query.edit_message_text(
            text,
            reply_markup=keyboard,
            parse_mode=ParseMode.MARKDOWN
        )

        return ADMIN_ADD_USER

    async def lea_wizard_confirm(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Confirm LEA wizard creation"""
        user = await self.check_admin_authorization(update, context)
        if not user:
            return ADMIN_PANEL

        query = update.callback_query
        await query.answer()

        lea_wizard = context.user_data.get("lea_wizard", {})
        user_id = lea_wizard.get("user_id")
        username = lea_wizard.get("username")
        name = lea_wizard.get("name")
        department = lea_wizard.get("department")
        badge = lea_wizard.get("badge")
        contact = lea_wizard.get("contact")
        rank = lea_wizard.get("rank")
        plan = lea_wizard.get("plan")

        if not all([user_id, username, name, department, badge, contact, rank, plan]):
            await query.edit_message_text("❌ Session expired or incomplete data. Please start over.")
            return ADMIN_PANEL

        # Create or convert LEA officer based on whether user exists
        existing_user = lea_wizard.get("existing_user", False)

        if existing_user:
            # Convert existing user to LEA officer
            success, message = self.user_model.convert_to_lea_officer(user_id)
            if success:
                # Update with detailed LEA info
                self.user_model.collection.update_one(
                    {"user_id": user_id},
                    {"$set": {
                        "lea_info.name": name,
                        "lea_info.department": department,
                        "lea_info.badge_number": badge,
                        "lea_info.rank": rank,
                        "lea_info.contact": contact,
                        "lea_info.plan_duration": plan,
                        "lea_info.created_via": "wizard",
                        "lea_info.created_by": user["user_id"]
                    }}
                )
        else:
            # Create new LEA officer (same as quick method)
            success, message = self.user_model.add_lea_officer(
                user_id, name, department, badge, rank, contact, plan, True
            )

        if success:
            # Send enhanced LEA officer notification with full details
            try:
                welcome_msg = self.message_templates.lea_welcome_notification_message(
                    name, department, plan, user_id
                )
                await context.bot.send_message(
                    chat_id=user_id,
                    text=welcome_msg
                    # CRITICAL FIX: Remove parse_mode to avoid Markdown parsing errors
                )
            except Exception as e:
                logger.error(f"Failed to send LEA notification to {user_id}: {e}")

            # Sanitize all data for success message
            safe_username = self.sanitize_markdown(username)
            safe_name = self.sanitize_markdown(name)
            safe_department = self.sanitize_markdown(department)
            safe_badge = self.sanitize_markdown(badge)
            safe_contact = self.sanitize_markdown(contact)
            safe_rank = self.sanitize_markdown(rank)

            await query.edit_message_text(
                f"✅ **LEA Officer Created Successfully!**\n\n"
                f"👤 **User:** {safe_username} ({user_id})\n"
                f"👮 **Name:** {safe_name}\n"
                f"🏛️ **Department:** {safe_department}\n"
                f"🎖️ **Badge:** {safe_badge}\n"
                f"📞 **Contact:** {safe_contact}\n"
                f"👮 **Rank:** {safe_rank}\n"
                f"📅 **Plan:** {plan}\n\n"
                f"The officer now has unlimited credits and LEA privileges."
                # REMOVED parse_mode to prevent Markdown parsing errors
            )
        else:
            await query.edit_message_text(f"❌ **Failed to create LEA officer**\n\n{message}")

        # Clear wizard data
        context.user_data.pop("lea_wizard", None)
        context.user_data.pop("admin_state", None)

        self.log_admin_action(user["user_id"], "lea_wizard_complete", f"Created LEA officer {user_id}")
        return ADMIN_PANEL

    # Missing LEA plan management methods
    async def manage_lea_30d_plan(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Manage 30-day LEA plan"""
        query = update.callback_query
        await query.answer()

        current_cost = self.settings_model.get_setting("lea_30d_cost", 500)
        enabled = self.settings_model.get_setting("lea_30d_enabled", True)

        text = (
            f"📅 **30-Day LEA Plan Management**\n\n"
            f"Status: {'✅ Enabled' if enabled else '❌ Disabled'}\n"
            f"Cost: ₹{current_cost}\n\n"
            f"Configure 30-day plan settings:"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{'❌ Disable' if enabled else '✅ Enable'} Plan", callback_data="toggle_lea_30d"),
                InlineKeyboardButton("💰 Set Cost", callback_data="set_lea_30d_cost")
            ],
            [InlineKeyboardButton("🔙 Back to LEA Plans", callback_data="admin_lea_plans")]
        ])

        await query.edit_message_text(text, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL

    async def manage_lea_6m_plan(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Manage 6-month LEA plan"""
        query = update.callback_query
        await query.answer()

        current_cost = self.settings_model.get_setting("lea_6m_cost", 2500)
        enabled = self.settings_model.get_setting("lea_6m_enabled", True)

        text = (
            f"📅 **6-Month LEA Plan Management**\n\n"
            f"Status: {'✅ Enabled' if enabled else '❌ Disabled'}\n"
            f"Cost: ₹{current_cost}\n\n"
            f"Configure 6-month plan settings:"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{'❌ Disable' if enabled else '✅ Enable'} Plan", callback_data="toggle_lea_6m"),
                InlineKeyboardButton("💰 Set Cost", callback_data="set_lea_6m_cost")
            ],
            [InlineKeyboardButton("🔙 Back to LEA Plans", callback_data="admin_lea_plans")]
        ])

        await query.edit_message_text(text, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL

    async def manage_lea_1y_plan(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Manage 1-year LEA plan"""
        query = update.callback_query
        await query.answer()

        current_cost = self.settings_model.get_setting("lea_1y_cost", 5000)
        enabled = self.settings_model.get_setting("lea_1y_enabled", True)

        text = (
            f"📅 **1-Year LEA Plan Management**\n\n"
            f"Status: {'✅ Enabled' if enabled else '❌ Disabled'}\n"
            f"Cost: ₹{current_cost}\n\n"
            f"Configure 1-year plan settings:"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{'❌ Disable' if enabled else '✅ Enable'} Plan", callback_data="toggle_lea_1y"),
                InlineKeyboardButton("💰 Set Cost", callback_data="set_lea_1y_cost")
            ],
            [InlineKeyboardButton("🔙 Back to LEA Plans", callback_data="admin_lea_plans")]
        ])

        await query.edit_message_text(text, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL

    async def show_lea_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show LEA general settings"""
        query = update.callback_query
        await query.answer()

        text = (
            f"🏛️ **LEA Officer Settings**\n\n"
            f"General LEA officer configuration:\n\n"
            f"• Unlimited credits: ✅ Enabled\n"
            f"• All features access: ✅ Enabled\n"
            f"• Priority support: ✅ Enabled"
        )

        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("👥 Manage Officers", callback_data="admin_user_mgmt"),
                InlineKeyboardButton("📊 LEA Statistics", callback_data="admin_lea_stats")
            ],
            [InlineKeyboardButton("🔙 Back to LEA Plans", callback_data="admin_lea_plans")]
        ])

        await query.edit_message_text(text, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL

    async def show_lea_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show LEA statistics"""
        query = update.callback_query
        await query.answer()

        # Get LEA statistics
        total_lea = len(self.user_model.get_lea_officers())
        active_lea = len(self.user_model.get_lea_officers(active_only=True))

        text = (
            f"📊 **LEA Officer Statistics**\n\n"
            f"👥 **Officers Overview:**\n"
            f"• Total LEA Officers: {total_lea}\n"
            f"• Active Officers: {active_lea}\n"
            f"• Inactive Officers: {total_lea - active_lea}\n\n"
            f"📈 **Quick Stats:**\n"
            f"• System Status: 🟢 Online\n"
            f"• All LEA features: ✅ Active"
        )

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 Back to LEA Settings", callback_data="admin_lea_settings")]
        ])

        await query.edit_message_text(text, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL

    async def show_expiring_lea_officers(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show LEA officers expiring soon"""
        query = update.callback_query
        await query.answer()

        from datetime import datetime, timedelta

        # Get LEA officers expiring in next 7 days
        expiring_officers = []
        lea_officers = self.user_model.get_lea_officers()

        for officer in lea_officers:
            expiry_date = officer.get('expiry_date')
            if expiry_date:
                try:
                    if isinstance(expiry_date, str):
                        expiry_date = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))

                    days_until_expiry = (expiry_date - datetime.now()).days
                    if 0 <= days_until_expiry <= 7:
                        expiring_officers.append({
                            'officer': officer,
                            'days_left': days_until_expiry
                        })
                except:
                    continue

        message = f"⏰ **LEA Officers Expiring Soon**\n\n"

        if expiring_officers:
            message += f"Found {len(expiring_officers)} officers expiring in next 7 days:\n\n"
            for item in expiring_officers:
                officer = item['officer']
                days_left = item['days_left']
                username = officer.get('username', 'Unknown')
                user_id = officer.get('user_id', 'Unknown')
                plan = officer.get('plan', 'Unknown')

                if days_left == 0:
                    status = "🔴 Expires Today"
                elif days_left == 1:
                    status = "🟡 Expires Tomorrow"
                else:
                    status = f"🟠 Expires in {days_left} days"

                message += f"• **{username}** ({user_id})\n"
                message += f"  Plan: {plan} | {status}\n\n"
        else:
            message += "✅ No LEA officers expiring in the next 7 days."

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 Back to LEA Plans", callback_data="admin_lea_plans")]
        ])

        await query.edit_message_text(message, reply_markup=keyboard, parse_mode=ParseMode.MARKDOWN)
        return ADMIN_PANEL

    async def handle_lea_toggle(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle LEA plan toggle callbacks"""
        query = update.callback_query
        data = query.data

        if data == "toggle_lea_30d":
            current = self.settings_model.get_setting("lea_30d_enabled", True)
            new_status = not current
            self.settings_model.set_setting("lea_30d_enabled", new_status)
            await query.answer(f"30D Plan {'✅ Enabled' if new_status else '❌ Disabled'}")
            return await self.manage_lea_30d_plan(update, context)

        elif data == "toggle_lea_6m":
            current = self.settings_model.get_setting("lea_6m_enabled", True)
            new_status = not current
            self.settings_model.set_setting("lea_6m_enabled", new_status)
            await query.answer(f"6M Plan {'✅ Enabled' if new_status else '❌ Disabled'}")
            return await self.manage_lea_6m_plan(update, context)

        elif data == "toggle_lea_1y":
            current = self.settings_model.get_setting("lea_1y_enabled", True)
            new_status = not current
            self.settings_model.set_setting("lea_1y_enabled", new_status)
            await query.answer(f"1Y Plan {'✅ Enabled' if new_status else '❌ Disabled'}")
            return await self.manage_lea_1y_plan(update, context)

        return ADMIN_PANEL

    async def show_lea_cost_input_again(self, update: Update, context: ContextTypes.DEFAULT_TYPE, plan_type: str, current_cost: int):
        """Show LEA cost input prompt again after successful update"""
        try:
            if plan_type == "30d":
                text = (
                    f"💰 **Set 30-Day LEA Plan Cost**\n\n"
                    f"Current cost: ₹{current_cost:,}\n\n"
                    f"Enter new cost (number only):\n"
                    f"Example: 500"
                )
                cancel_callback = "admin_lea_30d"
            elif plan_type == "6m":
                text = (
                    f"💰 **Set 6-Month LEA Plan Cost**\n\n"
                    f"Current cost: ₹{current_cost:,}\n\n"
                    f"Enter new cost (number only):\n"
                    f"Example: 2500"
                )
                cancel_callback = "admin_lea_6m"
            elif plan_type == "1y":
                text = (
                    f"💰 **Set 1-Year LEA Plan Cost**\n\n"
                    f"Current cost: ₹{current_cost:,}\n\n"
                    f"Enter new cost (number only):\n"
                    f"Example: 5000"
                )
                cancel_callback = "admin_lea_1y"
            else:
                return  # Unknown plan type

            # Create keyboard with navigation options
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("⬅️ Back", callback_data=cancel_callback),
                    InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
                ]
            ])

            # Send the input prompt again
            await update.message.reply_text(
                text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"Error showing LEA cost input again: {e}")
            # Fallback - clear context and return to admin panel
            context.user_data.pop("admin_state", None)

    async def handle_lea_cost_setting(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle LEA cost setting callbacks"""
        query = update.callback_query
        data = query.data

        if data == "set_lea_30d_cost":
            context.user_data["admin_state"] = "set_lea_30d_cost"
            current_cost = self.settings_model.get_setting("lea_30d_cost", 500)
            await query.edit_message_text(
                "💰 **Set 30-Day LEA Plan Cost**\n\n"
                f"Current cost: ₹{current_cost:,}\n\n"
                "Enter the new cost (number only):\n"
                "Example: 500",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Cancel", callback_data="admin_lea_30d")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_ADD_USER

        elif data == "set_lea_6m_cost":
            context.user_data["admin_state"] = "set_lea_6m_cost"
            current_cost = self.settings_model.get_setting("lea_6m_cost", 2500)
            await query.edit_message_text(
                "💰 **Set 6-Month LEA Plan Cost**\n\n"
                f"Current cost: ₹{current_cost:,}\n\n"
                "Enter the new cost (number only):\n"
                "Example: 2500",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Cancel", callback_data="admin_lea_6m")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_ADD_USER

        elif data == "set_lea_1y_cost":
            context.user_data["admin_state"] = "set_lea_1y_cost"
            current_cost = self.settings_model.get_setting("lea_1y_cost", 5000)
            await query.edit_message_text(
                "💰 **Set 1-Year LEA Plan Cost**\n\n"
                f"Current cost: ₹{current_cost:,}\n\n"
                "Enter the new cost (number only):\n"
                "Example: 5000",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("❌ Cancel", callback_data="admin_lea_1y")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return ADMIN_ADD_USER

        return ADMIN_PANEL
