# database/models/hidden.py
import logging
from datetime import datetime, timezone
from database.connection import get_collection
from config.constants import COLLECTIONS, FIELD_MAPPINGS

logger = logging.getLogger(__name__)

class HiddenRecordsModel:
    """Hidden records model for managing privacy controls"""
    
    def __init__(self):
        self.hidden_records = get_collection(COLLECTIONS['HIDDEN_RECORDS'])
        self.userdata = get_collection(COLLECTIONS['USERDATA'])
        self.raj_data = get_collection(COLLECTIONS['RAJ_DATA'])
    
    def hide_record(self, field, value, hidden_by_user_id, reason=""):
        """Smart hide: Hide a person by finding all their associated records and IDs"""
        try:
            # Step 1: Find all records containing this field:value
            all_records = []
            userdata_results = list(self.userdata.find({field: value}))

            # Handle raj_data search (match old bot search behavior exactly)
            raj_results = list(self.raj_data.find({field: value}))

            all_records.extend(userdata_results)
            all_records.extend(raj_results)

            if not all_records:
                return False, f"No records found with {field}: {value}"

            # Step 2: Extract all unique IDs from found records
            associated_ids = set()
            for record in all_records:
                if record.get("id"):
                    associated_ids.add(record["id"])

            # Step 3: Hide the original field:value
            original_doc = {
                "field": field,
                "value": value,
                "hidden_by": hidden_by_user_id,
                "hidden_at": datetime.now(timezone.utc),
                "reason": reason,
                "hide_type": "original"
            }

            try:
                self.hidden_records.insert_one(original_doc)
            except Exception as e:
                if "duplicate key" in str(e).lower():
                    return False, f"{field.upper()}: {value} is already hidden"
                raise

            # Step 4: Hide all associated IDs
            hidden_ids = []
            for id_value in associated_ids:
                id_doc = {
                    "field": "id",
                    "value": id_value,
                    "hidden_by": hidden_by_user_id,
                    "hidden_at": datetime.now(timezone.utc),
                    "reason": f"Associated with hidden {field}: {value}",
                    "hide_type": "associated",
                    "linked_to": f"{field}:{value}"
                }

                try:
                    self.hidden_records.insert_one(id_doc)
                    hidden_ids.append(id_value)
                except Exception as e:
                    if "duplicate key" not in str(e).lower():
                        logger.error(f"Error hiding associated ID {id_value}: {e}")

            # Step 5: Create summary message
            message_parts = [f"✅ Hidden {field.upper()}: {value}"]
            if hidden_ids:
                message_parts.append(f"✅ Hidden {len(hidden_ids)} associated ID(s): {', '.join(hidden_ids[:3])}")
                if len(hidden_ids) > 3:
                    message_parts.append(f"... and {len(hidden_ids) - 3} more")
            message_parts.append(f"📊 Total records affected: {len(all_records)}")

            logger.info(f"Smart hide completed for {field}:{value} by user {hidden_by_user_id}")
            return True, "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error in smart hide: {e}")
            return False, "Error hiding record"
    
    def unhide_record(self, field, value):
        """Smart unhide: Remove person and all associated hidden records"""
        try:
            # Step 1: Find the original hidden record
            original_record = self.hidden_records.find_one({
                "field": field, 
                "value": value, 
                "hide_type": "original"
            })
            if not original_record:
                return False, f"{field.upper()}: {value} was not hidden"

            # Step 2: Find all associated hidden records
            linked_records = list(self.hidden_records.find({
                "linked_to": f"{field}:{value}"
            }))

            # Step 3: Delete the original record
            self.hidden_records.delete_one({
                "field": field, 
                "value": value, 
                "hide_type": "original"
            })

            # Step 4: Delete all associated records
            deleted_ids = []
            for linked_record in linked_records:
                self.hidden_records.delete_one({"_id": linked_record["_id"]})
                if linked_record["field"] == "id":
                    deleted_ids.append(linked_record["value"])

            # Step 5: Create summary message
            message_parts = [f"✅ Unhidden {field.upper()}: {value}"]
            if deleted_ids:
                message_parts.append(f"✅ Unhidden {len(deleted_ids)} associated ID(s): {', '.join(deleted_ids[:3])}")
                if len(deleted_ids) > 3:
                    message_parts.append(f"... and {len(deleted_ids) - 3} more")

            logger.info(f"Smart unhide completed for {field}:{value}")
            return True, "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error in smart unhide: {e}")
            return False, "Error unhiding record"
    
    def is_record_hidden(self, field, value):
        """Check if a record is hidden"""
        return self.hidden_records.find_one({"field": field, "value": value}) is not None
    
    def get_hidden_records(self, limit=50):
        """Get all hidden records, prioritizing original entries"""
        # Get original hidden records first, then associated ones
        original_records = list(self.hidden_records.find({
            "hide_type": "original"
        }).sort("hidden_at", -1).limit(limit))

        # If we need more records, get associated ones
        if len(original_records) < limit:
            remaining_limit = limit - len(original_records)
            associated_records = list(self.hidden_records.find({
                "hide_type": "associated"
            }).sort("hidden_at", -1).limit(remaining_limit))
            original_records.extend(associated_records)

        return original_records
    
    def get_hidden_records_count(self):
        """Get count of hidden records"""
        return self.hidden_records.count_documents({})
    
    def search_hidden_records(self, query=""):
        """Search hidden records by field or value"""
        if not query:
            return list(self.hidden_records.find().sort("hidden_at", -1))

        search_filter = {
            "$or": [
                {"field": {"$regex": query, "$options": "i"}},
                {"value": {"$regex": query, "$options": "i"}},
                {"reason": {"$regex": query, "$options": "i"}}
            ]
        }
        return list(self.hidden_records.find(search_filter).sort("hidden_at", -1))
    
    def get_hidden_by_user(self, user_id):
        """Get records hidden by a specific user"""
        return list(self.hidden_records.find({
            "hidden_by": user_id
        }).sort("hidden_at", -1))
    
    def get_hidden_by_field(self, field):
        """Get hidden records by field type"""
        return list(self.hidden_records.find({
            "field": field
        }).sort("hidden_at", -1))
    
    def get_hidden_stats(self):
        """Get statistics about hidden records"""
        total_hidden = self.hidden_records.count_documents({})
        original_count = self.hidden_records.count_documents({"hide_type": "original"})
        associated_count = self.hidden_records.count_documents({"hide_type": "associated"})
        
        # Count by field type
        field_stats = {}
        pipeline = [
            {"$group": {"_id": "$field", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        for result in self.hidden_records.aggregate(pipeline):
            field_stats[result["_id"]] = result["count"]
        
        return {
            "total_hidden": total_hidden,
            "original_count": original_count,
            "associated_count": associated_count,
            "by_field": field_stats
        }
    
    def cleanup_orphaned_records(self):
        """Clean up associated records that have no original record"""
        # Find associated records
        associated_records = list(self.hidden_records.find({"hide_type": "associated"}))
        
        orphaned_count = 0
        for record in associated_records:
            linked_to = record.get("linked_to", "")
            if ":" in linked_to:
                field, value = linked_to.split(":", 1)
                # Check if original record exists
                original_exists = self.hidden_records.find_one({
                    "field": field,
                    "value": value,
                    "hide_type": "original"
                })
                
                if not original_exists:
                    # Remove orphaned associated record
                    self.hidden_records.delete_one({"_id": record["_id"]})
                    orphaned_count += 1
        
        if orphaned_count > 0:
            logger.info(f"Cleaned up {orphaned_count} orphaned hidden records")
        
        return orphaned_count
