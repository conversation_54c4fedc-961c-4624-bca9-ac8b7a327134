# services/pagination.py
import logging
from telegram import InlineKeyboardMarkup, InlineKeyboardButton
from config.settings import RESULTS_PER_PAGE, USERS_PER_PAGE, LOGS_PER_PAGE
from services.formatting import FormattingService

logger = logging.getLogger(__name__)

class PaginationService:
    """Service for handling pagination of results"""
    
    @staticmethod
    def paginate_results(data, page=0, per_page=RESULTS_PER_PAGE):
        """Paginate data and return page info"""
        start = page * per_page
        page_data = data[start: start + per_page]
        total_pages = (len(data) - 1) // per_page + 1 if data else 0
        
        return {
            'page_data': page_data,
            'current_page': page,
            'total_pages': total_pages,
            'total_items': len(data),
            'start_index': start,
            'end_index': min(start + per_page, len(data))
        }
    
    @staticmethod
    def format_search_results(data, page=0):
        """Format paginated search results"""
        pagination_info = PaginationService.paginate_results(data, page, RESULTS_PER_PAGE)
        page_data = pagination_info['page_data']
        
        if not page_data:
            return "❌ No results found.", 0
        
        results = []
        for idx, item in enumerate(page_data, start=pagination_info['start_index'] + 1):
            formatted_result = FormattingService.format_search_result(item)
            results.append(f"**📋 Result #{idx}**\n{formatted_result}")
        
        content = "\n\n" + "─" * 30 + "\n\n".join([""] + results) + "\n\n"
        footer = (f"📄 Page {pagination_info['current_page'] + 1} of {pagination_info['total_pages']} | "
                 f"Total: {pagination_info['total_items']} results")
        
        return content + footer, pagination_info['total_pages']
    
    @staticmethod
    def format_user_list(users, page=0):
        """Format paginated user list"""
        pagination_info = PaginationService.paginate_results(users, page, USERS_PER_PAGE)
        page_data = pagination_info['page_data']
        
        if not page_data:
            return "❌ No users found.", 0
        
        lines = [f"👥 **Users (Page {pagination_info['current_page'] + 1}/{pagination_info['total_pages']})**\n"]
        
        for idx, user in enumerate(page_data, start=pagination_info['start_index'] + 1):
            username = FormattingService.escape_markdown(user.get('username', 'Unknown'))
            user_id = user.get('user_id', 'Unknown')
            credits = user.get('credits', 0)
            plan = user.get('plan', 'Unknown')
            user_type = user.get('user_type', 'normal_user')

            # Fix credit status logic
            if user.get('is_suspended'):
                status = "🚫 Suspended"
            elif user_type == 'lea_officer':
                status = "🏛️ LEA Officer"
            elif user_type == 'admin':
                status = "👑 Admin"
            elif credits <= 0:
                status = "⏰ Expired"
            else:
                status = "✅ Active"

            # Show User ID, Username, Credits, Plan, Status
            lines.append(f"{idx}. **ID:** `{user_id}` | **{username}** | {credits} credits | {plan} | {status}")
        
        footer = f"\n📊 Total Users: {pagination_info['total_items']}"
        
        return "\n".join(lines) + footer, pagination_info['total_pages']
    
    @staticmethod
    def format_log_list(logs, page=0):
        """Format paginated log list"""
        pagination_info = PaginationService.paginate_results(logs, page, LOGS_PER_PAGE)
        page_data = pagination_info['page_data']
        
        if not page_data:
            return "❌ No search history found.", 0

        lines = [f"📑 **Search History (Page {pagination_info['current_page'] + 1}/{pagination_info['total_pages']})**\n"]

        for log_entry in page_data:
            formatted_log = FormattingService.format_log_entry(log_entry)
            lines.append(formatted_log)

        footer = f"\n📊 Total Searches: {pagination_info['total_items']}"
        
        return "\n".join(lines) + footer, pagination_info['total_pages']
    
    @staticmethod
    def create_pagination_keyboard(current_page, total_pages, callback_prefix, additional_buttons=None):
        """Create pagination keyboard"""
        buttons = []
        
        # Navigation buttons
        nav_buttons = []
        
        # Previous page button
        if current_page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"{callback_prefix}:{current_page - 1}"))
        
        # Page indicator
        nav_buttons.append(InlineKeyboardButton(f"{current_page + 1}/{total_pages}", callback_data="noop"))
        
        # Next page button
        if current_page < total_pages - 1:
            nav_buttons.append(InlineKeyboardButton("Next ➡️", callback_data=f"{callback_prefix}:{current_page + 1}"))
        
        if nav_buttons:
            buttons.append(nav_buttons)
        
        # Additional buttons (like Search Again, Back, etc.)
        if additional_buttons:
            if isinstance(additional_buttons[0], list):
                buttons.extend(additional_buttons)
            else:
                buttons.append(additional_buttons)
        
        return InlineKeyboardMarkup(buttons) if buttons else None
    
    @staticmethod
    def create_search_result_keyboard(current_page, total_pages, search_type):
        """Clean keyboard for search results without copy functionality"""
        additional_buttons = [
            [
                InlineKeyboardButton("🔍 Search Again", callback_data="search_again"),
                InlineKeyboardButton("⬅️ Back", callback_data="search_menu")
            ],
            [
                InlineKeyboardButton("🏠 Main Menu", callback_data="back_main")
            ]
        ]

        return PaginationService.create_pagination_keyboard(
            current_page,
            total_pages,
            f"{search_type}_page",
            additional_buttons
        )
    
    @staticmethod
    def create_admin_list_keyboard(current_page, total_pages, list_type, back_callback="admin_panel"):
        """Create keyboard for admin lists"""
        additional_buttons = [
            [
                InlineKeyboardButton("⬅️ Back", callback_data=back_callback),
                InlineKeyboardButton("🏠 Main Panel", callback_data="admin_panel")
            ]
        ]
        
        return PaginationService.create_pagination_keyboard(
            current_page,
            total_pages,
            f"{list_type}_page",
            additional_buttons
        )
    
    @staticmethod
    def get_page_from_callback(callback_data):
        """Extract page number from callback data"""
        try:
            if ":" in callback_data:
                return int(callback_data.split(":")[-1])
            return 0
        except (ValueError, IndexError):
            return 0
    
    @staticmethod
    def validate_page_number(page, total_pages):
        """Validate and normalize page number"""
        if page < 0:
            return 0
        if page >= total_pages:
            return max(0, total_pages - 1)
        return page
    
    @staticmethod
    def create_simple_navigation(back_callback, home_callback="back_main"):
        """Create simple back/home navigation"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data=back_callback),
                InlineKeyboardButton("🏠 Main Menu", callback_data=home_callback)
            ]
        ])
    
    @staticmethod
    def create_admin_navigation(back_callback, panel_callback="admin_panel"):
        """Create admin panel navigation"""
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("⬅️ Back", callback_data=back_callback),
                InlineKeyboardButton("🏠 Main Panel", callback_data=panel_callback)
            ]
        ])
    
    @staticmethod
    def chunk_list(data, chunk_size):
        """Split list into chunks of specified size"""
        for i in range(0, len(data), chunk_size):
            yield data[i:i + chunk_size]
    
    @staticmethod
    def get_pagination_info_text(current_page, total_pages, total_items, item_type="items"):
        """Get pagination information text"""
        if total_pages <= 1:
            return f"Total: {total_items} {item_type}"
        
        return f"Page {current_page + 1} of {total_pages} | Total: {total_items} {item_type}"
